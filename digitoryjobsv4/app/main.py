from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv
import os
from app.routers  import menu_mapping
from app.routers  import sub_recipe
from app.routers  import master_data
from app.routers  import print_data
from app.routers  import party_print
from app.routers  import llm
from app.routers  import smart_dashboard
from starlette.middleware.gzip import GZipMiddleware
from app.utility.chat_db import initialize_chat_tables
from app.utility.restaurant_db import initialize_restaurant_tables


load_dotenv()

# Initialize FastAPI app
app = FastAPI()

# Add CORS middleware
app.add_middleware(GZipMiddleware, minimum_size=500)  # Compress responses over 500 bytes
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security and authentication setup
security = HTTPBearer()
async def authenticate(credentials: HTTPAuthorizationCredentials = Depends(security)):
    if not credentials:
        raise HTTPException(status_code=401, detail="Not authenticated")

    expected_token = os.getenv("BEARER_TOKEN")
    if not expected_token or credentials.credentials != f"Bearer {expected_token}":
        raise HTTPException(status_code=401, detail="Invalid token")

    return credentials.credentials

# Include routers with appropriate prefixes and tags
app.include_router(menu_mapping.router, tags=['Menu_Mapping'], prefix='/menu_mapping')
app.include_router(sub_recipe.router, tags=['Sub_Recipe'], prefix='/sub_recipe')
app.include_router(master_data.router, tags=['Master Data'], prefix='/master_data')
app.include_router(print_data.router, tags=['Print_Data'], prefix='/print_data')
app.include_router(party_print.router, tags=['party_print'], prefix='/party_print')
app.include_router(llm.router, tags=['llm'], prefix='/llm')
app.include_router(smart_dashboard.router, tags=['Smart Dashboard'], prefix='/api/smart-dashboard')

# Initialize database tables on startup
@app.on_event("startup")
async def startup_event():
    # Initialize chat history tables
    await initialize_chat_tables()
    # Initialize restaurant data tables
    await initialize_restaurant_tables()
    print("Database tables initialized successfully")

# Health checker endpoint
@app.get("/api/healthchecker")
async def healthchecker():
    return {"message": "Hola, Welcome to Digi Live Reports!"}
