from collections import defaultdict
import pandas as pd
from collections import Counter
import datetime
import calendar
from app.utility import utilityFunctions as ut
import json
import requests
import urllib.parse
import numpy as np
import math
from app.utility import conversion as conv
import os
import warnings
import copy
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
warnings.filterwarnings('ignore')
import sys
from app.database import db,adjustinvCol,deletedgrnsCol,purchaseinvoicesCol, purchaserequestsCol,deletedindentsCol,dailybackupsCol,spoilages, purchaseordersCol, deletedposCol, tenantsCol, branchesCol,roloposconfigsCol, reportLists, tmpclosingsCol, weightedaveragesCol, menupriceCol, menuidmappingsCol, dailysalesdataCol, tenantsCol, grnsCol, branchesCol, Stockvalues, rts, indentlistsCol, servingsizerecipesCol, ibtsCol, intrabranchtransfersCol
args = sys.argv


reportsDirectory = '../digitoryapi/reports'
header_format = {
    'text_wrap': True,
    'valign': 'vcenter',
    'align': 'center',
    'bg_color': '#7a8c8c',
    'bold': True,
    'font_size': 13,
    'font_color': '#FFFFFF',
    'border': 5,
    'border_color': '#FFFFFF'
}

def getLocation(tenantId):
    location ={}
    branches = branchesCol.find({'tenantId': tenantId},{'restaurantIdOld': 1, 'branchLocation' :1})
    for branch in branches:
        location[branch['restaurantIdOld']]=branch['branchLocation']
    return location

def sanitize_sheet_name(sheet_name, l=20):
    invalid_characters = ['[', ']', '*', '?', '/', '\\']
    for char in invalid_characters:
        sheet_name = sheet_name.replace(char, '_')
    return sheet_name[:l]

def getStockDict(restaurantId, stock_dict):
    if restaurantId not in stock_dict.keys():
        stock_dict[restaurantId] = []
        documents = Stockvalues.find({'restaurantId': restaurantId},{
            'itemCode': 1,
            'category': 1,
            'subCategory': 1,
            'ledger' : 1,
            'taxRate':1,
            'uom' :1,
            'packageName':1,
            'packageQty' : 1,
            'itemName' : 1,
            'entryType' :1,
            'hsnCode':1,
            'withTaxPrice' : 1
        })
        for document in documents:
            entryType = document.get('entryType', 'N/A')
            hsnCode = document.get('hsnCode', 'N/A')
            packageName = document.get('packageName', 'N/A')
            item_code = document.get('itemCode')
            category = document['category']
            itemName = document.get('itemName', 'N/A')
            sub_category = document['subCategory']
            tax_rate = document.get('taxRate', 0)
            uom = document.get('uom', 'N/A')
            packageQty = document.get('packageQty', 1)
            withTaxPrice = document.get('withTaxPrice', 0)
            if category and sub_category:
                stock_dict[restaurantId].append({
                    'itemCode' : item_code,
                    'category': category,
                    'subCategory': sub_category,
                    'ledger' : document.get('ledger', '-'),
                    'taxRate' :tax_rate,
                    'uom' : uom,
                    'packageQty' : packageQty,
                    'packageName' : packageName,
                    'entryType' : entryType,
                    'itemName': itemName,
                    'hsnCode' : hsnCode,
                    'withTaxPrice' : withTaxPrice
                })
    return stock_dict

def getCreatedTime(job):
    createdTime = str(job['createTs'])
    parsed_time = datetime.datetime.strptime(createdTime, "%Y-%m-%d %H:%M:%S")
    dateAndTime = parsed_time.strftime("%d-%m-%Y|%I:%M_%p")
    return dateAndTime

def getGrnDict(job, startTime, endTime, qtyWise=False):
    if 'selectedBaseDate' in job['details'] and job['details']['selectedBaseDate'] == 'invoiceDate':
        baseDate = 'invoiceDate'
    elif 'selectedBaseDate' in job['details'] and job['details']['selectedBaseDate'] == 'grnDate':
        baseDate = 'grnDocumentDate'
    else:
        baseDate = 'createTs'

    grnFilter = {
        'restaurantId': { '$in': job['details']['selectedRestaurants']},
        'grnType': 'po',
        baseDate: {'$gte': startTime, '$lte': endTime}
    }
    if len(job['details']['selectedVendors']) > 0 :
        grnFilter['vendorId'] = {'$in' : job['details']['selectedVendors']}

    purchaseDict = {}
    for grn in grnsCol.find(grnFilter):
        restaurantId = grn['restaurantId']
        if restaurantId not in purchaseDict:
            purchaseDict[restaurantId] = {}
        for item in grn['grnItems']:
            itemCode = item['itemCode']
            if qtyWise:
                for package in item['packages']:
                    searchKey = itemCode + '|' + 'package'.upper() +  '|' + package['packageName'].upper()
                    orderedQty = item['quantity'] if item['quantity'] is not None else 0
                    receivedQty = item['receivedQty'] if item['receivedQty'] is not None else 0
                    RTVQty = item.get('RTVQty', 0)
                    taxAmount = item['taxAmount'] if item['taxAmount'] is not None else 0
                    subTotal = item['subTotal'] if item['subTotal'] is not None else 0
                    totalPrice = item['totalPrice'] if item['totalPrice'] is not None else 0
                    if receivedQty == 0:
                        continue
                    if searchKey in purchaseDict[restaurantId]:
                        purchaseDict[restaurantId][searchKey]['ordered'] += float(orderedQty or 0)
                        purchaseDict[restaurantId][searchKey]['received'] += float(receivedQty or 0)
                        purchaseDict[restaurantId][searchKey]['RTVQty'] += float(RTVQty or 0)
                        purchaseDict[restaurantId][searchKey]['taxAmount'] += float(taxAmount or 0)
                        purchaseDict[restaurantId][searchKey]['total(excl.tax)'] += float(subTotal or 0)
                        purchaseDict[restaurantId][searchKey]['total(incl.tax)'] += float(totalPrice or 0)
                    else:
                        purchaseDict[restaurantId][searchKey] = {
                            "ordered" : 0,
                            "received" : 0,
                            "RTVQty" : 0,
                            "taxAmount": 0,
                            "total(excl.tax)": 0,
                            "total(incl.tax)": 0
                        }
                        purchaseDict[restaurantId][searchKey]['ordered'] = float(orderedQty or 0)
                        purchaseDict[restaurantId][searchKey]['received'] = float(receivedQty or 0)
                        purchaseDict[restaurantId][searchKey]['RTVQty'] += float(RTVQty or 0)
                        purchaseDict[restaurantId][searchKey]['taxAmount'] = float(taxAmount or 0)
                        purchaseDict[restaurantId][searchKey]['total(excl.tax)'] = float(subTotal or 0)
                        purchaseDict[restaurantId][searchKey]['total(incl.tax)'] = float(totalPrice or 0)
            else:
                searchKey = itemCode + '|' + item['uom'].upper()
                for package in item['packages']:
                    packageQty = package.get('packageQty', 1)
                    receivedQty = item['receivedQty'] if item['receivedQty'] is not None else 0
                    if searchKey in purchaseDict[restaurantId]:
                        purchaseDict[restaurantId][searchKey] += float((receivedQty * packageQty) or 0)
                    else:
                        purchaseDict[restaurantId][searchKey] = float((receivedQty * packageQty) or 0)
    return purchaseDict


def getIndentDict(job, startDate, endDate, qtyWise=False, waWise=False, subRecipeSplit=True):
    """
    Optimized implementation of getIndentDict with performance improvements:
    - Batch database queries to eliminate queries inside loops
    - Cache frequently accessed data (stock dictionaries, subrecipe lookups)
    - Optimize data processing and dictionary operations
    """
    import time
    start_time = time.time()

    # Pre-fetch all stock dictionaries for selected restaurants
    stockDict = {}
    for restaurantId in job['details']['selectedRestaurants']:
        getStockDict(restaurantId, stockDict)

    # Build optimized query
    base_query = {'restaurantId': {'$in': job['details']['selectedRestaurants']}}

    if 'selectedBaseDateForIndent' in job['details'] and job['details']['selectedBaseDateForIndent'] == 'documentDate':
        base_query['indentDocumentDate'] = {'$gte': startDate, '$lte': endDate}
    elif 'selectedBaseDateForIndent' in job['details'] and job['details']['selectedBaseDateForIndent'] == 'modDate':
        base_query['modTs'] = {'$gte': startDate, '$lte': endDate}
    else:
        base_query['modTs'] = {'$gte': startDate, '$lte': endDate}

    if len(job['details']['selectedWorkAreas']) > 0:
        base_query['workArea'] = {'$in': job['details']['selectedWorkAreas']}

    # Pre-fetch all subrecipe data to avoid individual lookups
    subrecipe_cache = {}
    if not qtyWise:  # Only needed when not qtyWise
        subrecipe_docs = list(servingsizerecipesCol.find(
            {'tenantId': job['tenantId']},
            {'menuItemCode': 1, '_id': 1, 'Ingredients': 1}  # Include Ingredients field
        ))
        subrecipe_cache = {doc['menuItemCode']: doc for doc in subrecipe_docs}


    # Initialize result dictionaries
    indentsDict = {}
    subrecipeDict = {}

    # Process all indent documents in a single query
    indent_cursor = indentlistsCol.find(base_query)
    processed_items = 0

    for indent in indent_cursor:
        restaurantId = indent['restaurantId']
        workArea = indent['workArea']

        # Initialize restaurant dictionaries if needed
        if restaurantId not in indentsDict:
            indentsDict[restaurantId] = {}
        if restaurantId not in subrecipeDict:
            subrecipeDict[restaurantId] = {}
        # Process all items in this indent document
        for item in indent['indentItems']:
            itemCode = item['itemCode']

            # Optimize field access with default values
            entry_type = item.get('entryType', 'N/A') or 'N/A'
            package_name = item.get('packageName', 'N/A') or 'N/A'
            issue_qty = item.get('issueQty', 0) or 0
            pending_qty = item.get('pendingQty', 0) or 0
            dispatched_qty = issue_qty - pending_qty
            received_qty = item.get('receivedQty', issue_qty) or issue_qty

            if received_qty == 0:
                continue


            # sv = next((x for x in stockDict[restaurantId] if x['itemCode'] == itemCode and x['packageName'] == package_name), None)
            # category = None
            # if sv:
            #     category = sv['category']
            # if category and category in job['details']['excludedCategories']:
            #     continue

            # Calculate unit price
            unit_price = 0
            if 'packages' in item:
                unit_price = item.get('totalPrice', 0) / received_qty if received_qty > 0 else 0
            else:
                unit_price = item.get('price', 0)

            processed_items += 1

            if qtyWise:
                search_key = itemCode + '|' + entry_type.upper() + '|' + package_name.upper()
                if waWise:
                    if search_key not in indentsDict[restaurantId]:
                        indentsDict[restaurantId][search_key] = {}
                    if workArea not in indentsDict[restaurantId][search_key]:
                        indentsDict[restaurantId][search_key][workArea] = {}
                    work_area_data = indentsDict[restaurantId][search_key].setdefault(workArea, {
                        'requested': 0,
                        'issued': 0,
                        'unitPrice': 0
                    })

                    work_area_data['requested'] = work_area_data.get('requested', 0) + issue_qty
                    work_area_data['issued'] = work_area_data.get('issued', 0) + dispatched_qty
                    work_area_data['unitPrice'] = work_area_data.get('unitPrice', 0) + (dispatched_qty * unit_price)
                else:
                    if search_key in indentsDict[restaurantId]:
                        indentsDict[restaurantId][search_key]['requested'] += issue_qty
                        indentsDict[restaurantId][search_key]['issued'] += dispatched_qty
                        indentsDict[restaurantId][search_key]['unitPrice'] += (dispatched_qty * unit_price)
                    else:
                        indentsDict[restaurantId][search_key] = {
                            "requested": issue_qty,
                            "issued": dispatched_qty,
                            'unitPrice': (dispatched_qty * unit_price)
                        }
            else:
                # Non-qtyWise processing with optimized subrecipe lookup
                package_qty = 1
                sv = next((x for x in stockDict[restaurantId] if x['itemCode'] == itemCode and x['packageName'] == package_name), None)
                if sv:
                    uom = sv['uom']
                    package_qty = sv['packageQty']
                    search_key = itemCode + '|' + uom.upper()
                else:  # ASSUMED AS SUBRECIPE
                    package_qty = 1
                    search_key = itemCode + '|' + package_name.upper()

                # Use cached subrecipe lookup instead of individual database query
                is_subrecipe = subrecipe_cache.get(itemCode)
                if is_subrecipe:
                    if search_key in subrecipeDict[restaurantId]:
                        subrecipeDict[restaurantId][search_key] += float((dispatched_qty * package_qty) or 0)
                    else:
                        subrecipeDict[restaurantId][search_key] = float((dispatched_qty * package_qty) or 0)
                    if subRecipeSplit:
                        getSubRecipeValues(is_subrecipe, indentsDict, job['tenantId'], restaurantId, dispatched_qty)
                else:
                    if search_key in indentsDict[restaurantId]:
                        indentsDict[restaurantId][search_key] += float((dispatched_qty * package_qty) or 0)
                    else:
                        indentsDict[restaurantId][search_key] = float((dispatched_qty * package_qty) or 0)

    # Performance logging
    total_time = time.time() - start_time

    return [indentsDict, subrecipeDict]

def convert_to_float(value):
    """
    Converts a value to float. Handles None, empty strings, and non-numeric strings gracefully.
    """
    if value is None:
        return 0.0
    elif isinstance(value, (int, float)):
        return float(value)
    elif isinstance(value, str):
        stripped_value = value.strip()
        if stripped_value:
            try:
                return float(stripped_value)
            except ValueError:
                return 0.0
        else:
            return 0.0
    else:
        return 0.0

def getIbtDict(job, startDate, endDate, qtyWise=False):
    stockDict ={}
    ibtSearchFilter = {
        'tenantId': job['tenantId'],
        'receivedDate': {'$exists' : True},
        'receivedDate' :{"$gte":startDate,"$lte":endDate},
    }
    ibtInDict = {}
    subrecipeDict ={}
    for ibt in ibtsCol.find(ibtSearchFilter):
        restaurantId = ibt['toBranch']['restaurantId']
        getStockDict(restaurantId, stockDict)
        if restaurantId not in ibtInDict:
            ibtInDict[restaurantId] = {}
        if restaurantId not in subrecipeDict:
            subrecipeDict[restaurantId] = {}
        for item in ibt['items']:
            itemCode = item['itemCode']
            item['entryType'] = item.get('entryType') if item.get('entryType') is not None else 'N/A'
            item['packageName'] = item.get('packageName') if item.get('packageName') is not None else 'N/A'
            receivedQty = (item['quantity'] - item['recPendingQty']) - sum(item['shortageHistory'])
            if qtyWise:
                searchKey = itemCode + '|' + item['entryType'].upper() +  '|' + item['packageName'].upper()
                if searchKey in ibtInDict[restaurantId]:
                    ibtInDict[restaurantId][searchKey] += receivedQty
                else:
                    ibtInDict[restaurantId][searchKey] = receivedQty
            else:
                packageQty = item.get('packageQty', 1)
                sv = next((x for x in stockDict[restaurantId] if x['itemCode'] == itemCode and x['packageName'] == item['packageName']), None)
                if sv:
                    uom = sv['uom']
                else:
                    print("CHECK2")
                    continue
                searchKey = itemCode + '|' + uom.upper()
                isSubRecipe = servingsizerecipesCol.find_one({'tenantId': job['tenantId'], 'menuItemCode': itemCode})
                if isSubRecipe:
                    if searchKey in subrecipeDict[restaurantId]:
                        subrecipeDict[restaurantId][searchKey] += float((receivedQty * packageQty) or 0)
                    else:
                        subrecipeDict[restaurantId][searchKey] = float((receivedQty * packageQty) or 0)
                    getSubRecipeValues(isSubRecipe, ibtInDict, job['tenantId'], restaurantId, receivedQty)
                else:
                    if searchKey in ibtInDict[restaurantId]:
                        ibtInDict[restaurantId][searchKey] += float((receivedQty * packageQty) or 0)
                    else:
                        ibtInDict[restaurantId][searchKey] = float((receivedQty * packageQty) or 0)


    ibtSearchFilter = {
        'tenantId': job['tenantId'],
        'dispatchedDate': {'$exists' : True},
        'dispatchedDate' :{"$gte":startDate,"$lte":endDate},
    }
    ibtOutDict = {}
    subrecipeDict ={}
    for ibt in ibtsCol.find(ibtSearchFilter):
        restaurantId = ibt['fromBranch']['restaurantId']
        getStockDict(restaurantId, stockDict)
        if restaurantId not in ibtOutDict:
            ibtOutDict[restaurantId] = {}
        if restaurantId not in subrecipeDict:
            subrecipeDict[restaurantId] = {}
        for item in ibt['items']:
            itemCode = item['itemCode']
            item['entryType'] = item.get('entryType') if item.get('entryType') is not None else 'N/A'
            item['packageName'] = item.get('packageName') if item.get('packageName') is not None else 'N/A'
            dispatchedQty = item['quantity'] - item['pendingQty']
            if qtyWise:
                searchKey = itemCode + '|' + item['entryType'].upper() +  '|' + item['packageName'].upper()
                if searchKey in ibtOutDict[restaurantId]:
                    ibtOutDict[restaurantId][searchKey] += dispatchedQty
                else:
                    ibtOutDict[restaurantId][searchKey] = dispatchedQty
            else:
                packageQty = item.get('packageQty', 1)
                sv = next((x for x in stockDict[restaurantId] if x['itemCode'] == itemCode and x['packageName'] == item['packageName']), None)
                if sv:
                    uom = sv['uom']
                else:
                    print("CHECK3")
                    continue
                searchKey = itemCode + '|' + uom.upper()
                isSubRecipe = servingsizerecipesCol.find_one({'tenantId': job['tenantId'], 'menuItemCode': itemCode})
                if isSubRecipe:
                    if searchKey in subrecipeDict[restaurantId]:
                        subrecipeDict[restaurantId][searchKey] += float((dispatchedQty * packageQty) or 0)
                    else:
                        subrecipeDict[restaurantId][searchKey] = float((dispatchedQty * packageQty) or 0)
                    getSubRecipeValues(isSubRecipe, ibtOutDict, job['tenantId'], restaurantId, dispatchedQty)
                else:
                    if searchKey in ibtOutDict[restaurantId]:
                        ibtOutDict[restaurantId][searchKey] += float((dispatchedQty * packageQty) or 0)
                    else:
                        ibtOutDict[restaurantId][searchKey] = float((dispatchedQty * packageQty) or 0)
    return [ibtInDict, ibtOutDict]


def getIntraBranchTransferDict(job, startTime, endTime):
    ibtSearchFilter = {
        'restaurantId': {'$in': job['details']['selectedRestaurants']},
        'date': {'$gte': startTime, '$lte': endTime}
    }

    ibtData = list(intrabranchtransfersCol.find(ibtSearchFilter))
    ibtInDict = {}
    itemSvDict={}
    subrecipeDict ={}
    for ibt in ibtData:
        restaurantId = ibt['restaurantId']
        tenantId = ibt['tenantId']
        workArea = ibt['destinationWorkArea']
        if workArea not in job['details']['selectedWorkAreas']:
            continue
        if restaurantId not in ibtInDict:
            ibtInDict[restaurantId] = {}
        if restaurantId not in subrecipeDict:
            subrecipeDict[restaurantId] = {}
        for itemKey,quantity in ibt['items'].items():
            parts = itemKey.split('|')
            if len(parts) == 3:
                itemCode, packageName, entryType = parts
            elif len(parts) == 4:
                itemCode, packageName, entryType = parts[1], parts[2], parts[3]
            if '#' in packageName :
                packageName = packageName.replace('#','.')
            isSubRecipe = servingsizerecipesCol.find_one({'tenantId': tenantId, 'menuItemCode': itemCode})
            if isSubRecipe:
                getSubRecipeValues(isSubRecipe, ibtInDict, tenantId, restaurantId, quantity)
            else:
                uom = packageName
                packageQty = 1
                if entryType == "open":
                    key = itemCode + "|" + entryType
                else:
                    key = itemCode + "|" + packageName
                if key in itemSvDict:
                    packageQty = itemSvDict[key]['packageQty']
                    uom = itemSvDict[key]['uom']
                else:
                    svEntry = None
                    if packageName != "N/A" and entryType == "package":
                        svEntry = Stockvalues.find_one({'restaurantId': restaurantId, 'itemCode': itemCode, 'packageName': packageName},{"packageQty" : 1, 'uom' :1})
                    else:
                        svEntry = Stockvalues.find_one({'restaurantId': restaurantId, 'itemCode': itemCode, 'entryType':'open'},{"packageQty" : 1, 'uom' :1})
                    if svEntry:
                        packageQty = svEntry.get('packageQty', 1)
                        uom = svEntry.get('uom', 'N/A')
                    itemSvDict[key] = {'packageQty' : packageQty , 'uom': uom }


                searchKey = itemCode + '|' + uom.upper()
                if searchKey in ibtInDict[restaurantId]:
                    ibtInDict[restaurantId][searchKey] += float((quantity * packageQty) or 0)
                else:
                    ibtInDict[restaurantId][searchKey] = float((quantity * packageQty) or 0)

    ibtOutDict = {}
    subrecipeDict ={}
    for ibt in ibtData:
        restaurantId = ibt['restaurantId']
        tenantId = ibt['tenantId']
        workArea = ibt['sourceWorkArea']
        if workArea not in job['details']['selectedWorkAreas']:
            continue
        if restaurantId not in ibtOutDict:
            ibtOutDict[restaurantId] = {}
        if restaurantId not in subrecipeDict:
            subrecipeDict[restaurantId] = {}
        for itemKey,quantity in ibt['items'].items():
            parts = itemKey.split('|')
            if len(parts) == 3:
                itemCode, packageName, entryType = parts
            elif len(parts) == 4:
                itemCode, packageName, entryType = parts[1], parts[2], parts[3]
            isSubRecipe = servingsizerecipesCol.find_one({'tenantId': tenantId, 'menuItemCode': itemCode})
            if isSubRecipe:
                getSubRecipeValues(isSubRecipe, ibtOutDict, tenantId, restaurantId, quantity)
            else:
                if entryType == "open":
                    key = itemCode + "|" + entryType
                else:
                    key = itemCode + "|" + packageName
                if key in itemSvDict:
                    packageQty = itemSvDict[key]['packageQty']
                    uom = itemSvDict[key]['uom']
                else:
                    svEntry = None
                    if packageName != "N/A" and entryType == "package":
                        svEntry = Stockvalues.find_one({'restaurantId': restaurantId, 'itemCode': itemCode, 'packageName': packageName},{"packageQty" : 1, 'uom' :1})
                    else:
                        svEntry = Stockvalues.find_one({'restaurantId': restaurantId, 'itemCode': itemCode, 'entryType':'open'},{"packageQty" : 1, 'uom' :1})
                    if svEntry:
                        packageQty = svEntry.get('packageQty', 1)
                        uom = svEntry.get('uom', 'N/A')
                    itemSvDict[key] = {'packageQty' : packageQty , 'uom': uom }

                searchKey = itemCode + '|' + uom.upper()
                if searchKey in ibtOutDict[restaurantId]:
                    ibtOutDict[restaurantId][searchKey] += float((quantity * packageQty) or 0)
                else:
                    ibtOutDict[restaurantId][searchKey] = float((quantity * packageQty) or 0)
    return [ibtInDict, ibtOutDict]

def getSpoilageDictStore(job, startTime, endTime, qtyWise=False):
    adjInvFilter = {
        'restaurantId': {'$in': job['details']['selectedRestaurants']},
        'createTs': {'$gte': startTime, '$lte': endTime}
    }
    adjInvDict = {}
    for adjInv in adjustinvCol.find(adjInvFilter):
        restaurantId = adjInv['restaurantId']
        if restaurantId not in adjInvDict:
            adjInvDict[restaurantId] = {}
        for item in adjInv['adjustInvItems']:
            itemCode = item['itemCode']
            item['packageName'] = item.get('packageName') if item.get('packageName') is not None else 'N/A'
            item['entryType'] = item.get('entryType') if item.get('entryType') is not None else 'N/A'
            searchKey = itemCode + '|' + item['entryType'].upper() +  '|' + item['packageName'].upper()
            if item['adjustQty'] is None:
                item['adjustQty'] = 0
            elif item['adjustType'] == "Dec":
                item['adjustQty'] = - float(item['adjustQty'])
            if searchKey in adjInvDict[restaurantId]:
                adjInvDict[restaurantId][searchKey] += item['adjustQty']
            else:
                adjInvDict[restaurantId][searchKey] = item['adjustQty']
    return adjInvDict

def getRTS(job, startTime, endTime, qtyWise=False):
    rtsFilter = {
        'restaurantId': {'$in': job['details']['selectedRestaurants']},
        'createTs': {'$gte': startTime, '$lte': endTime}
    }
    if len(job['details']['selectedWorkAreas']) > 0 :
        rtsFilter['workArea'] = {'$in' : job['details']['selectedWorkAreas']}

    rtsDict = {}
    for rec in rts.find(rtsFilter):
        restaurantId = rec['restaurantId']
        if restaurantId not in rtsDict:
            rtsDict[restaurantId] = {}
        for item in rec['items']:
            item['packageName'] = item.get('packageName') if item.get('packageName') is not None else 'N/A'
            item['entryType'] = item.get('entryType') if item.get('entryType') is not None else 'N/A'
            item['packageQty'] = item.get('packageQty', 1)
            if not qtyWise:
                searchKey = item['itemCode'] + '|' + item['entryType'].upper() +  '|' + item['packageName'].upper()
                if searchKey in rtsDict[restaurantId]:
                    rtsDict[restaurantId][searchKey] += float(item['transferQty'])
                else:
                    rtsDict[restaurantId][searchKey] = float(item['transferQty'])
            else:
                searchKey = item['itemCode'] + '|' + item['uom'].upper()
                if searchKey in rtsDict[restaurantId]:
                    rtsDict[restaurantId][searchKey] += float((float(item['transferQty']) * item['packageQty']) or 0)
                else:
                    rtsDict[restaurantId][searchKey] = float((float(item['transferQty']) * item['packageQty']) or 0)
    return rtsDict

def getSpoilageDictWorkArea(job, startTime, endTime):
    adjInvFilter = {
        'restaurantId': {'$in': job['details']['selectedRestaurants']},
        'workArea': {'$in': job['details']['selectedWorkAreas']},
        'createTs': {'$gte': startTime, '$lte': endTime}
    }
    adjInvDict = {}
    itemSvDict={}
    for adjInv in adjustinvCol.find(adjInvFilter):
        restaurantId = adjInv['restaurantId']
        if restaurantId not in adjInvDict:
            adjInvDict[restaurantId] = {}
        for item in adjInv['adjustInvItems']:
            itemCode = item['itemCode']
            entryType = item.get('entryType') if item.get('entryType') is not None else 'N/A'
            packageName = item.get('packageName') if item.get('packageName') is not None else 'N/A'
            caseFlag = 0
            if packageName !='N/A' and entryType !='N/A':
                key = itemCode + "|" + packageName
                caseFlag = 1
            elif entryType == "open":
                key = itemCode + "|" + entryType
                caseFlag = 2
            else:
                key = itemCode
                caseFlag = 3

            if key in itemSvDict:
                packageQty = itemSvDict[key]['packageQty']
                uom = itemSvDict[key]['uom']
            else:
                svEntry = None
                if caseFlag == 1:
                    svEntry = Stockvalues.find_one({'restaurantId': restaurantId, 'itemCode': itemCode, 'packageName': packageName},{"packageQty" : 1, 'uom' :1})
                elif caseFlag == 2:
                    svEntry = Stockvalues.find_one({'restaurantId': restaurantId, 'itemCode': itemCode, 'entryType':'open'},{"packageQty" : 1, 'uom' :1})
                else:
                    svEntry = Stockvalues.find_one({'restaurantId': restaurantId, 'itemCode': itemCode},{"packageQty" : 1, 'uom' :1})
                if svEntry:
                    packageQty = svEntry.get('packageQty', 1)
                    uom = svEntry.get('uom', 'N/A')
                itemSvDict[key] ={'packageQty' : packageQty, 'uom' :uom}

            searchKey = itemCode + '|' + uom.upper()
            if item['adjustQty'] is None:
                item['adjustQty'] = 0
            else:
                if item['adjustType'] == "Inc":
                    item['adjustQty'] = item['adjustQty']
                else:
                    item['adjustQty'] = -(item['adjustQty'])
            if searchKey in adjInvDict[restaurantId]:
                adjInvDict[restaurantId][searchKey] += float((item['adjustQty'] * packageQty) or 0)
            else:
                adjInvDict[restaurantId][searchKey] = float((item['adjustQty'] * packageQty) or 0)
    return adjInvDict


def getSubRecipeValues(recipe, invDict, tenantId, restaurantId, quantity=0, modifierReceipes=[],autobarDict={},autobarFlag= False, subrecipeDict={}):
    if recipe is not None:
        totalItemSales = quantity
        for ingredient in recipe['Ingredients']:
            itemCode = ingredient['IngredientCode']
            if itemCode == recipe['menuItemCode']:
                continue
            searchKey = ""
            initWeight = 0
            if ingredient['ingredientUom'].lower() == 'gm':
                initWeight = conv.toKilo(ingredient['initialWeight'])
                searchKey = itemCode + '|' + "KG"
                if autobarFlag:
                    autobarSearchKey = recipe['menuItemCode'] + '|' + "KG"
            elif ingredient['ingredientUom'].lower() == 'ml':
                initWeight = conv.toLitre(ingredient['initialWeight'])
                searchKey = itemCode + '|' + "LITRE"
                if autobarFlag:
                    autobarSearchKey = recipe['menuItemCode'] + '|' + "LITRE"
            else:
                initWeight = ingredient['initialWeight']
                searchKey = itemCode + '|' + ingredient['ingredientUom']
                if autobarFlag:
                    autobarSearchKey = recipe['menuItemCode'] + '|' + ingredient['ingredientUom']
            isSubRecipe = servingsizerecipesCol.find_one({'tenantId': tenantId, 'menuItemCode': itemCode})
            if isSubRecipe:
                requirement = totalItemSales * initWeight
                if restaurantId in subrecipeDict:
                    if searchKey in subrecipeDict[restaurantId]:
                        subrecipeDict[restaurantId][searchKey] += (requirement)
                    else:
                        subrecipeDict[restaurantId][searchKey] = (requirement)
                getSubRecipeValues(isSubRecipe, invDict, tenantId, restaurantId, requirement, modifierReceipes, autobarDict, autobarFlag, subrecipeDict)
            else:
                modifierCounter = Counter(modifierReceipes)
                if(("isModifier" in ingredient.keys()) and (ingredient['isModifier'].upper().strip() =="Y") and (ingredient['modifierName'] not in modifierCounter.keys())):
                    continue
                elif (("isModifier" in ingredient.keys()) and (ingredient['isModifier'].upper().strip() =="Y") and (ingredient['modifierName'] in modifierCounter.keys()) ):
                    if searchKey in invDict[restaurantId]:
                        invDict[restaurantId][searchKey] += (float(modifierCounter[ingredient['modifierName']]) * initWeight)
                    else:
                        invDict[restaurantId][searchKey] = (float(modifierCounter[ingredient['modifierName']]) * initWeight)
                    if autobarFlag:
                        if autobarSearchKey in autobarDict[restaurantId]:
                            autobarDict[restaurantId][autobarSearchKey] += (float(modifierCounter[ingredient['modifierName']]) * initWeight)
                        else:
                            autobarDict[restaurantId][autobarSearchKey] = (float(modifierCounter[ingredient['modifierName']]) * initWeight)
                else:
                    if searchKey in invDict[restaurantId]:
                        invDict[restaurantId][searchKey] += (quantity * initWeight)
                    else:
                        invDict[restaurantId][searchKey] = (quantity * initWeight)
                    if autobarFlag:
                        if autobarSearchKey in autobarDict[restaurantId]:
                            autobarDict[restaurantId][autobarSearchKey] += (quantity * initWeight)
                        else:
                            autobarDict[restaurantId][autobarSearchKey] = (quantity * initWeight)

def getTheoreticalConsumption(job, startTime, endTime, autobar=False):
    salesSearchFilter = {
        'tenantId': job['tenantId'],
        'restaurantId': {'$in': job['details']['selectedRestaurants']},
        'date': {'$gte': startTime, '$lte': endTime}
    }
    orQueryForWorkAreas=[]
    for area in job['details']['selectedWorkAreas']:
        temp={}
        temp['salesByWorkArea.{}'.format(area.strip())] = {'$exists': True }
        orQueryForWorkAreas.append(temp)
    if len(orQueryForWorkAreas) > 0:
        salesSearchFilter['$or'] = orQueryForWorkAreas

    invDict = {}
    subrecipeDict = {}
    autobarDict = {}
    for entry in dailysalesdataCol.find(salesSearchFilter):
        restaurantId = entry['restaurantId']
        if restaurantId not in invDict.keys():
            invDict[restaurantId] = {}
        if restaurantId not in subrecipeDict.keys():
            subrecipeDict[restaurantId] = {}
        if restaurantId not in autobarDict.keys():
            autobarDict[restaurantId] = {}
        for workArea in entry['salesByWorkArea']:
            if workArea not in job['details']['selectedWorkAreas']:
                continue
            for itemKey, value in entry['salesByWorkArea'][workArea].items():
                menuItemCode, servingSize = itemKey.split("|")
                menuItemCode = menuItemCode.strip()
                servingSize = servingSize.strip()
                menu = servingsizerecipesCol.find_one({
                    'tenantId': job['tenantId'],
                    'menuItemCode': menuItemCode,
                    "isSubRecipe" : False,
                    'servingSize': servingSize
                })
                if menu is None:
                    continue
                getSubRecipeValues(menu, invDict, job['tenantId'], restaurantId, value['quantity'], value['modifierReceipes'],autobarDict, autobar, subrecipeDict)
    if autobar:
        return [invDict, autobarDict]
    else:
        return [invDict, subrecipeDict]

def getSellingPrice(job, month, year):
    menus = list(menuidmappingsCol.find({'tenantId': job['tenantId']}))
    menu_selling_price = {}
    key_search = year + '_'+ month
    for menu in menus:
        if 'price' not in menu.keys():
            continue
        for rId , value in menu['price'].items():
            if rId not in menu_selling_price.keys():
                menu_selling_price[rId] ={}
            if key_search in value.keys():
                search_key =  menu['itemCode'] + '|' + menu['servingSize']
                if value[key_search]['weighted_average'] <=0:
                    value[key_search]['weighted_average'] = 0.1
                menu_selling_price[rId][search_key] = {
                    "non_modifier" : value[key_search]['weighted_average'],
                    "modifier" : value[key_search]['overall_modifier_unit_costs']
                }
    return menu_selling_price

def getProductionCost(job, month, year):
    query = {
        'tenantId': job['tenantId'],
        'restaurantId': {'$in': job['details']['selectedRestaurants']},
        'month': month,
        'year' : year
    }
    menus = list(menupriceCol.find(query))
    menu_production_cost = {}
    for menu in menus:
        if menu['restaurantId'] not in menu_production_cost.keys():
            menu_production_cost[menu['restaurantId']] ={}
        search_key =  menu['menuItemCode'] + '|' + menu['servingSize']
        menu_production_cost[menu['restaurantId']][search_key] = {
            "modifier" : {mod["modifierName"]: (mod.get("withTaxPrice", 0)) for mod in menu['splitUp']['modifier'].values()},
            "non_modifier" : sum((ing.get("withTaxPrice", 0)) for ing in menu['splitUp']['nonModfier'].values())
        }
    return menu_production_cost



def getDailySales(job, startTime, endTime):
    salesSearchFilter = {
        'tenantId': job['tenantId'],
        'restaurantId': {'$in': job['details']['selectedRestaurants']},
        'date': {'$gte': startTime, '$lte': endTime}
    }
    modified_sales_data = []
    for sales_item in dailysalesdataCol.find(salesSearchFilter):
        restaurant_id = sales_item.get('restaurantId', '')
        sales_by_work_area = sales_item.get('salesByWorkArea', {})
        for work_area, menu_items in sales_by_work_area.items():
            for menu_item, details in menu_items.items():
                quantity = details.get('quantity', 0)
                modifiers = details.get('modifierReceipes', [])
                if quantity != len(modifiers):
                    diff = quantity - len(modifiers)
                    if diff > 0:
                        modifiers.extend(['NAN'] * diff)

                modifier_quantity_dict = {}
                for modifier in modifiers:
                    modifier_quantity_dict[modifier] = 0
                for modifier in modifiers:
                    modifier_quantity_dict[modifier] += 1
                for modifier, modifier_quantity in modifier_quantity_dict.items():
                    modified_sales_data.append({
                        'Restaurant ID': restaurant_id,
                        'Work Area': work_area,
                        'Menu Item': menu_item,
                        'Quantity': modifier_quantity,
                        'Modifier': modifier
                    })
    df = pd.DataFrame(modified_sales_data)
    if len(df) > 0:
        df_grouped = df.groupby(['Restaurant ID', 'Work Area', 'Menu Item', 'Modifier'], as_index=False)['Quantity'].sum()
        return df_grouped
    else:
        return df

def get_purchase_rates(restaurantIds, baseDate, unitsLevel=False):
    rates = {}
    d = calendar.monthrange(baseDate.year, baseDate.month)
    current_year = baseDate.year
    previous_year = current_year - 1
    for rId in restaurantIds:
        rates[rId] = {}
        match_query = {
            'restaurantId': rId,
            'year': {"$in": [current_year, previous_year]},
            'date': {"$lte": baseDate.replace(day=d[1])}
        }
        records = weightedaveragesCol.find(match_query,{'details': 0}).sort([('date', 1)])
        latest_records = {}
        for record in records:
            key = record['itemCode'] if unitsLevel else f"{record['itemCode']}|{record['packageName']}"
            latest_records[key] = {
                "restaurantId": record["restaurantId"],
                "month": record["month"],
                "year": record["year"],
                "itemCode": record["itemCode"],
                "packageName": record["packageName"],
                "wac": float(record.get("weightedAverage", 0)),
                "monthWac" : float(record.get("monthWeightedAverage", 0)),
                "cessCharges": float(record.get("cessCharges", 0)),
                "extraCharges": float(record.get("extraCharges", 0)),
                "discCharges": float(record.get("discCharges", 0)),
                "taxAmount": float(record.get("taxAmount", 0))
            }
        if latest_records:
            rates[rId] = latest_records
    return rates

## requestedCategory = 1 means , needs conversion in litre, kg , nos
def getUserDictWorkArea(job, event, requestedCategory=0):
    if event == "closing":
        if "endDate" in job['details'] and job['details']['endDate']:
            startTime = job['details']['endDate'].replace(hour=0, minute=0, second=0)
            endTime = job['details']['endDate'].replace(hour=23, minute=59, second=59)
        else:
            startTime = job['details']['startDate'].replace(hour=0, minute=0, second=0)
            endTime = job['details']['startDate'].replace(hour=23, minute=59, second=59)
    if event == "opening":
        startTime = job['details']['startDate'].replace(hour=0, minute=0, second=0) - datetime.timedelta(days=1)
        endTime = job['details']['startDate'].replace(hour=23, minute=59, second=59) - datetime.timedelta(days=1)
    userFilter = {
        'type' : 'kitchenClosing',
        'restaurantId': {'$in': job['details']['selectedRestaurants']},
        'date': {'$gte': startTime, '$lte': endTime},
        'active': False
    }
    orQueryForWorkAreas=[]
    for area in job['details']['selectedWorkAreas']:
        temp={}
        temp['workAreas.{}'.format(area.strip())] = {'$exists': True }
        orQueryForWorkAreas.append(temp)
    if len(orQueryForWorkAreas) > 0:
        userFilter['$or'] = orQueryForWorkAreas

    userList = list(tmpclosingsCol.find(userFilter))
    userList.reverse()
    userDict = {}
    stockDict = {}
    for entry in userList:
        restaurantId = entry['restaurantId']
        getStockDict(restaurantId, stockDict)
        if restaurantId not in userDict:
            userDict[restaurantId] = {}
        for workArea in entry['workAreas']:
            if workArea not in job['details']['selectedWorkAreas']:
                continue
            if workArea not in userDict[restaurantId]:
                userDict[restaurantId][workArea] = {}
            for category in entry['workAreas'][workArea]['category']:
                for subCategory in entry['workAreas'][workArea]['category'][category]['subCategory']:
                    if "items" in entry['workAreas'][workArea]['category'][category]['subCategory'][subCategory]:
                        for itemCode in entry['workAreas'][workArea]['category'][category]['subCategory'][subCategory]['items']:
                            if "packagingSizes" in entry['workAreas'][workArea]['category'][category]['subCategory'][subCategory]['items'][itemCode]:
                                for data in entry['workAreas'][workArea]['category'][category]['subCategory'][subCategory]['items'][itemCode]['packagingSizes']:
                                    searchKey = itemCode + '|' + data['uom'].upper() +'|'+ data['pkgName'].upper()
                                    if data['uom'].lower() == 'kg':
                                        weight = 1000
                                    elif data['uom'].lower() == 'litre' or data['uom'].lower() == 'ltr':
                                        weight = 1000
                                    elif data['uom'].lower() == 'nos' or data['uom'].lower() == 'mtr':
                                        weight = 1
                                    else:
                                        continue
                                    if data['otherPackages'] is None:
                                        data['otherPackages'] = 0
                                    if not isinstance(data['orderedPackages'], (int, float)):
                                        data['orderedPackages'] = 0
                                    sv = next((x for x in stockDict[restaurantId] if x['itemCode'] == itemCode and x['packageName'] == data['pkgName']), None)
                                    if sv:
                                        data['pkgQty'] = sv['packageQty']
                                    if searchKey not in userDict[restaurantId][workArea].keys():
                                        userDict[restaurantId][workArea][searchKey] = 0
                                        if requestedCategory == 0:
                                            userDict[restaurantId][workArea][searchKey] += ((data['orderedPackages'] * data['pkgQty'])* weight)
                                            if data['uom'].lower() == 'litre' or data['uom'].lower() == 'ltr':
                                                userDict[restaurantId][workArea][searchKey] += checkBottleWeight(data)
                                            else:
                                                userDict[restaurantId][workArea][searchKey] += ((data['otherPackages'])* weight)
                                        elif requestedCategory == 2:
                                            if data['uom'].lower() == 'litre' or data['uom'].lower() == 'ltr':
                                                userDict[restaurantId][workArea][searchKey] = {
                                                    'open' : (checkBottleWeight(data)/1000),
                                                    "package" : data['orderedPackages']
                                                }
                                            else:
                                                userDict[restaurantId][workArea][searchKey] = {
                                                    'open' : data['otherPackages'],
                                                    "package" : data['orderedPackages']
                                                }
                                        else:
                                            userDict[restaurantId][workArea][searchKey] += (data['orderedPackages'] * data['pkgQty'])
                                            if data['uom'].lower() == 'litre' or data['uom'].lower() == 'ltr':
                                                userDict[restaurantId][workArea][searchKey] += (checkBottleWeight(data)/1000)
                                            else:
                                                userDict[restaurantId][workArea][searchKey] += (data['otherPackages'])
                            else:
                                data = entry['workAreas'][workArea]['category'][category]['subCategory'][subCategory]['items'][itemCode]
                                searchKey = itemCode + '|' + 'N/A' +'|'+ 'N/A'
                                if searchKey not in userDict[restaurantId][workArea].keys():
                                    userDict[restaurantId][workArea][searchKey] = 0
                                    if data['closingStock'] is None:
                                        data['closingStock'] = 0
                                    if requestedCategory == 0:
                                        userDict[restaurantId][workArea][searchKey] += (data['closingStock'] * 1000)
                                    elif requestedCategory == 2:
                                        userDict[restaurantId][workArea][searchKey] = {
                                            "open" : data['closingStock']
                                        }
                                    else:
                                        userDict[restaurantId][workArea][searchKey] += data['closingStock']
    if requestedCategory ==2:
        res={}
        for restId in userDict.keys():
            res[restId] ={}
            for wa in userDict[restId]:
                if wa not in res[restId].keys():
                    res[restId][wa] ={}
                for searchKey in userDict[restId][wa].keys():
                    itemCode, uom , pkgName = searchKey.split("|")
                    if itemCode not in res[restId][wa].keys():
                        res[restId][wa][itemCode] = userDict[restId][wa][searchKey]['open']
                    else:
                        res[restId][wa][itemCode] += userDict[restId][wa][searchKey]['open']
        return res, userDict
    else:
        res={}
        for restId in userDict.keys():
            res[restId] ={}
            for wa in userDict[restId]:
                for searchKey in userDict[restId][wa].keys():
                    itemCode, uom , pkgName = searchKey.split("|")
                    updatedSearchKey = itemCode+"|" + uom.upper()
                    if updatedSearchKey not in res[restId].keys():
                        res[restId][updatedSearchKey] = userDict[restId][wa][searchKey]
                    else:
                        res[restId][updatedSearchKey] += userDict[restId][wa][searchKey]
        return res

def getUserDictStore(job, event, requestedCategory=0, qtyWise=False):
    if event == "closing":
        if "endDate" in job['details'] and job['details']['endDate']:
            startTime = job['details']['endDate'].replace(hour=0, minute=0, second=0)
            endTime = job['details']['endDate'].replace(hour=23, minute=59, second=59)
        else:
            startTime = job['details']['startDate'].replace(hour=0, minute=0, second=0)
            endTime = job['details']['startDate'].replace(hour=23, minute=59, second=59)
    if event == "opening":
        startTime = job['details']['startDate'].replace(hour=0, minute=0, second=0) - datetime.timedelta(days=1)
        endTime = job['details']['startDate'].replace(hour=23, minute=59, second=59) - datetime.timedelta(days=1)
    userFilter = {
        'type' : 'storeClosing',
        'restaurantId': {'$in': job['details']['selectedRestaurants']},
        'date': {'$gte': startTime, '$lte': endTime},
        'active': False
    }
    userList = list(tmpclosingsCol.find(userFilter))
    userList.reverse()
    userDict = {}
    stockDict = {}
    for entry in userList:
        restaurantId = entry['restaurantId']
        getStockDict(restaurantId, stockDict)
        if restaurantId not in userDict:
            userDict[restaurantId] = {}
        for workArea in entry['workAreas']:
            if workArea not in userDict[restaurantId]:
                userDict[restaurantId][workArea] = {}
            for category in entry['workAreas'][workArea]['category']:
                for subCategory in entry['workAreas'][workArea]['category'][category]['subCategory']:
                    if "items" in entry['workAreas'][workArea]['category'][category]['subCategory'][subCategory]:
                        for itemCode in entry['workAreas'][workArea]['category'][category]['subCategory'][subCategory]['items']:
                            if "packagingSizes" in entry['workAreas'][workArea]['category'][category]['subCategory'][subCategory]['items'][itemCode]:
                                for data in entry['workAreas'][workArea]['category'][category]['subCategory'][subCategory]['items'][itemCode]['packagingSizes']:
                                    searchKey = itemCode + '|' + data['uom'].upper() +'|'+ data['pkgName'].upper()
                                    if data['uom'].lower() == 'kg':
                                        weight = 1000
                                    elif data['uom'].lower() == 'litre' or data['uom'].lower() == 'ltr':
                                        weight = 1000
                                    elif data['uom'].lower() == 'nos' or data['uom'].lower() == 'mtr':
                                        weight = 1
                                    else:
                                        continue
                                    if data['otherPackages'] is None:
                                        data['otherPackages'] = 0
                                    if not isinstance(data['orderedPackages'], (int, float)):
                                        data['orderedPackages'] = 0
                                    sv = next((x for x in stockDict[restaurantId] if x['itemCode'] == itemCode and x['packageName'] == data['pkgName']), None)
                                    if sv:
                                        data['pkgQty'] = sv['packageQty']
                                    if searchKey not in userDict[restaurantId][workArea].keys():
                                        userDict[restaurantId][workArea][searchKey] = 0
                                        if requestedCategory == 0:
                                            userDict[restaurantId][workArea][searchKey] += ((data['orderedPackages'] * data['pkgQty'])* weight)
                                            if data['uom'].lower() == 'litre' or data['uom'].lower() == 'ltr':
                                                userDict[restaurantId][workArea][searchKey] += checkBottleWeight(data)
                                            else:
                                                userDict[restaurantId][workArea][searchKey] += ((data['otherPackages'])* weight)
                                        elif requestedCategory == 2:
                                            if data['uom'].lower() == 'litre' or data['uom'].lower() == 'ltr':
                                                userDict[restaurantId][workArea][searchKey] = {
                                                    'open' :(checkBottleWeight(data)/1000),
                                                    "package" : data['orderedPackages']
                                                }
                                            else:
                                                userDict[restaurantId][workArea][searchKey] = {
                                                    'open' : (data['otherPackages']),
                                                    "package" : data['orderedPackages']
                                                }
                                        else:
                                            userDict[restaurantId][workArea][searchKey] += (data['orderedPackages'] * data['pkgQty'])
                                            if data['uom'].lower() == 'litre' or data['uom'].lower() == 'ltr':
                                                userDict[restaurantId][workArea][searchKey] += (checkBottleWeight(data)/1000)
                                            else:
                                                userDict[restaurantId][workArea][searchKey] += (data['otherPackages'])
                            else:
                                data = entry['workAreas'][workArea]['category'][category]['subCategory'][subCategory]['items'][itemCode]
                                if data['closingStock'] is None:
                                    data['closingStock'] = 0
                                searchKey = itemCode + '|' + 'N/A' +'|'+ 'N/A'
                                if searchKey not in userDict[restaurantId][workArea].keys():
                                    userDict[restaurantId][workArea][searchKey] = 0
                                    if requestedCategory == 0:
                                        userDict[restaurantId][workArea][searchKey] += (data['closingStock'] * 1000)
                                    elif requestedCategory == 2:
                                        userDict[restaurantId][workArea][searchKey] ={"open" : data['closingStock'] }
                                    else:
                                        userDict[restaurantId][workArea][searchKey] += data['closingStock']
    res={}
    if qtyWise:
        for restId in userDict.keys():
            res[restId] ={}
            for wa in userDict[restId]:
                for updatedSearchKey in userDict[restId][wa].keys():
                    if updatedSearchKey not in res[restId].keys():
                        res[restId][updatedSearchKey] = userDict[restId][wa][searchKey]
                    else:
                        res[restId][updatedSearchKey] += userDict[restId][wa][searchKey]
    elif requestedCategory == 2:
        for restId in userDict.keys():
            res[restId] ={}
            for wa in userDict[restId]:
                if wa not in res[restId].keys():
                    res[restId][wa] ={}
                for searchKey in userDict[restId][wa].keys():
                    itemCode, uom , pkgName = searchKey.split("|")
                    if itemCode not in res[restId][wa].keys():
                        res[restId][wa][itemCode] = userDict[restId][wa][searchKey]['open']
                    else:
                        res[restId][wa][itemCode] += userDict[restId][wa][searchKey]['open']
        return [res, userDict]
    else:
        for restId in userDict.keys():
            res[restId] ={}
            for wa in userDict[restId]:
                for searchKey in userDict[restId][wa].keys():
                    itemCode, uom , pkgName = searchKey.split("|")
                    updatedSearchKey = itemCode+"|" + uom.upper()
                    if updatedSearchKey not in res[restId].keys():
                        res[restId][updatedSearchKey] = userDict[restId][wa][searchKey]
                    else:
                        res[restId][updatedSearchKey] += userDict[restId][wa][searchKey]
    return res
##############################################
def getExternalSalesData(fromDate, toDate, APIKeys):
    final ={}
    for APIKey in APIKeys:
        url = "http://stockapi.provargocloud.com/api/Sales/GetCloudSalesData"
        headers = {'Content-Type': 'application/json'}
        payload = json.dumps({
            "From": fromDate,
            "To": toDate,
            "DeptKey": None,
            "APIKey": APIKey,
            "RID": "TEST1"
        })
        response = requests.request("GET", url, headers=headers, data=payload)
        data = json.loads(response.text)
        if "ProductSales" not in data.keys():
            continue
        for item in data["ProductSales"]:
            if item["ExternalPLUNo"] in final.keys():
                final[item["ExternalPLUNo"]]['Cloudcount'] += item['Cloudcount']
                final[item["ExternalPLUNo"]]['CloudVolume'] += item['CloudVolume']
            else:
                final[item["ExternalPLUNo"]]={
                    "PLUNo": item['PLUNo'],
                    "Cloudcount": item['Cloudcount'],
                    "CloudVolume": item['CloudVolume']
                }
    return final

def adjustWidth(df, writer, sheetName):
    max_width = 30
    wrap_text_format = writer.book.add_format({'text_wrap': True})
    for column in df:
        content_max_length = max(df[column].astype(str).map(len).max(), len(column))
        column_length = min(content_max_length, max_width) + 2
        col_idx = df.columns.get_loc(column)
        writer.sheets[sheetName].set_column(col_idx, col_idx, column_length)

def createHeader(workbook, sheet, job, tenantName, reportHeaderName, CONSOLIDATED = False,baseDateValue=''):
    head_format = workbook.add_format({'border': 5,"bold": True,"valign": "vcenter",'font_size' : '13',"align" :"center"})
    createdTime = str(job['createTs'])
    parsed_time = datetime.datetime.strptime(createdTime, "%Y-%m-%d %H:%M:%S")
    formatted_time = parsed_time.strftime("%d-%m-%Y/%I:%M_%p")
    reportList = reportLists.find_one({'tenantId': job['tenantId']})
    if reportList is not None:
        requiredObject = next((el for el in reportList['types'] if el['backendName'] == job['details']['type']), None)
        if requiredObject :
            reqHeader = requiredObject['displayName']
        else :
            reqHeader = reportHeaderName
        reportName = reqHeader
    else :
        reportName = reportHeaderName
    if "startDate" in job['details'] and "endDate" in job['details'] \
            and job['details']['startDate'] is not None and job['details']['endDate'] is not None:
        sheet.merge_range('A1:H1', tenantName.upper(), head_format)
        sheet.merge_range('A2:H2', f"{reportName.upper()}", head_format)
        if CONSOLIDATED :
            baseDate = 'Based On GRN Date(System Entry Date)'
            if reportHeaderName == 'CONSOLIDATED ISSUE' :
                if 'selectedBaseDateForIndent' in job['details'] and job['details']['selectedBaseDateForIndent'] == 'modDate':
                    baseDate = 'Based On Issue Date'
                elif 'selectedBaseDateForIndent' in job['details'] and job['details']['selectedBaseDateForIndent'] == 'documentDate':
                    baseDate = 'Based On Indent Date'
                else :
                    baseDate = 'Based On Requested Date'
            else :
                if 'selectedBaseDate' in job['details'] and job['details']['selectedBaseDate'] == 'grnDate':
                    baseDate = 'Based on GRN Date(Goods Received Date)'
                elif 'selectedBaseDate' in job['details'] and job['details']['selectedBaseDate'] == 'invoiceDate':
                    baseDate = 'Based on Vendor Invoice Date'
                else :
                    baseDate = 'Based On GRN Date(System Entry Date)'
            sheet.merge_range('A3:H3', f"{'Generated On : '+ formatted_time + '|' + job['details']['startDate'].strftime('%d-%b-%Y')} to {job['details']['endDate'].strftime('%d-%b-%Y')+ '|' + baseDate }", head_format)
        else :
            sheet.merge_range('A3:H3', f"{'Generated On : '+ formatted_time + '|' + job['details']['startDate'].strftime('%d-%b-%Y')} to {job['details']['endDate'].strftime('%d-%b-%Y')+ baseDateValue}", head_format)
    elif "startDate" in job['details'] and job['details']['startDate'] is not None:
        closingDate = ''
        if job['details']['type'] == 'closingReport' or job['details']['type'] == 'systemClosingReport' :
            closingDate = '(Closing Date)'
        sheet.merge_range('A1:H1', tenantName.upper(), head_format)
        sheet.merge_range('A2:H2', f"{reportName.upper()}", head_format)
        sheet.merge_range('A3:H3', f"{'Generated On : '+ formatted_time + '|' + job['details']['startDate'].strftime('%d-%b-%Y')+closingDate}", head_format)
    else:
        sheet.merge_range('A1:H1', tenantName.upper(), head_format)
        sheet.merge_range('A2:H2', f"{reportName.upper()}", head_format)
        sheet.merge_range('A3:H3', f"{'Generated On : '+ formatted_time}", head_format)

def getIngredientCost(restaurantId ,itemCode, uom ):
    stockFilter ={}
    stockFilter['restaurantId'] = restaurantId
    stockFilter['itemCode'] = itemCode
    stockFilter['entryType'] = 'open'
    if uom.lower() == 'kg':
        weight = 1000
    elif uom.lower() == 'litre' or uom.lower() == 'ltr':
        weight = 1000
    elif uom.lower() == 'nos':
        stockFilter['entryType'] = 'package'
        weight = 1
    priceDec = Stockvalues.find_one(stockFilter)
    if priceDec :
        if "uom" in priceDec.keys() and priceDec['uom'].upper() == "NOS":
            priceDec['withTaxPrice'] = priceDec['withTaxPrice'] / priceDec['packageQty']
        return ut.truncate_and_floor(priceDec['withTaxPrice'] /weight, 3)
    else:
        return 0

def create_header(workbook, sheet, selectedRestaurants, startDate, endDate):
    branch = list(branchesCol.find({'restaurantIdOld': selectedRestaurants[0]}))
    tenant_name = branch[0]['tenantName']
    allBranch = ""
    for i in selectedRestaurants:
        branchs = i.split('@')[1]
        allBranch += branchs + " "
    merge_format = workbook.add_format({'bold': 1,'border': 1,'align': 'center','valign': 'vcenter'})
    sheet.merge_range('A2:I2', tenant_name+' - '+allBranch, merge_format)
    if startDate != "":
        sheet.merge_range('A3:I3', str(startDate) + " - " +str(endDate), merge_format)


def accuracy_header(workbook, sheet, medianDf):
    headingFormat = workbook.add_format({
        'bold': 1,
        'border': 1,
        'align': 'center',
        'valign': 'vcenter'
    })
    dataFormat = workbook.add_format({
        'align': 'center',
        'valign': 'vcenter'
    })
    sheet.write(4, 1, "Category", headingFormat)
    sheet.write(4, 2, "Median", headingFormat)
    sheet.write(4, 3, "Accuracy", headingFormat)
    for i in range(len(medianDf)):
        sheet.write(5+i, 1, medianDf['category'][i], dataFormat)
        sheet.write(5+i, 2, abs(medianDf['deviation'][i]), dataFormat)
        sheet.write(5+i, 3, 100 - abs(medianDf['deviation'][i]), dataFormat)


def contains(input, filter):
    for x in input:
        if filter(x):
            return x
    return None

def checkBottleWeight(data):
    if((data['emptyBottleWeight'] is None) or (data['emptyBottleWeight'] == 0) or (data['fullBottleWeight'] is None) or (data['fullBottleWeight'] == 0)):
        if data['uom'].lower() == 'litre' or data['uom'].lower() == 'ltr':
            if data['otherPackages'] is None:
                data['otherPackages'] =0
            return data['otherPackages'] * 1000
        else:
            return 0
    else:
        return (float(data['cnvtdOtherPackages'] or 0))


def convert_datetime_to_date(dt_obj):
    if isinstance(dt_obj, datetime.datetime):
        return dt_obj.date()
    return None

def bold_last(row):
    color = 'white'
    if row.values[-1] == 'Total':
        color = 'yellow'
    return ['background-color: %s' % color]*len(row.values)

def consolidatedSalesData(salesData):
    salesDict = {}
    for entry in salesData:
        for saleData in entry['items']:
            itemCode = saleData['itemCode']
            itemName = saleData['itemName']
            servingSize = saleData['servingSize']
            quantity = saleData['quantity']
            restaurantId = entry['restaurantId']
            tenantId = entry['tenantId']
            tup = (itemCode, servingSize)
            if tup in salesDict:
                salesDict[tup]['actual'] += quantity
            else:
                restEntry = Stockvalues.find_one({"restaurantId": restaurantId, "itemCode": itemCode})
                if restEntry :
                    category = restEntry['category']
                    subCategory = restEntry['subCategory']
                else:
                    category = "N/A"
                    subCategory = "N/A"
                salesDict[tup] = {
                    'itemCode': itemCode + "|" + servingSize,
                    'actual': quantity,
                    'category': category,
                    'subCategory': subCategory,
                    'itemName': itemName
                }
    return salesDict


def calculateMetricsData(metricsData):
    consolidatedMetricsData = {}
    for entry in metricsData:
        for predictData in entry['items']:
            itemCode = predictData
            quantity = entry['items'][predictData]['predicted']
            tup = (itemCode)
            if tup in consolidatedMetricsData:
                consolidatedMetricsData[tup]['predicted'] += quantity
            else:
                consolidatedMetricsData[tup] = {
                    'itemCode': itemCode,
                    'predicted': quantity,
                }
    return consolidatedMetricsData

def fetch_extra(row, stockDict):
    if row['restaurantId'] in stockDict:
        sv = next((x for x in stockDict[row['restaurantId']] if x['itemCode'] == row['itemCode']), None)
        if sv:
            category = sv['category']
            subCategory = sv['subCategory']
            itemName = sv['itemName']
            return pd.Series([category, subCategory, itemName])
    return pd.Series(['N/A', 'N/A', 'N/A'])

def get_menu_value(dictionary, restaurant_id, search_key, modifier_key=None):
    if restaurant_id in dictionary and search_key in dictionary[restaurant_id]:
        if modifier_key and modifier_key in dictionary[restaurant_id][search_key]['modifier']:
            return dictionary[restaurant_id][search_key]['non_modifier'], dictionary[restaurant_id][search_key]['modifier'][modifier_key]
        else:
            return dictionary[restaurant_id][search_key]['non_modifier'], 0
    else:
        return 0.1, 0


def get_pr_approval_status(element):
    if element.get('approvalDetail') and len(element['approvalDetail']) > 0:
        data = element['approvalDetail']
        if len(data) != 0:
            level_order = [item['level'] for item in data]
            status_with_role = []
            for current_level in level_order:
                matching_data = next((item for item in data if item['level'] == current_level), None)
                if matching_data:
                    level = matching_data['level']
                    status = matching_data['status']
                    role = matching_data['role']

                    if status == "rejected":
                        status_with_role = [status.capitalize(), role]
                        break
                    elif status == "pending" and "rejected" not in status_with_role:
                        status_with_role = [status.capitalize(), role]
                    elif status == "approved" and "rejected" not in status_with_role and "pending" not in status_with_role:
                        status_with_role = [status.capitalize(), role]

            return status_with_role
    else:
        return ['Auto Approved', 'Auto']

def format_time_to_ampm(time_str):
    time_obj = datetime.datetime.strptime(time_str, "%H:%M:%S")
    return time_obj.strftime("%I:%M %p")

def getPOSToken(tenantId):
    tenant = roloposconfigsCol.find_one({'tenantId': tenantId})
    if tenant:
        base_url = tenant['url'] if tenant['url'] else 'https://rms-api.digitory.com'
        priceTier_id = tenant.get('defaultPriceTier',0)
        parsed_base_url = urllib.parse.urlparse(base_url)
        headers = {"Content-Type": "application/json", "App-Id": "inventory"}
        payload = {
            "emailID": tenant['emailId'],
            "password": tenant['password']
        }
        response = requests.post(urllib.parse.urljoin(parsed_base_url.geturl(), '/login'), headers=headers, json=payload)
        if response.status_code == 200:
            response_json = response.json()
            return {
                'success': True,
                'base_url': base_url,
                'priceTier_id': priceTier_id,
                'response': response_json
            }
        else:
            return {
                'success': False,
                'message': 'Failed to obtain access token'
            }
    else:
        return {
            'success': False,
            'message': 'Account setup is not done'
        }

def getPOSMenuPriceById(resp,priceTier):
    if resp['success']:
        if 'loggedInEmployee' in resp['response']:
            id = resp['priceTier_id']
            base_url = resp['base_url']
            account_id = resp['response']['loggedInEmployee']['accountID']
            access_token = resp['response']['loggedInEmployee']['token']
            url = f"{base_url}/account/{account_id}/tieredPriceGroup/{priceTier}/getPrices"
            response = requests.get(url, headers={'Authorization': f'Bearer {access_token}'})
            if response.status_code == 200:
                return json.loads(response.text)
            else:
                return []
        else:
            return []
    else:
        return {'success': False, 'message': 'Failed to obtain access token'}

def getPOSMenuItems(resp, priceTier,tenantId):
    if resp['success']:
        if 'loggedInEmployee' in resp['response']:
            account_id = resp['response']['loggedInEmployee']['accountID']
            base_url = resp['base_url']
            url = f"{base_url}/api/v1/external/accounts/{account_id}/tieredPriceGroup/{priceTier}/menuItems"
            headers = {
                "App-Id": "inventory",
                "App-Code": "2F9EE361-D254-41C5-8541-969D1EBAC2BE"
            } if tenantId != '100001' else {
                "App-Id": "staging-inventory",
                "App-Code": "1E5142F7-D762-4B50-95EF-4A0C6FF7B93C"
            }
            response = requests.post(url, headers=headers)

            if response.status_code == 200:
                return response.json()
            else:
                return {'success': False, 'message': 'Failed to fetch menu items', 'status_code': response.status_code}
        else:
            return {'success': False, 'message': 'Invalid response structure'}
    else:
        return {'success': False, 'message': 'Failed to obtain access token'}


def getPOSModifiers(resp):
    if resp['success']:
        if 'loggedInEmployee' in resp['response']:
            base_url = resp['base_url']
            account_id = resp['response']['loggedInEmployee']['accountID']
            access_token = resp['response']['loggedInEmployee']['token']
            url = f"{base_url}/accounts/{account_id}/modifiers"
            response = requests.get(url, headers={'Authorization': f'Bearer {access_token}'})
            if response.status_code == 200:
                return json.loads(response.text)
            else:
                return []
        else:
            return []
    else:
        return {'success': False, 'message': 'Failed to obtain access token'}

def getPOSModifierPriceById(resp):
    if resp['success']:
        if 'loggedInEmployee' in resp['response']:
            base_url = resp['base_url']
            account_id = resp['response']['loggedInEmployee']['accountID']
            access_token = resp['response']['loggedInEmployee']['token']
            url = f"{base_url}/account/{account_id}/modifiers"
            response = requests.get(url, headers={'Authorization': f'Bearer {access_token}'})
            if response.status_code == 200:
                return json.loads(response.text)
            else:
                return []
        else:
            return []
    else:
        return {'success': False, 'message': 'Failed to obtain access token'}

def posSellingPrice(tenantId,priceTier):
    client_data = getPOSToken(tenantId)
    selling_price = []
    if client_data['success']:
        for el in getPOSMenuItems(client_data,priceTier,tenantId):
            if len(el.get('prices', {})) != 0:
                    for ss in el.get('servingSizes', []):
                        obj = {}
                        obj['menuItemName'] = f"{el['pluCode']}|{ss['name']}"
                        if str(ss['id']) in el['prices']:
                            obj['price'] = el['prices'][str(ss['id'])]['price'] / 100
                            if 'modifiers' in el['prices'][str(ss['id'])] and len(el['prices'][str(ss['id'])]['modifiers']) > 0:
                                for mod in el['prices'][str(ss['id'])]['modifiers']:
                                    obj[f"{mod['name']}|{ss['name']}"] = float(mod['priceStr'].replace(',', ''))
                        else:
                            obj['price'] = 0
                        selling_price.append(obj)
    return selling_price


def posModifiers(tenantId):
    client_data = getPOSToken(tenantId)
    modifierList = []
    if client_data['success']:
        for item in getPOSModifiers(client_data):
            obj = {
                "name" : item['name'],
                "id" : item['id']
            }
            modifierList.append(obj)
    return modifierList


# Convert ml to bottle and remaining ml
def full_open_ml_to_bottle(store_full, store_open, pkgQty):
    store_full += math.floor(store_open / pkgQty)
    remaining_ml = store_open % pkgQty
    return [store_full, remaining_ml]


def prStatusReport(job):
    startDate = job['details']['startDate'].replace(hour=0, minute=0, second=0)
    endDate = job['details']['endDate'].replace(hour=23, minute=59, second=59)
    prSearchFilter = {
        'restaurantId': {'$in': job['details']['selectedRestaurants']},
        'createTs' :{ '$gte': startDate, '$lte': endDate},
        'subPr' : False
    }
    prList = list(purchaserequestsCol.find(prSearchFilter))
    location1 = getLocation(job['tenantId'])
    finalList = []
    for pr in prList:
        temp ={}
        app = get_pr_approval_status(pr)
        prStatus = ""
        wa = ""
        if pr['isPoCreated']:
            prStatus = "COMPLETED"
        else:
            prStatus = "PENDING"

        if (('selectedWorkArea' in pr.keys()) and (pr['selectedWorkArea']) and (len(pr['selectedWorkArea'])>0)) :
            wa = pr['selectedWorkArea'][0]
        else:
            wa = "STORE"
        temp={
            'Location' : location1[pr['restaurantId']],
            'Created By': pr['creator'],
            'Workarea' : wa,
            'PR ID' : pr['prId'],
            'Created Date' : pr['createTs'].date(),
            'Created Time': format_time_to_ampm(pr['createTs'].strftime("%H:%M:%S")),
            'Delivery Date': (pr['deliveryDate'].date() if (pr.get('deliveryDate') is not None) else pr['eta'].date()) if ('deliveryDate' in pr) else pr['eta'].date(),
            'Approver': app[1],
            'Approval Status': app[0],
            'Req. Status': prStatus,
            'Amount' : pr.get('totalAmount', 'N/A')
        }
        finalList.append(temp)

    df = pd.DataFrame(finalList)
    if len(df) > 0:
        df = df[['Location', 'Created By', 'Workarea' ,'PR ID', 'Created Date','Created Time', 'Delivery Date', 'Approver' ,'Approval Status', 'Req. Status', 'Amount']]
        if not df.empty:
            df.insert(0, 'S.No', range(1, len(df) + 1))
            totals = df[['Amount']].sum()
            df = df._append(pd.Series(['Total',
                (totals['Amount'])
            ], index=['S.No','Amount']), ignore_index=True)
        if len(df)>0 and 'requiredColumns' in job['details']:
            df = df[job['details']['requiredColumns']]
    else:
        df = pd.DataFrame()
    return df

def poStatusReport(job, delete=False):
    tenantEntry = tenantsCol.find_one({'tenantId': job['tenantId']})
    startDate = job['details']['startDate'].replace(hour=0, minute=0, second=0)
    endDate = job['details']['endDate'].replace(hour=23, minute=59, second=59)
    vendorDict = {}
    for vendor in tenantEntry['vendors']:
        vendorDict[vendor['vendorId']] = vendor['vendorName']
    location1 = getLocation(job['tenantId'])
    poSearchFilter = {
        'restaurantId': {'$in': job['details']['selectedRestaurants']},
        'vendorId' :  { '$in': job['details']['selectedVendors']},
        'createTs' :{ '$gte': startDate, '$lte': endDate}
    }
    if delete:
        poList = list(deletedposCol.find(poSearchFilter))
    else:
        poList = list(purchaseordersCol.find(poSearchFilter))
    finalList = []
    for po in poList:
        if len(po['grns']) > 0:
            for index, grn in enumerate(po['grns']):
                temp ={}
                temp={
                    'Location' : location1[po['restaurantId']],
                    'Vendor ID': po['vendorId'],
                    'Vendor Name': vendorDict[po['vendorId']] if po['vendorId'] in vendorDict else 'Vendor Not Found',
                    'PO ID' : po['poId'],
                    'PO Created By' : po['creator'] if 'creator' in po else '-',
                    'PO Created Date' : po['createTs'].date(),
                    'PO Created Time' : format_time_to_ampm(po['createTs'].strftime("%H:%M:%S")),
                    'Delivery Date': po['validityDate'].date(),
                    'GRN ID': grn['grnId'],
                    'GRN Date': grn['createTs'].date(),
                    'PO Status': "CLOSED" if po['status']['orderStatus'] == 'closed' else po['statusHistory'][index+1]['orderStatus'].upper()
                }
                finalList.append(temp)
        else:
            temp ={}
            temp = {
                'Location': location1[po['restaurantId']],
                'Vendor ID': po['vendorId'],
                'Vendor Name': vendorDict.get(po['vendorId'], 'Vendor Not Found'),
                'PO ID': po['poId'],
                'PO Created By' : po['creator'] if 'creator' in po else '-',
                'PO Created Date': po['createTs'].date(),
                'PO Created Time' : format_time_to_ampm(po['createTs'].strftime("%H:%M:%S")),
                'Delivery Date': po['validityDate'].date(),
                'GRN ID': 'NOT YET RECEIVED',
                'GRN Date': 'NOT YET RECEIVED',
                'PO Status': "CLOSED" if po['status']['orderStatus'] == 'closed' else 'PENDING'
            }
            finalList.append(temp)

    df = pd.DataFrame(finalList)
    if len(df) > 0:
        df = df[['Location', 'Vendor ID', 'Vendor Name', 'PO ID','PO Created By','PO Created Date','PO Created Time', 'Delivery Date', 'GRN ID', 'GRN Date', 'PO Status']]
        if not df.empty:
            df.insert(0, 'S.No', range(1, len(df) + 1))
            if 'requiredColumns' in job['details']:
                df = df[job['details']['requiredColumns']]
    else:
        df = pd.DataFrame()
    return df

def rateVarianceReport(job):
    stockDict ={}
    selectedRids = job['details']['selectedRestaurants']
    base_year = job['details']['startYear']
    base_month = job['details']['startMonth']
    date_object = datetime.datetime(base_year, base_month, 1)
    location1 = getLocation(job['tenantId'])
    stockDict ={}
    for rId in selectedRids:
        getStockDict(rId, stockDict)
    Wac = list(weightedaveragesCol.find(
        {'restaurantId' :{"$in" : selectedRids},"date":{"$gte": date_object}},
        {"_id": 0, "items" : 0, "modAt" : 0, "createdAt": 0, "date" :0, "tenantId" : 0,
         "itemName": 0},sort=[('year', -1),('month', -1)])
    )
    df = pd.DataFrame(Wac)
    base_prices = df[(df['year'] == base_year) & (df['month'] == base_month)].set_index(['restaurantId', 'itemCode', 'packageName'])['weightedAverage']
    df = df.merge(base_prices, on=['restaurantId', 'itemCode', 'packageName'], suffixes=('', '_Base'))
    df['Rate_Variance_Value'] = df['weightedAverage'] - df['weightedAverage_Base']
    df['Rate_Variance_Percentage'] = (df['Rate_Variance_Value'] /  df['weightedAverage_Base']) * 100
    unique_months = df[['month', 'year']].drop_duplicates().apply(lambda x: f"{x['month']} {x['year']}", axis=1).tolist()
    new_avg_price_columns = []
    new_deviation_columns = []
    new_percentage_columns = []
    for month_year in unique_months:
        month, year = month_year.split()
        month_abbr = calendar.month_abbr[int(month)]
        new_avg_price_columns.append(f'{month_abbr} {int(year)} AVG. PRICE')
        new_deviation_columns.append(f'{month_abbr} {int(year)} DEVIATION')
        new_percentage_columns.append(f'{month_abbr} {int(year)} DEVIATION (%)')
    pivot_avg_price = df.pivot_table(index=['restaurantId', 'itemCode', 'packageName'],
        columns=['month', 'year'],
        values='weightedAverage',
        aggfunc='first',
        sort=False)
    pivot_deviation = df.pivot_table(index=['restaurantId', 'itemCode', 'packageName'],
        columns=['month', 'year'],
        values='Rate_Variance_Value',
        aggfunc='first',
        sort=False)
    pivot_percentage = df.pivot_table(index=['restaurantId', 'itemCode', 'packageName'],
        columns=['month', 'year'],
        values='Rate_Variance_Percentage',
        aggfunc='first',
        sort=False)

    pivot_avg_price = pivot_avg_price[pivot_avg_price.columns]
    pivot_deviation = pivot_deviation[pivot_deviation.columns]
    pivot_percentage = pivot_percentage[pivot_percentage.columns]
    pivot_avg_price.columns = new_avg_price_columns
    pivot_deviation.columns = new_deviation_columns
    pivot_percentage.columns = new_percentage_columns
    final_df = pd.concat([pivot_avg_price, pivot_deviation, pivot_percentage], axis=1)
    final_df.reset_index(inplace=True)
    final_df[['category', 'subcategory', 'itemName']] = final_df.apply(lambda row: fetch_extra(row, stockDict), axis=1)
    def tran(val):
        return location1[val]
    final_df['restaurantId'] =  df['restaurantId'].apply(tran)
    desired_columns = ['restaurantId', 'category','subcategory' ,'itemCode', 'itemName', 'packageName']
    for month_year in unique_months:
        month, year = month_year.split()
        month = int(month)
        month_abbr = calendar.month_abbr[month]
        avg_price_col = f'{month_abbr} {int(year)} AVG. PRICE'
        if month == base_month and int(year) == base_year:
            desired_columns.insert(6,f'{avg_price_col} (BASE)')
        else:
            desired_columns.append(avg_price_col)
        if month == base_month and int(year) == base_year:
            pass
        else:
            desired_columns.append(f'{month_abbr} {int(year)} DEVIATION')
            desired_columns.append(f'{month_abbr} {int(year)} DEVIATION (%)')
    base_month_abbr = calendar.month_abbr[base_month]
    col = f'{base_month_abbr} {base_year} AVG. PRICE'
    if col in final_df.columns:
        final_df[f'{base_month_abbr} {base_year} AVG. PRICE (BASE)'] = final_df[col]
    else:
        final_df[f'{base_month_abbr} {base_year} AVG. PRICE (BASE)'] = 0

    final_df = final_df[desired_columns]
    final_df.rename(columns={
        'restaurantId': 'Location',
        'category': 'Category',
        'subcategory': 'Sub Category',
        'itemCode': 'Item Code',
        'itemName': 'Item Name',
        'packageName': 'Package Name',
    }, inplace=True)
    df = final_df.round(2)

    # Group by location and add serial numbers
    dataframes = {}
    for location, group_df in df.groupby('Location'):
        if not group_df.empty:
            group_df = group_df.copy()
            group_df.insert(0, 'S.No', range(1, len(group_df) + 1))
            dataframes[location] = group_df

    return dataframes

def consolidated_purchase_indent(job):
    _category = [str(category).lower() for category in job['details']['selectedCategories'] if not str(category).isnumeric()]
    _subCategory = [str(subCategory).lower() for subCategory in job['details']['selectedSubCategories'] if not str(subCategory).isnumeric()]
    selectedRestaurants = job['details']['selectedRestaurants']
    invSearchFilter = {
        'ItemType' : 'Inventory',
        'restaurantId' : {'$in': selectedRestaurants}
    }
    stockDict = {}
    purchaseList = []
    indentList = []
    location1 = getLocation(job['tenantId'])
    ########################## prerequists #############################
    startTime = job['details']['startDate'].replace(hour=0, minute=0, second=0)
    endTime = job['details']['endDate'].replace(hour=23, minute=59, second=59)
    grnDict = getGrnDict(job, startTime, endTime, qtyWise=True)
    getRates = get_purchase_rates(selectedRestaurants, endTime)

    indentDict, _sr = getIndentDict(job, startTime, endTime, qtyWise= True, waWise= True)
    for item in Stockvalues.find(invSearchFilter):
        if item['category'].lower() not in _category and ("all" not in _category) :
            continue
        if item['subCategory'].lower() not in _subCategory and ("all" not in _subCategory):
            continue
        itemCode = item['itemCode']
        location = location1[item['restaurantId']]
        restaurantId = item['restaurantId']
        getStockDict(restaurantId, stockDict)
        item['entryType'] = item.get('entryType') if item.get('entryType', None) is not None else 'N/A'
        item['packageName'] = item.get('packageName') if item.get('packageName', None) is not None else 'N/A'
        item['uom'] = item.get('uom', 'N/A')
        searchKey = itemCode + '|' + item['entryType'].upper() +  '|' + item['packageName'].upper()

        hsn ='N/A'
        sv = next((x for x in stockDict[restaurantId] if x['itemCode'] == itemCode and x['packageName'] == item['packageName']), None)
        if sv:
            hsn =sv['hsnCode']

        ######################### GRN ########################
        if restaurantId in grnDict:
            if searchKey in grnDict[restaurantId]:
                rateKey= itemCode+'|'+item['packageName']
                qty = grnDict[restaurantId][searchKey]['received']
                other_charges = getRates[restaurantId].get(rateKey, {'extraCharges' : 0}).get('extraCharges')
                disc_charges = getRates[restaurantId].get(rateKey, {'discCharges' : 0}).get('discCharges')
                cess_charges = getRates[restaurantId].get(rateKey, {'cessCharges' : 0}).get('cessCharges')
                monthWac = getRates[restaurantId].get(rateKey, {'monthWac' : 0}).get('monthWac')
                purchaseList.append({
                    'Location' : location,
                    'Category': item['category'],
                    'Sub Category': item['subCategory'],
                    'Item Code': itemCode,
                    'HSN/SAC': hsn,
                    'Item Name': item['itemName'],
                    'Package Name': item['packageName'],
                    'WAC(incl.tax,etc)': ut.truncate_and_floor(monthWac,2),
                    'Average Purchase Cost' : ut.truncate_and_floor(grnDict[restaurantId][searchKey]['total(excl.tax)'] / grnDict[restaurantId][searchKey]['received'],3),
                    'Ordered Qty':  grnDict[restaurantId][searchKey]['ordered'],
                    'Received Qty': grnDict[restaurantId][searchKey]['received'],
                    'Total(excl.tax)' : grnDict[restaurantId][searchKey]['total(excl.tax)'],
                    'Tax Amount' : grnDict[restaurantId][searchKey]['taxAmount'],
                    'Other Charges' : ut.truncate_and_floor((other_charges * qty),3),
                    'Discount' : ut.truncate_and_floor((disc_charges * qty),3),
                    'Cess' : ut.truncate_and_floor((cess_charges * qty),3),
                    'Total(incl.tax,etc)' : grnDict[restaurantId][searchKey]['total(incl.tax)']
                })

        ########################## indent ##########################
        if restaurantId in indentDict:
            if searchKey in indentDict[restaurantId]:
                for workArea in indentDict[restaurantId][searchKey].keys():
                    indentDict[restaurantId][searchKey][workArea]['completed'] = True
                    indentReqQty = indentDict[restaurantId][searchKey][workArea]['requested']
                    indentIssuedQty = indentDict[restaurantId][searchKey][workArea]['issued']
                    if indentIssuedQty <=0:
                        continue
                    indentPrice = indentDict[restaurantId][searchKey][workArea]['unitPrice'] / indentIssuedQty
                    rateKey= itemCode+'|'+item['packageName']
                    monthWac = getRates[restaurantId].get(rateKey, {'monthWac' : 0}).get('monthWac')
                    indentList.append({
                        'Location': location,
                        'WorkArea': workArea,
                        'Category': item['category'],
                        'Sub Category': item['subCategory'],
                        'Item Code': itemCode,
                        'HSN/SAC': hsn,
                        'Item Name': item['itemName'],
                        'Package Name': item['packageName'],
                        'WAC(incl.tax,etc)': ut.truncate_and_floor(monthWac,2),
                        'Average Purchase Cost': ut.truncate_and_floor(indentPrice ,3),
                        'Requested Qty': ut.truncate_and_floor(indentReqQty, 3),
                        'Issued Qty': ut.truncate_and_floor(indentIssuedQty, 3),
                        'Total(incl.tax,etc)': ut.truncate_and_floor(indentIssuedQty * indentPrice, 3)
                    })

    df1 = pd.DataFrame(purchaseList)
    if len(df1) >0:
        df1 = df1[df1['Received Qty'] > 0]
        df1 = df1.sort_values(by='Category', ascending=True)

    df2 = pd.DataFrame(indentList)
    if len(df2) > 0:
        df2 = df2[df2['Issued Qty'] > 0]
        df2 = df2.sort_values(by='Category', ascending=True)

    dataframes = { "CONSOLIDATED PURCHASE" : df1, "CONSOLIDATED ISSUE" : df2 }

    # Process dataframes to add totals and serial numbers
    for sheet_name, df in dataframes.items():
        if not df.empty:
            totals = df[['Total(incl.tax,etc)']].sum()
            df.insert(0, 'S.No', range(1, len(df) + 1))
            df = df._append(pd.Series(['Total',
                (totals['Total(incl.tax,etc)'])
            ], index=['S.No','Total(incl.tax,etc)']), ignore_index=True)
            if sheet_name=="CONSOLIDATED PURCHASE":
                if len(df)>0 and 'requiredColumnsForGRN' in job['details']:
                    df = df[job['details']['requiredColumnsForGRN']]
            else:
                if len(df)>0 and 'requiredColumnsForIndent' in job['details']:
                    df = df[job['details']['requiredColumnsForIndent']]
            dataframes[sheet_name] = df

    return dataframes

def store_variance(job):
    _category = [str(category).lower() for category in job['details']['selectedCategories'] if not str(category).isnumeric()]
    _subCategory = [str(subCategory).lower() for subCategory in job['details']['selectedSubCategories'] if not str(subCategory).isnumeric()]
    selectedRestaurants = job['details']['selectedRestaurants']

    invSearchFilter = {
        'status': {"$ne" :"discontinued"},
        'ItemType' : {'$in': ['Inventory','SubRecipe']},
        'restaurantId' : {'$in': selectedRestaurants}
    }

    invList = []
    openingSystemDict = {}
    closingSystemDict = {}
    location1 = getLocation(job['tenantId'])
    ########################## prerequists #############################
    startTime = job['details']['startDate'].replace(hour=0, minute=0, second=0)
    endTime = job['details']['endDate'].replace(hour=23, minute=59, second=59)
    grnDict = getGrnDict(job, startTime, endTime, qtyWise=True)
    rtsDict = getRTS(job, startTime, endTime)
    getRates = get_purchase_rates(selectedRestaurants, endTime)
    indentDict, _sr = getIndentDict(job, startTime, endTime, qtyWise=True)
    ibtInDict, ibtOutDict = getIbtDict(job, startTime, endTime, qtyWise=True)
    spoilageDict = getSpoilageDictStore(job, startTime, endTime, qtyWise=True)
    openDictStore, closingUserStoreDict = getUserDictStore(job, 'closing', 2)
    openDictStoreOpen, userStoreDictOpen = getUserDictStore(job, 'opening', 2)

    for item in Stockvalues.find(invSearchFilter):
        if item['category'].lower() not in _category and ("all" not in _category):
            continue
        if item['subCategory'].lower() not in _subCategory and ("all" not in _subCategory):
            continue
        itemCode = item['itemCode']
        location = location1[item['restaurantId']]
        restaurantId = item['restaurantId']
        entryType = item.get('entryType', None)
        pkgName = item.get('packageName', None)
        item['entryType'] = item.get('entryType') if item.get('entryType', None) is not None else 'N/A'
        item['packageName'] = item.get('packageName') if item.get('packageName', None) is not None else 'N/A'
        item['uom'] = item.get('uom', 'N/A')
        taxRate = item.get('taxRate', 0)
        sv_price = float(item.get('withTaxPrice', 0))
        rateKey= itemCode+'|'+item['packageName']
        wac = float(getRates[restaurantId].get(rateKey, {'monthWac': sv_price}).get('monthWac'))
        if entryType == 'open':
            wac = wac / item.get('packageQty', 1)

        if item['ItemType'] == "SubRecipe":
            searchKey1 = itemCode + '|' + 'N/A' + '|' + 'N/A'
            measure = "open"
        else:
            measure = item['entryType']
            searchKey1 = itemCode + '|' + item['uom'].upper() + '|' + item['packageName'].upper()


        ###################### user closing ###################
        physicalStoreQty = 0
        if item['entryType'] == "open":
            if item['restaurantId'] in openDictStore:
                if itemCode in openDictStore[item['restaurantId']]['store'].keys():
                    physicalStoreQty = openDictStore[item['restaurantId']]['store'][itemCode]
        else:
            if item['restaurantId'] in closingUserStoreDict:
                if searchKey1 in closingUserStoreDict[item['restaurantId']]['store'].keys():
                    physicalStoreQty = closingUserStoreDict[item['restaurantId']]['store'][searchKey1][measure]

        ###################### user opening ###################
        systemOpening = {'inStock' : 0}
        matchFound = False
        if item['entryType'] == "open":
            if item['restaurantId'] in openDictStoreOpen:
                if itemCode in openDictStoreOpen[item['restaurantId']]['store'].keys():
                    matchFound = True
                    systemOpening = {"inStock" : openDictStoreOpen[item['restaurantId']]['store'][itemCode]}
        else:
            if item['restaurantId'] in userStoreDictOpen:
                if searchKey1 in userStoreDictOpen[item['restaurantId']]['store'].keys():
                    matchFound = True
                    systemOpening = {"inStock" : userStoreDictOpen[item['restaurantId']]['store'][searchKey1][measure]}

        searchKey = itemCode + '|' + item['entryType'].upper() +  '|' + item['packageName'].upper()
        if not matchFound :
            ######################## system opening ########################
            if location not in openingSystemDict:
                system = dailybackupsCol.find_one({
                    'restaurantId': item['restaurantId'],
                    'createTs': {
                        '$gte': job['details']['startDate'].replace(hour=0, minute=0, second=0) - datetime.timedelta(days=1),
                        '$lte': job['details']['startDate'].replace(hour=23, minute=59, second=59) - datetime.timedelta(days=1)
                    }
                })
                openingSystemDict[location] = system['items'] if system else []
            if 'packageName' not in item.keys():
                item['packageName'] = None
            if 'entryType' not in item.keys():
                item['entryType'] = None
            systemOpening = next((i for i in openingSystemDict[location] if i.get('itemCode') == item['itemCode'] and i.get('packageName') == pkgName and i.get('entryType') == entryType), {})

        ######################## system closing ########################
        systemClosing = {}
        if endTime.replace(hour=0, minute=0, second=0) <= datetime.datetime.now() <= endTime:
            systemClosing['inStock'] = item['inStock']
        else:
            if location not in closingSystemDict:
                system = dailybackupsCol.find_one({
                    'restaurantId': item['restaurantId'],
                    'createTs': {
                        '$gte': job['details']['endDate'].replace(hour=0, minute=0, second=0),
                        '$lte': job['details']['endDate'].replace(hour=23, minute=59, second=59)
                    }
                })
                closingSystemDict[location] = system['items'] if system else []

            if 'packageName' not in item.keys():
                item['packageName'] = None
            if 'entryType' not in item.keys():
                item['entryType'] = None
            systemClosing = next((i for i in closingSystemDict[location] if i.get('itemCode') == item['itemCode'] and i.get('packageName') == pkgName and i.get('entryType') == entryType), {})

        ############################### GRN ##############################
        if restaurantId in grnDict:
            if searchKey in grnDict[restaurantId]:
                RTVQty = grnDict[restaurantId][searchKey]['RTVQty']
                grnQty = grnDict[restaurantId][searchKey]['received']
            else:
                RTVQty = 0
                grnQty = 0
        else:
            RTVQty = 0
            grnQty = 0

        ############################# ibt in #############################
        if restaurantId in ibtInDict:
            if searchKey in ibtInDict[restaurantId]:
                ibtInQty = ibtInDict[restaurantId][searchKey]
            else:
                ibtInQty = 0
        else:
            ibtInQty = 0

        ############################# ibt out #############################
        if restaurantId in ibtOutDict:
            if searchKey in ibtOutDict[restaurantId]:
                ibtOutQty = ibtOutDict[restaurantId][searchKey]
            else:
                ibtOutQty = 0
        else:
            ibtOutQty = 0

        ############################# indent #############################
        if restaurantId in indentDict:
            if searchKey in indentDict[restaurantId]:
                indentQty = indentDict[restaurantId][searchKey]['issued']
            else:
                indentQty = 0
        else:
            indentQty = 0


        ############################# rts #############################
        rtsQty = 0
        if restaurantId in rtsDict:
            if searchKey in rtsDict[restaurantId]:
                rtsQty = rtsDict[restaurantId][searchKey]

        ############################# spoilage #############################
        if restaurantId in spoilageDict:
            if searchKey in spoilageDict[restaurantId]:
                spoilageQty = spoilageDict[restaurantId][searchKey]
            else:
                spoilageQty = 0
        else:
            spoilageQty = 0


        entry = {
            'Location': location,
            'Category': item['category'],
            'Sub Category': item['subCategory'],
            'Item Code': itemCode,
            'Item Name': item['itemName'],
            'Entry Type': item['entryType'],
            'Package Name': item['packageName'],
            'UOM': item['uom'],
            "Tax(%)" : taxRate,
            'WAC(incl.tax,etc)' : ut.truncate_and_floor(wac, 2),
            'Opening Qty':  ut.truncate_and_floor(systemOpening.get('inStock', 0), 2),
            'Purchase Qty':  ut.truncate_and_floor(grnQty, 2),
            'Return Qty':  ut.truncate_and_floor(RTVQty,2),
            'Return To Store In Qty':  ut.truncate_and_floor(rtsQty, 2),
            'Indent Qty':  ut.truncate_and_floor(indentQty, 2),
            'Ibt In Qty':  ut.truncate_and_floor(ibtInQty, 2),
            'Ibt Out Qty':  ut.truncate_and_floor(ibtOutQty, 2),
            'Spoilage Qty':  ut.truncate_and_floor(spoilageQty, 2),
            'Closing Qty':  ut.truncate_and_floor(systemClosing.get('inStock', 0), 2),
            'Physical Closing Qty':  ut.truncate_and_floor(physicalStoreQty, 2),
            "Variance" : ut.truncate_and_floor((ut.truncate_and_floor(physicalStoreQty, 2) - ut.truncate_and_floor(systemClosing.get('inStock', 0), 2)),2),
            "Variance Amount" : ut.truncate_and_floor(((ut.truncate_and_floor(physicalStoreQty, 2) - ut.truncate_and_floor(systemClosing.get('inStock', 0), 2)) * wac),2),
            'Opening Amount':  ut.truncate_and_floor((systemOpening.get('inStock', 0) * wac),2),
            'Purchase Amount':  ut.truncate_and_floor((grnQty * wac),2),
            'Return-Qty Amount':  ut.truncate_and_floor((RTVQty * wac),2),
            'Indent Amount':  ut.truncate_and_floor((indentQty * wac),2),
            'Ibt In Amount':  ut.truncate_and_floor((ibtInQty * wac),2),
            'Ibt Out Amount':  ut.truncate_and_floor((ibtOutQty * wac),2),
            'Spoilage Amount':  ut.truncate_and_floor((spoilageQty * wac),2),
            'Closing Amount':  ut.truncate_and_floor((systemClosing.get('inStock', 0) * wac),2),
            'Physical Closing Amount':  ut.truncate_and_floor((physicalStoreQty * wac), 2)
        }
        invList.append(entry)
    df = pd.DataFrame(invList)

    return df

def systemClosingReport(job):
    selectedRestaurants = job['details']['selectedRestaurants']
    getRates = get_purchase_rates(selectedRestaurants, job['details']['startDate'])
    invSearchFilter = {
        'status': {"$ne" :"discontinued"},
        'ItemType' : {'$in': ['Inventory','SubRecipe']},
        'restaurantId' : {'$in': selectedRestaurants}
    }
    orQueryForWorkAreas=[]
    for area in job['details']['selectedWorkAreas']:
        temp={}
        temp['workArea.{}'.format(area.strip())] = {'$exists': True }
        orQueryForWorkAreas.append(temp)
    if len(orQueryForWorkAreas) > 0:
        invSearchFilter['$or'] = orQueryForWorkAreas
    location1 = getLocation(job['tenantId'])
    invList = []
    workAreaDict = {}
    systemDict = {}
    _category = [str(category).lower() for category in job['details']['selectedCategories'] if not str(category).isnumeric()]
    _subCategory = [str(subCategory).lower() for subCategory in job['details']['selectedSubCategories'] if not str(subCategory).isnumeric()]
    for item in Stockvalues.find(invSearchFilter):
        if item['category'].lower() not in _category and ("all" not in _category):
            continue
        if item['subCategory'].lower() not in _subCategory and ("all" not in _subCategory):
            continue
        itemCode = item['itemCode']
        location = location1[item['restaurantId']]
        if location not in systemDict:
            system = dailybackupsCol.find_one({
                'restaurantId': item['restaurantId'],
                'createTs': {
                    '$gte': job['details']['startDate'].replace(hour=0, minute=0, second=0) ,
                    '$lte': job['details']['startDate'].replace(hour=23, minute=59, second=59)
                }
            })
            if system :
                systemDict[location] = system['items']
            else:
                systemDict[location] = []

        if location not in workAreaDict.keys():
            workAreaDict[location] =[]
        if 'packageName' not in item.keys():
            item['packageName'] = None
        if 'entryType' not in item.keys():
            item['entryType'] = None

        matching = next((i for i in systemDict[location] if i.get('itemCode') == item['itemCode'] and i.get('packageName') == item['packageName'] and i.get('entryType') == item['entryType']), None)
        if matching:
            item['inStock'] = matching['inStock']
            item['workArea'] = matching['workArea']
        else:
            continue
        item['entryType'] = item.get('entryType') if item.get('entryType', None) is not None else 'N/A'
        packageName = item.get('packageName') if item.get('packageName', None) is not None else 'N/A'
        rateKey= itemCode+'|'+packageName
        price = getRates[item['restaurantId']].get(rateKey, {'monthWac': item.get('withTaxPrice', 0)}).get('monthWac')
        if item['entryType'] == 'open':
            price = price / item.get('packageQty', 1)
        totalStockInHand = ut.truncate_and_floor(item['inStock'], 2)
        entry = {
            'Location': location,
            'Category': item['category'],
            'Sub Category': item['subCategory'],
            'Item Code': itemCode,
            'HSN/SAC': item.get('hsnCode', 'N/A'),
            'Item Name': item['itemName'],
            'Entry Type': item.get("entryType","N/A"),
            'Package Name': packageName,
            'UOM': item['uom'],
            "Tax(%)" : item.get("taxRate", 0),
            'WAC(incl.tax,etc)' : ut.truncate_and_floor(price, 2),
            'Store Stock':  ut.truncate_and_floor(totalStockInHand, 2),
            'Store Total(incl.tax,etc)' : ut.truncate_and_floor(totalStockInHand * price, 2)
        }
        workAreaCount = 0
        workAreaTotalCount = 0
        for workArea in item['workArea'].keys():
            if workArea not in job['details']['selectedWorkAreas']:
                continue
            if item['workArea'][workArea] is None or math.isnan(item['workArea'][workArea]):
                item['workArea'][workArea] = 0

            entry[workArea] = ut.truncate_and_floor(item['workArea'][workArea], 2)
            workAreaCount += entry[workArea]
            entry['{} Total(incl.tax,etc)'.format(workArea)] = ut.truncate_and_floor(price * item['workArea'][workArea])
            workAreaTotalCount += ut.truncate_and_floor(price * item['workArea'][workArea])
            totalStockInHand += ut.truncate_and_floor(item['workArea'][workArea],2)

            if workArea not in workAreaDict[location]:
                workAreaDict[location].append(workArea)
            if '{} Total(incl.tax,etc)'.format(workArea) not in workAreaDict[location]:
                workAreaDict[location].append('{} Total(incl.tax,etc)'.format(workArea))
        entry['All Work Area stock Total Qty'] = workAreaCount
        entry['All Work Area Stock Total(incl.tax,etc)'] = workAreaTotalCount

        entry['Grand Total(incl.tax,etc)'] = ut.truncate_and_floor(price * totalStockInHand, 2)
        entry['Total Stock InHand(Store+work area)'] = ut.truncate_and_floor(totalStockInHand, 2)
        if 'showZeroStock' in job['details'] :
            if job['details']['showZeroStock'] == False:
                if entry['Total Stock InHand(Store+work area)'] > 0 :
                    invList.append(entry)
            else:
                invList.append(entry)
        else :
            invList.append(entry)
    df = pd.DataFrame(invList)

    # Group by location and process data
    dataframes = {}
    if len(df) > 0:
        for sheet_name, group_df in df.groupby('Location'):
            if not group_df.empty:
                group_df = group_df.copy()
                dfColumns = [
                    'S.No',
                    'Location',
                    'Category',
                    'Sub Category',
                    'Item Code',
                    'HSN/SAC',
                    'Item Name',
                    'Entry Type',
                    'Package Name',
                    'UOM',
                    'Store Stock',
                    'Tax(%)',
                    'WAC(incl.tax,etc)',
                    'Store Total(incl.tax,etc)'
                ]
                dfColumns += ((workAreaDict[sheet_name]) + [
                    'All Work Area stock Total Qty',
                    'All Work Area Stock Total(incl.tax,etc)',
                    'Total Stock InHand(Store+work area)',
                    'Grand Total(incl.tax,etc)'
                ])
                workAreas = [item for item in workAreaDict[sheet_name] if "Total(incl.tax,etc)" in item]
                totalRequiredFields = ['Store Total(incl.tax,etc)','Total Stock InHand(Store+work area)','Grand Total(incl.tax,etc)'] + workAreas
                totals = group_df[totalRequiredFields].sum()
                group_df.insert(0, 'S.No', range(1, len(group_df) + 1))
                data1 = ['Total'] + list(totals.to_dict().values())
                data2 = ['S.No'] + totalRequiredFields
                group_df = group_df._append(pd.Series(data1, index=data2), ignore_index=True)
                group_df = group_df[dfColumns]
                if len(job['details']['selectedWorkAreas']) > 0:
                    group_df = group_df[job['details']['requiredColumns'] + workAreaDict.get(sheet_name, []) + ['All Work Area stock Total Qty', 'All Work Area Stock Total(incl.tax,etc)','Total Stock InHand(Store+work area)','Grand Total(incl.tax,etc)']]
                if len(job['details']['selectedWorkAreas']) == 0:
                    group_df = group_df[job['details']['requiredColumns']]
                dataframes[sheet_name] = group_df

    return dataframes


def inventoryConsumptionNew(job):
    """
    Optimized parallel implementation of inventory consumption report
    Uses ThreadPoolExecutor for concurrent data fetching and processing
    """
    import time
    start_time = time.time()


    stockDict = {}
    invOverallList = []
    startTime = job['details']['startDate'].replace(hour=0, minute=0, second=0)
    endTime = job['details']['endDate'].replace(hour=23, minute=59, second=59)

    originalWas = job['details']['selectedWorkAreas']
    location1 = getLocation(job['tenantId'])
    getRates = get_purchase_rates(job['details']['selectedRestaurants'],endTime, True)
    _category = [str(category).lower() for category in job['details']['selectedCategories'] if not str(category).isnumeric()]
    _subCategory = [str(subCategory).lower() for subCategory in job['details']['selectedSubCategories'] if not str(subCategory).isnumeric()]



    # Collect all restaurant-work area combinations for parallel processing
    restaurant_work_areas = []
    for restaurantId in job['details']['selectedRestaurants']:
        getStockDict(restaurantId, stockDict)
        was = branchesCol.find_one({'restaurantIdOld' : restaurantId},{'workAreas' : 1})
        for wa in was['workAreas']:
            if wa in originalWas:
                restaurant_work_areas.append((restaurantId, wa))

    # Parallel data preparation function
    def prepare_data_for_work_area(restaurant_wa_tuple):
        restaurantId, wa = restaurant_wa_tuple


        # Create a copy of job for this work area
        work_area_job = copy.deepcopy(job)
        work_area_job['details']['selectedWorkAreas'] = [wa]

        # Prepare data dictionaries in parallel using ThreadPoolExecutor
        with ThreadPoolExecutor(max_workers=6) as executor:
            # Submit all data preparation tasks
            spoilage_future = executor.submit(getSpoilageDictWorkArea, work_area_job, startTime, endTime)
            indent_future = executor.submit(getIndentDict, work_area_job, startTime, endTime, False)  # RE-ENABLED - INDENT INCLUDED
            ibt_future = executor.submit(getIntraBranchTransferDict, work_area_job, startTime, endTime)
            opening_future = executor.submit(getUserDictWorkArea, work_area_job, 'opening', 1)
            closing_future = executor.submit(getUserDictWorkArea, work_area_job, 'closing', 1)
            rts_future = executor.submit(getRTS, work_area_job, startTime, endTime, True)

            # Collect results
            spoilageDict = spoilage_future.result()
            indent = indent_future.result()  # RE-ENABLED - INDENT INCLUDED
            indentDict = indent[0]  # Re-enabled indent dictionary
            subrecipeIndentDict = indent[1]  # Re-enabled subrecipe indent dictionary
            ibtDict = ibt_future.result()
            ibtInDict = ibtDict[0]
            ibtOutDict = ibtDict[1]
            openingUserDict = opening_future.result()
            closingUserDict = closing_future.result()
            rtsDict = rts_future.result()

        # Theoretical consumption (currently empty as per original code)
        theoreticalConsumptionDict = {}
        subrecipeConsumptionDict = {}

        return {
            'restaurantId': restaurantId,
            'wa': wa,
            'spoilageDict': spoilageDict,
            'indentDict': indentDict,
            'subrecipeIndentDict': subrecipeIndentDict,
            'ibtInDict': ibtInDict,
            'ibtOutDict': ibtOutDict,
            'openingUserDict': openingUserDict,
            'closingUserDict': closingUserDict,
            'rtsDict': rtsDict,
            'theoreticalConsumptionDict': theoreticalConsumptionDict,
            'subrecipeConsumptionDict': subrecipeConsumptionDict
        }

    # Process all restaurant-work area combinations in parallel
    # Adjust max_workers based on system resources and database connection limits
    max_workers = min(4, len(restaurant_work_areas), os.cpu_count() or 1)


    with ThreadPoolExecutor(max_workers=max_workers) as main_executor:
        # Submit all work area processing tasks
        future_to_data = {main_executor.submit(prepare_data_for_work_area, rwa): rwa for rwa in restaurant_work_areas}

        # Process results as they complete
        for future in as_completed(future_to_data):
            try:
                work_area_data = future.result()
                restaurantId = work_area_data['restaurantId']
                wa = work_area_data['wa']

                # Extract all the dictionaries
                spoilageDict = work_area_data['spoilageDict']
                indentDict = work_area_data['indentDict']
                subrecipeIndentDict = work_area_data['subrecipeIndentDict']
                ibtInDict = work_area_data['ibtInDict']
                ibtOutDict = work_area_data['ibtOutDict']
                openingUserDict = work_area_data['openingUserDict']
                closingUserDict = work_area_data['closingUserDict']
                rtsDict = work_area_data['rtsDict']
                theoreticalConsumptionDict = work_area_data['theoreticalConsumptionDict']
                subrecipeConsumptionDict = work_area_data['subrecipeConsumptionDict']

                # Process inventory items for this work area (same logic as original)
                queries=[
                    {'restaurantId': restaurantId, 'entryType': 'open', 'ItemType': 'Inventory', f"workArea.{wa}": {'$exists': True }},
                    {'restaurantId': restaurantId, 'uom': {'$in':['Nos','nos','NOS']}, 'ItemType': 'Inventory', f"workArea.{wa}": {'$exists': True }},
                    {'restaurantId': restaurantId, 'ItemType': 'SubRecipe', f"workArea.{wa}": {'$exists': True }},
                ]

                for query in queries:
                    invItems = list(Stockvalues.find(query))
                    for entry in invItems:
                        if entry['category'].lower() not in _category and ("all" not in _category):
                            continue
                        if entry['subCategory'].lower() not in _subCategory and ("all" not in _subCategory):
                            continue

                        itemCode = entry['itemCode'].strip()
                        uom = entry['uom'].strip()
                        searchKey = itemCode + '|' + uom.upper()
                        isSubRecipe = False
                        if entry['ItemType'] =='SubRecipe':
                            isSubRecipe = True
                            searchSubRecipeKey = itemCode + '|' + 'N/A'

                        ########## workarea transfer in #############
                        if restaurantId in ibtInDict:
                            if searchKey in ibtInDict[restaurantId]:
                                ibtInQty = ibtInDict[restaurantId][searchKey]
                            else:
                                ibtInQty = 0
                        else:
                            ibtInQty = 0

                        ########## workarea transfer out ############
                        if restaurantId in ibtOutDict:
                            if searchKey in ibtOutDict[restaurantId]:
                                ibtOutQty = ibtOutDict[restaurantId][searchKey]
                            else:
                                ibtOutQty = 0
                        else:
                            ibtOutQty = 0

                        ########## workarea transfer out ############
                        rtsQty =0
                        if restaurantId in rtsDict:
                            if searchKey in rtsDict[restaurantId]:
                                rtsQty = rtsDict[restaurantId][searchKey]

                        ########## indent #############
                        if isSubRecipe:
                            if restaurantId in subrecipeIndentDict:
                                if searchKey in subrecipeIndentDict[restaurantId]:
                                    indentQty = subrecipeIndentDict[restaurantId][searchKey]
                                else:
                                    indentQty = 0
                            else:
                                indentQty = 0
                        else:
                            if restaurantId in indentDict:
                                if searchKey in indentDict[restaurantId]:
                                    indentQty = indentDict[restaurantId][searchKey]
                                else:
                                    indentQty = 0
                            else:
                                indentQty = 0

                        ########## spoilage #############
                        if restaurantId in spoilageDict:
                            if searchKey in spoilageDict[restaurantId]:
                                spoilageQty = spoilageDict[restaurantId][searchKey]
                            else:
                                spoilageQty = 0
                        else:
                            spoilageQty = 0

                        ########## Theoretical ConsumptionDict #############
                        if isSubRecipe:
                            if restaurantId in subrecipeConsumptionDict:
                                if searchKey in subrecipeConsumptionDict[restaurantId]:
                                    theoreticalConsumptionQty = subrecipeConsumptionDict[restaurantId][searchKey]
                                else:
                                    theoreticalConsumptionQty = 0
                            else:
                                theoreticalConsumptionQty = 0
                        else:
                            if restaurantId in theoreticalConsumptionDict:
                                if searchKey in theoreticalConsumptionDict[restaurantId]:
                                    theoreticalConsumptionQty = theoreticalConsumptionDict[restaurantId][searchKey]
                                else:
                                    theoreticalConsumptionQty = 0
                            else:
                                theoreticalConsumptionQty = 0

                        ########## Opening User workarea #############
                        if isSubRecipe:
                            if restaurantId in openingUserDict:
                                if searchSubRecipeKey in openingUserDict[restaurantId]:
                                    openingUserQtyWorkArea = openingUserDict[restaurantId][searchSubRecipeKey]
                                else:
                                    openingUserQtyWorkArea = None
                            else:
                                openingUserQtyWorkArea = None
                        else:
                            if restaurantId in openingUserDict:
                                if searchKey in openingUserDict[restaurantId]:
                                    openingUserQtyWorkArea = openingUserDict[restaurantId][searchKey]
                                else:
                                    openingUserQtyWorkArea = None
                            else:
                                openingUserQtyWorkArea = None

                        ########## Closing User workarea #############
                        if isSubRecipe:
                            if restaurantId in closingUserDict:
                                if searchSubRecipeKey in closingUserDict[restaurantId]:
                                    closingUserQtyWorkArea = closingUserDict[restaurantId][searchSubRecipeKey]
                                else:
                                    closingUserQtyWorkArea = None
                            else:
                                closingUserQtyWorkArea = None
                        else:
                            if restaurantId in closingUserDict:
                                if searchKey in closingUserDict[restaurantId]:
                                    closingUserQtyWorkArea = closingUserDict[restaurantId][searchKey]
                                else:
                                    closingUserQtyWorkArea = None
                            else:
                                closingUserQtyWorkArea = None

                        price = ut.truncate_and_floor(entry.get('withTaxPrice', 0), 2)
                        isGrnAvailable = getRates[restaurantId].get(entry['itemCode'], None)
                        if isGrnAvailable:
                            sv = next((x for x in stockDict[restaurantId] if x['itemCode'] == entry['itemCode'] and x['packageName'] == isGrnAvailable.get('packageName')), None)
                            packageQty = sv['packageQty'] if sv else 1
                            price = isGrnAvailable.get('monthWac')/ packageQty

                        totalStock = (( (ut.truncate_and_floor(openingUserQtyWorkArea, 3) if (openingUserQtyWorkArea is not None) else 0) +indentQty + ibtInQty + spoilageQty) - (ibtOutQty + rtsQty))
                        actualConsumption = (totalStock -closingUserQtyWorkArea) if (closingUserQtyWorkArea is not None) else 0
                        subTotalOpen = ut.truncate_and_floor(openingUserQtyWorkArea * price, 2) if openingUserQtyWorkArea is not None else 0
                        subTotalClos = ut.truncate_and_floor(closingUserQtyWorkArea * price, 2) if closingUserQtyWorkArea is not None else 0
                        subTotalActu = ut.truncate_and_floor((actualConsumption * price), 2)
                        subTotalTheo = ut.truncate_and_floor((theoreticalConsumptionQty * price), 2)
                        subTotalOver = ut.truncate_and_floor(((theoreticalConsumptionQty - actualConsumption)* price), 2)
                        temp = {
                            'Location': location1[entry['restaurantId']],
                            'WorkArea' : wa,
                            'Category': entry['category'],
                            'Sub Category': entry['subCategory'],
                            'Item Name': entry['itemName'],
                            'Item Code': entry['itemCode'],
                            'UOM': uom,
                            'WorkArea Opening': ut.truncate_and_floor(openingUserQtyWorkArea, 3) if openingUserQtyWorkArea is not None else 0,
                            'WorkArea Indent': ut.truncate_and_floor(indentQty, 3),
                            'WorkArea Transfer In': ut.truncate_and_floor(ibtInQty, 3),
                            'WorkArea Transfer Out': ut.truncate_and_floor(ibtOutQty, 3),
                            'Return To Store Out': ut.truncate_and_floor(rtsQty, 3),
                            'Spoilage/Adjustments': ut.truncate_and_floor(spoilageQty, 3),
                            'Total WorkArea Stock': ut.truncate_and_floor(totalStock, 3),
                            'WorkArea Closing': ut.truncate_and_floor(closingUserQtyWorkArea, 3) if closingUserQtyWorkArea is not None else 0,
                            'Actual': ut.truncate_and_floor(actualConsumption, 3),
                            'Theoretical': ut.truncate_and_floor(theoreticalConsumptionQty, 3),
                            'Variance Qty': theoreticalConsumptionQty - actualConsumption,
                            'WAC(incl.tax,etc)': ut.truncate_and_floor(price, 3),
                            'WorkArea Opening(incl.tax,etc)': ut.truncate_and_floor(subTotalOpen, 2),
                            'WorkArea Closing(incl.tax,etc)': ut.truncate_and_floor(subTotalClos, 2),
                            'Actual(incl.tax,etc)': ut.truncate_and_floor(subTotalActu, 2),
                            'Theoretical(incl.tax,etc)': ut.truncate_and_floor(subTotalTheo, 2),
                            'Variance (incl.tax,etc)': ut.truncate_and_floor(subTotalOver, 2)
                        }
                        invOverallList.append(temp)

            except Exception as e:
                print(f"Error processing work area {future_to_data[future]}: {str(e)}")
                # Print more detailed error information for debugging
                import traceback
                print(f"Detailed error trace: {traceback.format_exc()}")
                continue

    total_time = time.time() - start_time

    return pd.DataFrame(invOverallList)

def purchaseBaseConsumption(job):
    stockDict = {}
    startTime = job['details']['startDate'].replace(hour=0, minute=0, second=0)
    endTime = job['details']['endDate'].replace(hour=23, minute=59, second=59)
    selectedRestaurants = job['details']['selectedRestaurants']
    getRates = get_purchase_rates(selectedRestaurants,endTime, True)
    grnDict = getGrnDict(job, startTime, endTime)
    openingUserDictWorkArea = getUserDictWorkArea(job, 'opening', 1)
    closingUserDictWorkArea = getUserDictWorkArea(job, 'closing', 1)
    openingUserDictStore = getUserDictStore(job, 'opening', 3)
    closingUserDictStore = getUserDictStore(job, 'closing', 3)
    location1 = getLocation(job['tenantId'])
    invOverallList = []
    orQueryForWorkAreas=[]

    for area in job['details']['selectedWorkAreas']:
        temp = {'workArea.{}'.format(area.strip()): {'$exists': True}}
        orQueryForWorkAreas.append(temp)

    _category = [str(category).lower() for category in job['details']['selectedCategories'] if not str(category).isnumeric()]
    _subCategory = [str(subCategory).lower() for subCategory in job['details']['selectedSubCategories'] if not str(subCategory).isnumeric()]

    for restaurantId in job['details']['selectedRestaurants']:
        queries = [
            {
                'status': {"$ne" :"discontinued"},
                'restaurantId': restaurantId,
                'entryType': 'open',
                'ItemType': 'Inventory',
                '$or': orQueryForWorkAreas
            },
            {
                'status': {"$ne" :"discontinued"},
                'restaurantId': restaurantId,
                'uom': {'$in': ['Nos', 'nos', 'NOS']},
                'ItemType': 'Inventory',
                '$or': orQueryForWorkAreas
            }
        ]
        for query in queries:
            for item in Stockvalues.find(query):
                if item['category'].lower() not in _category and ("all" not in _category):
                    continue
                if item['subCategory'].lower() not in _subCategory and ("all" not in _subCategory):
                    continue

                searchKey = item['itemCode'] + '|' + item['uom'].upper()
                getStockDict(restaurantId, stockDict)
                isGrnAvailable = getRates[restaurantId].get(item['itemCode'], None)
                price = item.get('withTaxPrice',0)
                if isGrnAvailable:
                    sv = next((x for x in stockDict[restaurantId] if x['itemCode'] == item['itemCode'] and x['packageName'] == isGrnAvailable.get('packageName')), None)
                    packageQty = sv['packageQty'] if sv else 1
                    price = isGrnAvailable.get('monthWac') / packageQty

                ################## GRN ###################
                purchaseQty = 0
                if restaurantId in grnDict:
                    if searchKey in grnDict[restaurantId]:
                        purchaseQty = ut.truncate_and_floor(grnDict[restaurantId][searchKey],3)

                closing = { "store": 0, "workArea" : 0 }
                opening = { "store": 0, "workArea" : 0 }
                ######### Opening User workarea ###########
                if restaurantId in openingUserDictWorkArea:
                    if searchKey in openingUserDictWorkArea[restaurantId]:
                        opening['workArea'] = ut.truncate_and_floor(openingUserDictWorkArea[restaurantId][searchKey],3)

                ######### Closing User workarea ###########
                if restaurantId in closingUserDictWorkArea:
                    if searchKey in closingUserDictWorkArea[restaurantId]:
                        closing['workArea'] = ut.truncate_and_floor(closingUserDictWorkArea[restaurantId][searchKey],3)

                ########## Opening User store #############
                if restaurantId in openingUserDictStore:
                    if searchKey in openingUserDictStore[restaurantId]:
                        opening['store'] = ut.truncate_and_floor(openingUserDictStore[restaurantId][searchKey],3)

                ########## Closing User store #############
                if restaurantId in closingUserDictStore:
                    if searchKey in closingUserDictStore[restaurantId]:
                        closing['store'] = ut.truncate_and_floor(closingUserDictStore[restaurantId][searchKey],3)

                totalStock = opening['store'] + opening['workArea'] + purchaseQty
                actualConsumption = (totalStock) - (closing['store'] + closing['workArea'])
                temp = {}
                temp = {
                    'Location': location1[item['restaurantId']],
                    'Category': item['category'],
                    'Sub Category': item['subCategory'],
                    'Item Name': item['itemName'],
                    'Item Code': item['itemCode'],
                    'UOM': item['uom'].upper(),
                    'Store Opening': opening['store'],
                    'WorkArea Opening': opening['workArea'],
                    'Purchase Qty':  purchaseQty,
                    'Total Stock': totalStock,
                    'Store Closing': closing['store'],
                    'WorkArea Closing': closing['workArea'],
                    'Actual Consumption': actualConsumption,
                    'WAC(incl.tax,etc)': ut.truncate_and_floor(price,2),
                    'Store Opening Valuation': ut.truncate_and_floor(opening['store'] * price,2),
                    'WorkArea Opening Valuation': ut.truncate_and_floor(opening['workArea'] * price,2),
                    'Purchase Valuation': ut.truncate_and_floor(purchaseQty * price,2),
                    'Total Stock Valuation': ut.truncate_and_floor(totalStock * price,2),
                    'Store Closing Valuation':  ut.truncate_and_floor(closing['store'] * price,2),
                    'WorkArea Closing Valuation': ut.truncate_and_floor(closing['workArea'] * price,2),
                    'Actual Consumption Valuation': ut.truncate_and_floor(actualConsumption * price, 2),
                }
                invOverallList.append(temp)
    df = pd.DataFrame(invOverallList)
    return df

def inventoryStatusReport(job, userReportsDirectory, reportType, requestDate, reportNo):
    selectedRestaurants = job['details']['selectedRestaurants']
    invSearchFilter = {
        'status': {"$ne" :"discontinued"},
        'ItemType' : {'$in': ['Inventory','SubRecipe']},
        'restaurantId' : {'$in': selectedRestaurants}
    }
    if 'scheduler' in job.keys() and job['scheduler']:
        invSearchFilter['ItemType'] = {'$in': ['Inventory']}
        invSearchFilter['$expr'] = { '$gt': ["$parLevel", "$inStock"] }
    orQueryForWorkAreas=[]
    for area in job['details']['selectedWorkAreas']:
        temp={}
        temp['workArea.{}'.format(area.strip())] = {'$exists': True }
        orQueryForWorkAreas.append(temp)
    if len(orQueryForWorkAreas) > 0:
        invSearchFilter['$or'] = orQueryForWorkAreas
    location1 = getLocation(job['tenantId'])
    invList = []
    workAreaDict = {}
    _category = [str(category).lower() for category in job['details']['selectedCategories'] if not str(category).isnumeric()]
    _subcategory = [str(subCategory).lower() for subCategory in job['details']['selectedSubCategories'] if isinstance(subCategory, str)]

    for item in Stockvalues.find(invSearchFilter):
        itemCode = item['itemCode']
        if str(item['category']).lower() not in _category and ("all" not in _category):
            continue
        if item['subCategory'].lower() not in _subcategory and ("all" not in _subcategory):
            continue
        location = location1[item['restaurantId']]
        if location not in workAreaDict.keys():
            workAreaDict[location] =[]
        item['packageName'] = item.get('packageName') if item.get('packageName') is not None else 'N/A'
        item['entryType'] = item.get('entryType') if item.get('entryType') is not None else 'N/A'
        item['taxRate'] = item.get("taxRate", 0)
        item['withTaxPrice'] = float(item.get("withTaxPrice", 0))
        item['inStock'] = item.get("inStock", 0)
        item['parLevel'] = item.get("parLevel", 0)
        totalStockInHand = ut.truncate_and_floor(item['inStock'], 2)

        entry = {
            'Location': location,
            'Category': item['category'],
            'Sub Category': item['subCategory'],
            'HSN/SAC': item.get('hsnCode', 'N/A'),
            'Item Code': itemCode,
            'Item Name': item['itemName'],
            'Entry Type': item.get("entryType","N/A"),
            'Package Name': item['packageName'],
            'UOM': item['uom'],
            "Tax(%)" : float(item['taxRate']),
            'Store Stock':  totalStockInHand,
            'Par Level':  item['parLevel'],
            'WAC(incl.tax,etc)' : ut.truncate_and_floor(item['withTaxPrice'], 2),
            'Store Total(incl.tax,etc)' : ut.truncate_and_floor(item['withTaxPrice'] * totalStockInHand, 2)
        }
        for workArea in item['workArea'].keys():
            if workArea not in job['details']['selectedWorkAreas']:
                continue
            if item['workArea'][workArea] is None or math.isnan(item['workArea'][workArea]):
                item['workArea'][workArea] = 0
            entry[workArea] = ut.truncate_and_floor(item['workArea'][workArea], 2)
            entry['{} Total(incl.tax,etc)'.format(workArea)] = ut.truncate_and_floor(item['withTaxPrice'] * item['workArea'][workArea], 2)

            totalStockInHand += ut.truncate_and_floor(item['workArea'][workArea],2)

            if workArea not in workAreaDict[location]:
                workAreaDict[location].append(workArea)
            if '{} Total(incl.tax,etc)'.format(workArea) not in workAreaDict[location]:
                workAreaDict[location].append('{} Total(incl.tax,etc)'.format(workArea))

        entry['Grand Total(incl.tax,etc)'] = ut.truncate_and_floor(item['withTaxPrice'] * totalStockInHand, 2)

        entry['Total Stock(Store + Workarea)'] = ut.truncate_and_floor(totalStockInHand, 2)
        if 'showZeroStock' in job['details'] :
            if job['details']['showZeroStock'] == False:
                if entry['Total Stock(Store + Workarea)'] > 0 :
                    invList.append(entry)
            else:
                invList.append(entry)
        else :
            invList.append(entry)

    df = pd.DataFrame(invList)
    return df

def adjustInventoryReport(job):
    stockDict ={}
    selectedRestaurants = job['details']['selectedRestaurants']
    startDate = job['details']['startDate'].replace(hour=0, minute=0, second=0)
    endDate = job['details']['endDate'].replace(hour=23, minute=59, second=59)
    adjustInvFilter = {
        'restaurantId': {'$in': selectedRestaurants},
        'createTs': {'$gte': startDate, '$lte': endDate}
    }
    location1 = getLocation(job['tenantId'])
    adjustInvDict = {}
    count = 0
    for entry in adjustinvCol.find(adjustInvFilter):
        getStockDict(entry['restaurantId'], stockDict)
        location = location1[entry['restaurantId']]
        userEmail = entry['userEmail']
        workArea = entry['workArea']
        reqNo = entry['adjustId']
        for item in entry['adjustInvItems']:
            itemCode = item['itemCode']
            item['packageName'] = item.get("packageName", 'N/A')
            item['entryType'] = item.get('entryType', 'N/A')
            item['uom'] = item.get('uom', 'N/A')
            category ='N/A'
            subCategory ='N/A'
            sv = next((x for x in stockDict[entry['restaurantId']] if x['itemCode'] == itemCode and x['packageName'] == item['packageName']), None)
            if sv:
                category = sv['category']
                subCategory = sv['subCategory']

            price = item.get('price', 0)
            adjType = "Increment" if item['adjustType'] == "Inc" else "Decrement"
            adjustInvDict[count] = {
                'Location': location,
                'User': userEmail,
                'ID' : reqNo,
                'Adjusted Date' : entry['createTs'].date(),
                'Adjusted Time' : format_time_to_ampm(entry['createTs'].strftime("%H:%M:%S")),
                'WorkArea/Store': workArea,
                'Category' : category,
                'Sub Category' : subCategory,
                'Item Code': item['itemCode'],
                'Item Name': item['itemName'],
                'Entry Type': item['entryType'],
                'Package Name': item['packageName'],
                'UOM': item['uom'],
                'Type': adjType,
                'WAC(incl.tax,etc)': ut.truncate_and_floor(price, 2),
                'Qty': item['adjustQty'],
                'Total(incl.tax,etc)' :ut.truncate_and_floor(item['adjustQty'] * price, 2),
                'Remark Type': item.get('shortageType','Others'),
                'Remarks': item['reason']
            }
            count += 1
    df = pd.DataFrame(list(adjustInvDict.values()))

    if not df.empty:
        totals = df[['Total(incl.tax,etc)']].sum()
        df.insert(0, 'S.No', range(1, len(df) + 1))
        df = df._append(pd.Series(['Total',
            (totals['Total(incl.tax,etc)'])
        ], index=['S.No','Total(incl.tax,etc)']), ignore_index=True)
        if len(df)>0 and 'requiredColumns' in job['details']:
            df = df[job['details']['requiredColumns']]
    return df

def spoilageReport(job):
    selectedRestaurants = job['details']['selectedRestaurants']
    startDate = job['details']['startDate'].replace(hour=0, minute=0, second=0)
    endDate = job['details']['endDate'].replace(hour=23, minute=59, second=59)
    filter = {
        'restaurantId': {'$in': selectedRestaurants},
        'createTs': {'$gte': startDate, '$lte': endDate}
    }
    location1 = getLocation(job['tenantId'])
    entries =[]
    for entry in spoilages.find(filter):
        for item in entry['invItems']:
            adjType = "Increment" if item['adjustType'] == "Inc" else "Decrement"
            taxRate = item.get('taxRate', 0)
            price = item.get('price', 0)
            documentDate = entry.get('documentDate', '-')
            if documentDate != '-' :
                documentDate = convert_datetime_to_date(entry["documentDate"])
            e = {
                'Location': location1[entry['restaurantId']],
                'WorkArea/Store': entry['indentArea'],
                'User': entry['email'],
                'ID' : entry.get('id', '-'),
                'Created Date' : entry['createTs'].date(),
                'Created Time' : format_time_to_ampm(entry['createTs'].strftime("%H:%M:%S")),
                "Spoilage Document Date" : documentDate,
                'Category' : item.get('category','N/A'),
                'Sub Category' : item.get('subCategory','N/A'),
                'Item Code': item['itemCode'],
                'Item Name': item['itemName'],
                'Entry Type': item.get('entryType', 'N/A'),
                'Package Name': item.get("packageName","N/A"),
                'UOM': item.get('uom', 'N/A'),
                'Type': adjType,
                'Tax%' : taxRate,
                'Qty': item['adjustQty'],
                'WAC(incl.tax,etc)': ut.truncate_and_floor(price, 2),
                'Total(incl.tax,etc)' :ut.truncate_and_floor(price * item['adjustQty'], 2),
                'Status' : entry['status'],
                'Remarks': item['reason']
            }
            entries.append(e)
    df = pd.DataFrame(entries)
    if not df.empty:
        totals = df[['Total(incl.tax,etc)']].sum()
        df.insert(0, 'S.No', range(1, len(df) + 1))
        df = df._append(pd.Series(['Total',
            (totals['Total(incl.tax,etc)'])
        ], index=['S.No','Total(incl.tax,etc)']), ignore_index=True)
        if len(df)>0 and 'requiredColumns' in job['details']:
            df = df[job['details']['requiredColumns']]
    return df

def manualClosingReport(job, userReportsDirectory, reportType, requestDate, reportNo):
    selectedRestaurants = job['details']['selectedRestaurants']
    if job['details']['startDate'] is None:
        job['details']['startDate'] = datetime.datetime.now()
    startTime = job['details']['startDate'].replace(hour=0, minute=0, second=0)
    endTime   = job['details']['startDate'].replace(hour=23, minute=59, second=59)
    getRates = get_purchase_rates(selectedRestaurants, endTime)
    openDictWa, closingUserWorkAreaDict = getUserDictWorkArea(job, 'closing', 2)
    openDictStore, closingUserStoreDict = getUserDictStore(job, 'closing', 2)
    final = []
    workAreaDict = {}
    location1 = getLocation(job['tenantId'])
    invSearchFilter = {
        'status': {"$ne" :"discontinued"},
        'ItemType' : {'$in': ['Inventory','SubRecipe']},
        'restaurantId' : {'$in': selectedRestaurants}
    }
    orQueryForWorkAreas=[]
    for area in job['details']['selectedWorkAreas']:
        temp={}
        temp['workArea.{}'.format(area.strip())] = {'$exists': True }
        orQueryForWorkAreas.append(temp)
    if len(orQueryForWorkAreas) > 0:
        invSearchFilter['$or'] = orQueryForWorkAreas

    invItems = list(Stockvalues.find(invSearchFilter))
    for item in invItems:
        itemCode = item['itemCode']
        restaurantId = item['restaurantId']
        location = location1[restaurantId]
        if location not in workAreaDict.keys():
            workAreaDict[location] =[]

        item['entryType'] = item.get('entryType') if item.get('entryType', None) is not None else 'N/A'
        item['packageName'] = item.get('packageName') if item.get('packageName', None) is not None else 'N/A'
        item['uom'] = item.get('uom', 'N/A')
        taxRate = item.get('taxRate', 0)

        if item['ItemType'] == "SubRecipe":
            searchKey = itemCode + '|' + 'N/A' + '|' + 'N/A'
            measure = "open"
        else:
            measure = item['entryType']
            searchKey = itemCode + '|' + item['uom'].upper() + '|' + item['packageName'].upper()


        ###################### store valuation ###################
        storeQty = 0
        if item['entryType'] == "open":
            if restaurantId in openDictStore:
                if itemCode in openDictStore[restaurantId]['store'].keys():
                    storeQty = openDictStore[restaurantId]['store'][itemCode]
        else:
            if restaurantId in closingUserStoreDict:
                if searchKey in closingUserStoreDict[restaurantId]['store'].keys():
                    storeQty = closingUserStoreDict[restaurantId]['store'][searchKey][measure]

        rateKey= itemCode+'|'+item['packageName']
        price = getRates[restaurantId].get(rateKey, {'monthWac' : item.get('withTaxPrice', 0)} ).get('monthWac')
        if item['entryType'] == 'open':
            price = price / item.get('packageQty', 1)
        price = float(price)
        entry = {
            'Location' : location,
            'Category': item['category'],
            'Sub Category': item['subCategory'],
            'Item Code': itemCode,
            'HSN/SAC': item.get('hsnCode', 'N/A'),
            'Item Name': item['itemName'],
            'Entry Type': item['entryType'],
            'Package Name': item['packageName'],
            'UOM': item['uom'],
            'Tax(%)': ut.truncate_and_floor(taxRate, 2),
            'Store Stock': storeQty,
            'WAC(incl.tax,etc)' : ut.truncate_and_floor(price, 2),
            'Store Total(incl.tax,etc)': ut.truncate_and_floor(price * float(storeQty), 2)
        }

        ###################### workarea valuation ###################
        workAreaQty = 0
        for workArea in item['workArea'].keys():
            if workArea not in job['details']['selectedWorkAreas']:
                continue
            entry[workArea] = 0
            if item['entryType'] == "open":
                if restaurantId in openDictWa:
                    if workArea in openDictWa[restaurantId]:
                        if itemCode in openDictWa[restaurantId][workArea]:
                            entry[workArea] = openDictWa[restaurantId][workArea][itemCode]
            else:
                if restaurantId in closingUserWorkAreaDict:
                    if workArea in closingUserWorkAreaDict[restaurantId]:
                        if searchKey in closingUserWorkAreaDict[restaurantId][workArea]:
                            entry[workArea] = closingUserWorkAreaDict[restaurantId][workArea][searchKey][measure]

            workAreaQty += entry[workArea]
            entry['{} Total(incl.tax,etc)'.format(workArea)] = ut.truncate_and_floor(price * (entry[workArea]), 2)
            if workArea not in workAreaDict[location]:
                workAreaDict[location].append(workArea)
            if '{} Total(incl.tax,etc)'.format(workArea) not in workAreaDict[location]:
                workAreaDict[location].append('{} Total(incl.tax,etc)'.format(workArea))

        totalStockInHand = (storeQty + workAreaQty)
        entry['Grand Total(incl.tax,etc)'] = ut.truncate_and_floor(price * totalStockInHand, 2)
        entry['Total Quantity'] = ut.truncate_and_floor(totalStockInHand, 2)

        if 'showZeroStock' in job['details'] :
            if job['details']['showZeroStock'] == False:
                if totalStockInHand > 0 :
                    final.append(entry)
            else:
                final.append(entry)
        else :
            final.append(entry)

    df = pd.DataFrame(final)
    return df

def flrReport(job):
    selectedRestaurants = job['details']['selectedRestaurants']
    startTime = job['details']['startDate'].replace(hour=0, minute=0, second=0)
    endTime = job['details']['startDate'].replace(hour=23, minute=59, second=59)
    tmpClosingFilter = {
        'restaurantId' : {'$in': selectedRestaurants},
        'active': False,
        'type' :'kitchenClosing',
        'date': {'$gte': startTime, '$lte': endTime}
    }
    location1 = getLocation(job['tenantId'])
    tmpClosingData = list(tmpclosingsCol.find(tmpClosingFilter))
    checkList = ["LIQUORS", "WINE", "BEER", "LIQUOR"]
    flrList = []
    backup = {}
    stockDict = {}
    for entry in tmpClosingData:
        loc = location1[entry['restaurantId']]
        getStockDict(entry['restaurantId'], stockDict)
        if loc not in backup.keys():
            system = dailybackupsCol.find_one({
                'restaurantId': entry['restaurantId'],
                'createTs': {'$gte': startTime, '$lte': endTime}
            })
            backup[loc] = system['items'] if system else []

        for workArea in entry['workAreas']:
            for category in entry['workAreas'][workArea]['category']:
                if category in checkList:
                    for subCategory in entry['workAreas'][workArea]['category'][category]['subCategory']:
                        if "items" in entry['workAreas'][workArea]['category'][category]['subCategory'][subCategory]:
                            for itemCode in entry['workAreas'][workArea]['category'][category]['subCategory'][subCategory]['items']:
                                if "packagingSizes" in entry['workAreas'][workArea]['category'][category]['subCategory'][subCategory]['items'][itemCode]:
                                    for data in entry['workAreas'][workArea]['category'][category]['subCategory'][subCategory]['items'][itemCode]['packagingSizes']:
                                        sv = next((x for x in stockDict[entry['restaurantId']] if x['itemCode'] == itemCode and x['packageName'] == data['pkgName']), None)
                                        if sv:
                                            data['pkgQty'] = sv['packageQty']
                                        flrDict = {}
                                        flrDict['id'] = entry['_id']
                                        flrDict['Location'] = loc
                                        flrDict['WorkArea'] = workArea
                                        flrDict['Category'] = category
                                        flrDict['Sub Category'] = subCategory
                                        flrDict['Item Code'] = itemCode
                                        flrDict['Item Name'] = data['itemName']
                                        flrDict['Pkg Name'] = data['pkgName']
                                        flrDict['Pkg Qty'] = data['pkgQty']
                                        check = contains(flrList, lambda x:(x['Item Code'] == itemCode and x['Pkg Name'] == data['pkgName'] and x['WorkArea'] == workArea) )

                                        if not isinstance(data['orderedPackages'], (int, float)):
                                            data['orderedPackages'] = 0
                                        if check is not None:
                                            if ((check['id'] == entry['_id']) and (check['Location'] == loc)):
                                                check['Workarea Full Bottle (nos)'] += data['orderedPackages']
                                                check['Workarea Open Bottle (ml)'] += checkBottleWeight(data)
                                            else:
                                                check['id'] = entry['_id']
                                                check['Location'] = loc
                                                check['Workarea Full Bottle (nos)'] = 0
                                                check['Workarea Open Bottle (ml)'] = 0
                                                check['Workarea Full Bottle (nos)'] = data['orderedPackages']
                                                check['Workarea Open Bottle (ml)'] = checkBottleWeight(data)
                                        else:
                                            flrDict['Workarea Full Bottle (nos)'] = data['orderedPackages']
                                            flrDict['Workarea Open Bottle (ml)'] = checkBottleWeight(data)
                                            flrList.append(flrDict)

    df = pd.DataFrame(flrList)
    for index, row in df.iterrows():
        storePkgQty = next((i for i in backup[loc] if i.get('itemCode') == row['Item Code'] and i.get('packageName') == row["Pkg Name"] and i.get('entryType') == 'package'), {})
        storeOpenQty = next((i for i in backup[loc] if i.get('itemCode') == row['Item Code'] and i.get('packageName') == row["Pkg Name"] and i.get('entryType') == 'open'), {})
        store_full = row["Workarea Full Bottle (nos)"] + storePkgQty.get("inStock", 0)
        store_open = row["Workarea Open Bottle (ml)"] + storeOpenQty.get("inStock", 0)
        formatted = full_open_ml_to_bottle(store_full, store_open, (row['Pkg Qty'] * 1000))

        df.at[index, "Store Full Bottle (nos)"] = storePkgQty.get("inStock", 0)
        df.at[index, "Store Open Bottle (ml)"] = storeOpenQty.get("inStock", 0)
        df.at[index, "Store + Workarea Full Bottle (nos)"] = formatted[0]
        df.at[index, "Store + Workarea Open Bottle (ml)"] = formatted[1]

    if len(flrList) > 0:
        df = df[[
            "Location",
            "WorkArea",
            "Category",
            "Sub Category",
            "Item Code",
            "Item Name",
            "Pkg Name",
            "Pkg Qty",
            "Store Full Bottle (nos)",
            "Store Open Bottle (ml)",
            "Workarea Full Bottle (nos)",
            "Workarea Open Bottle (ml)",
            "Store + Workarea Full Bottle (nos)",
            "Store + Workarea Open Bottle (ml)"
        ]]
    return df 

def intraBranchTransferReport(job):
    #### oly for inventory items is supported
    stockDict ={}
    selectedRestaurants = job['details']['selectedRestaurants']
    startDate = job['details']['startDate'].replace(hour=0, minute=0, second=0)
    endDate = job['details']['endDate'].replace(hour=23, minute=59, second=59)
    query = {
        'restaurantId': {'$in': selectedRestaurants},
        'date': {'$gte': startDate,'$lte': endDate}
    }
    location1 = getLocation(job['tenantId'])
    getRates = get_purchase_rates(selectedRestaurants, endDate)
    transferRecords = list(intrabranchtransfersCol.find(query, {"_id": 0}))
    transferList = []
    for record in transferRecords:
        restaurantId = record["restaurantId"]
        getStockDict(restaurantId, stockDict)
        for item, value in record["items"].items():
            parts = item.split('|')
            if len(parts) == 3:
                itemCode, packageName, entryType = parts
            elif len(parts) == 4:
                itemCode, packageName, entryType = parts[1], parts[2], parts[3]
            if '#' in packageName :
                packageName = packageName.replace('#','.')
            category ='N/A'
            subCategory ='N/A'
            sv = next((x for x in stockDict[restaurantId] if x['itemCode'] == itemCode and x['packageName'] == packageName), None)
            if sv:
                category = sv['category']
                subCategory = sv['subCategory']
                itemName = sv['itemName']
                sv_price = sv['withTaxPrice']
                packageQty = sv['packageQty']

            rateKey= itemCode+'|'+packageName
            wac = float(getRates[restaurantId].get(rateKey, {'monthWac': sv_price}).get('monthWac'))
            if entryType == 'open':
                wac = wac / packageQty
            remarks = record.get('remarks', '')
            if remarks:
                remarks = f"{remarks}"
            transferDocumentDate = record.get('transferDocumentDate', '')
            if transferDocumentDate != '':
                transferDocumentDate = f"{transferDocumentDate}"
                transferDocumentDate = transferDocumentDate[:10]
            tempObj = {
                "Location" : location1[restaurantId],
                "Source" : record["sourceWorkArea"],
                "Destination" : record["destinationWorkArea"],
                "Transfer Document Date" :  transferDocumentDate,
                "Transferred Date" : convert_datetime_to_date(record["date"] ),
                'Transferred Time' :format_time_to_ampm(record["date"].strftime("%H:%M:%S")),
                "Category" : category,
                "Sub Category" : subCategory,
                "Item Code" : itemCode,
                "Item Name" : itemName,
                "Entry Type" : entryType,
                "Package Name" : packageName,
                "Transfered Qty" : value,
                "WAC(incl.tax,etc)" : ut.truncate_and_floor(wac, 2),
                'Total(incl.tax,etc)' :ut.truncate_and_floor(value * wac, 2),
                "Remarks" : remarks
            }
            transferList.append(tempObj)
    df = pd.DataFrame(transferList)
    if not df.empty:
        totals = df[['Total(incl.tax,etc)']].sum()
        df.insert(0, 'S.No', range(1, len(df) + 1))
        df = df._append(pd.Series(['Total',
            (totals['Total(incl.tax,etc)'])
        ], index=['S.No','Total(incl.tax,etc)']), ignore_index=True)
    if len(df)>0 and 'requiredColumns' in job['details']:
        df = df[job['details']['requiredColumns']]
    return df

def rtsReport(job):
    stockDict ={}
    selectedRestaurants = job['details']['selectedRestaurants']
    startDate = job['details']['startDate'].replace(hour=0, minute=0, second=0)
    endDate = job['details']['endDate'].replace(hour=23, minute=59, second=59)
    query = {
        'restaurantId': {'$in': selectedRestaurants},
        'createTs': {'$gte': startDate,'$lte': endDate}
    }
    transferRecords = list(rts.find(query, {"_id": 0}))
    transferList = []
    for record in transferRecords:
        restaurantId = record["restaurantId"]
        getStockDict(restaurantId, stockDict)
        for item in record["items"]:
            tempObj = {
                "Location" : record['restaurantId'],
                "Source" : record["workArea"],
                "ID" : record["RTSId"],
                "Transferred Date" : convert_datetime_to_date(record["createTs"] ),
                "Transfer Document Date" : convert_datetime_to_date(record["documentDate"]),
                "Category" : item['category'],
                "Sub Category" : item['subCategory'],
                "Item Code" : item['itemCode'],
                "Item Name" : item['itemName'],
                "Package Name" : item['packageName'],
                'Tax%' : item['taxRate'],
                "Transfered Qty" : item['transferQty'],
                "WAC(incl.tax,etc)" : ut.truncate_and_floor(item['price'], 2),
                'Total(incl.tax,etc)' :ut.truncate_and_floor((item['price'] * item['transferQty']), 2)
            }
            tempObj['Transferred Time'] = format_time_to_ampm(record["createTs"].strftime("%H:%M:%S"))
            transferList.append(tempObj)
    df = pd.DataFrame(transferList)
    if len(df) > 0:
        columns = [
            "Location",
            "Source",
            "Transferred Date",
            "Transfer Document Date",
            "Transferred Time",
            "ID",
            "Category",
            "Sub Category",
            "Item Code",
            "Item Name",
            "Package Name",
            'Tax%',
            "Transfered Qty",
            'WAC(incl.tax,etc)',
            'Total(incl.tax,etc)'
        ]
        df = df[columns]
        if not df.empty:
            totals = df[['Total(incl.tax,etc)']].sum()
            df.insert(0, 'S.No', range(1, len(df) + 1))
            df = df._append(pd.Series(['Total',
                (totals['Total(incl.tax,etc)'])
            ], index=['S.No','Total(incl.tax,etc)']), ignore_index=True)
        if len(df)>0 and 'requiredColumns' in job['details']:
            df = df[job['details']['requiredColumns']]
    else:
        df = pd.DataFrame()
    return df

def grnStatusReport(job, delete=False, PI=False):
    stockDict ={}
    tenantEntry = tenantsCol.find_one({'tenantId': job['tenantId']})
    startDate = job['details']['startDate'].replace(hour=0, minute=0, second=0)
    endDate = job['details']['endDate'].replace(hour=23, minute=59, second=59)
    vendorDict = {}
    location1 = getLocation(job['tenantId'])
    _category = [str(category).lower() for category in job['details']['selectedCategories'] if not str(category).isnumeric()]
    _subCategory = [str(subCategory).lower() for subCategory in job['details']['selectedSubCategories'] if not str(subCategory).isnumeric()]

    if 'selectedBaseDate' in job['details'] and job['details']['selectedBaseDate'] == 'deliveryDate':
        baseDate = 'createTs'
        systemEntryDate = 'Based on GRN Date(System Entry Date)'
        vendorInvoiceDate = 'Vendor Invoice Date'
        grnDate = 'Goods Received Date'
    elif 'selectedBaseDate' in job['details'] and job['details']['selectedBaseDate'] == 'invoiceDate':
        baseDate = 'invoiceDate'
        systemEntryDate = 'GRN Date(System Entry Date)'
        vendorInvoiceDate = 'Based on Vendor Invoice Date'
        grnDate = 'Goods Received Date'
    elif 'selectedBaseDate' in job['details'] and job['details']['selectedBaseDate'] == 'grnDate':
        baseDate = 'grnDocumentDate'
        systemEntryDate = 'GRN Date(System Entry Date)'
        vendorInvoiceDate = 'Vendor Invoice Date'
        grnDate = 'Based on Goods Received Date'
    else:
        baseDate = 'createTs'
        systemEntryDate = 'Based on GRN Date(System Entry Date)'
        vendorInvoiceDate = 'Vendor Invoice Date'
        grnDate = 'Goods Received Date'

    for vendor in tenantEntry['vendors']:
        vendorDict[vendor['vendorId']] = vendor['vendorName']
    grnSearchFilter = {
        'restaurantId': { '$in': job['details']['selectedRestaurants']},
        'grnType': 'po',
        baseDate :{'$gte': startDate, '$lte': endDate}
    }
    if delete:
        grns = deletedgrnsCol.find(grnSearchFilter).sort([('_id', 1)])
        all_tax_names = set(grnsCol.distinct("otherTax.taxName",grnSearchFilter))
    elif PI:
        q1 ={
            'restaurantId': { '$in': job['details']['selectedRestaurants']},
            'createTs' :{'$gte': startDate, '$lte': endDate}
        }
        grns = purchaseinvoicesCol.find(q1).sort([('_id', 1)])
        all_tax_names = set(grnsCol.distinct("otherTax.taxName",q1))
    else:
        grns = grnsCol.find(grnSearchFilter).sort([('_id', 1)])
        all_tax_names = set(grnsCol.distinct("otherTax.taxName",grnSearchFilter))

    isApproval = False

    # Initialize variables
    batch_size = 5000  # Set batch size
    entries = []       # Buffer for accumulating rows
    df = pd.DataFrame(columns=['Location', 'Category', 'Sub Category', 'Vendor Id', 'Vendor Name', 'Invoice Id',
                            'PO Id', 'GRN Id', 'PI Id', 'Creator', systemEntryDate,
                            'Item Code', 'Ledger', 'HSN/SAC', 'Item Name', 'Package Name', 'Order Qty',
                            'Received Qty','Return Qty', 'Pending Qty', 'Unit Cost', 'Tax(%)', 'Total(excl.tax)',
                            'Tax Amount', 'Cess', 'Discount', 'Extra Charges', 'Total(incl.tax,etc)',
                            vendorInvoiceDate, grnDate, 'Transportation',
                            'Approval Status', 'Approval Status Detailed', 'Remarks', 'Payment Terms',
                            'PO Terms', 'Payment Methods', 'Grand Total'])

    for grn in grns:
        getStockDict(grn['restaurantId'], stockDict)
        approvalStatus = None
        appStatus = None
        other_tax_total = 0

        if PI:
            _entry = grn['items']
            _approval_key = "piApprovalSetting"
        else:
            _entry = grn['grnItems']
            _approval_key = "grnApprovalSetting"

        if _approval_key in grn and grn[_approval_key] is not None:
            approval_status_lines = []
            approvals = []
            for row in grn[_approval_key]:
                cat = row.get('category', 'N/A').upper()
                level = row.get('level', 'N/A').upper()
                role = row.get('role', 'N/A')
                status = row.get('status', 'N/A').upper()
                row_string = f"{status}    : {cat} - {level} - {role}"
                approval_status_lines.append(row_string)
                approvals.append(status)
            if len(approval_status_lines) > 0:
                approvalStatus = '\n'.join(approval_status_lines)
                if "REJECTED" in approvals:
                    appStatus = "REJECTED"
                elif "PENDING" in approvals:
                    appStatus = "PENDING"
                elif "APPROVED" in approvals:
                    appStatus = "APPROVED"

        for _index, item in enumerate(_entry):
            category = 'N/A'
            subCategory = 'N/A'
            ledger = 'N/A'
            itemCode = item['itemCode']
            sv = next((x for x in stockDict[grn['restaurantId']] if x['itemCode'] == itemCode), None)
            if sv:
                category = sv['category']
                subCategory = sv['subCategory']
                ledger = sv['ledger'].upper()

            for package in item['packages']:
                tax_rate_value = item.get('taxRate', None)
                taxRate = convert_to_float(tax_rate_value)
                cess = item['cessAmt'] if 'cessAmt' in item else 0
                discount = item['discAmt'] if 'discAmt' in item else 0
                extra = item['extraAmt'] if 'extraAmt' in item else 0
                rtvQty = item['RTVQty'] if 'RTVQty' in item else 0
                item['receivedQty'] = item.get('receivedQty',0)
                rtvTotal = 0 if item['receivedQty'] == 0 else rtvQty * (item['totalPrice'] / item['receivedQty'])
                if item['receivedQty'] == 0:
                    continue
                item['quantity'] = item.get('quantity', item['receivedQty'])
                priceType = f"({item.get('priceType')})" if item.get('priceType') else ''

                if category.lower() not in _category and ("all" not in _category):
                    continue
                if subCategory.lower() not in _subCategory and ("all" not in _subCategory):
                    continue

                entry = {
                    'Location': location1[grn['restaurantId']],
                    'Category': category,
                    'Sub Category': subCategory,
                    'Vendor Id': grn['vendorId'],
                    'Vendor Name': vendorDict.get(grn['vendorId'], 'vendor not found'),
                    'Invoice Id': grn['invoiceId'],
                    'PO Id': grn['details']['poId'],
                    'GRN Id': grn['grnId'],
                    'PI Id': grn.get('piId', 'N/A'),
                    'Creator': grn.get('creator', '-'),
                    systemEntryDate: grn['createTs'].date(),
                    'Item Code': item['itemCode'],
                    'Ledger': ledger,
                    'HSN/SAC': item.get('hsnCode', '-'),
                    'Item Name': f"{item['itemName']}{priceType}",
                    'Package Name': package['packageName'],
                    'Order Qty': item['quantity'],
                    'Received Qty': item['receivedQty'],
                    'Return Qty': rtvQty,
                    'Pending Qty': item['quantity'] - item['receivedQty'],
                    'Unit Cost': ut.truncate_and_floor(float(item['subTotal']) / float(item['receivedQty']), 2),
                    'Tax(%)': taxRate,
                    'Total(excl.tax)': ut.truncate_and_floor(item['subTotal'], 2),
                    'Tax Amount': item['taxAmount'],
                    'Cess': cess,
                    'Discount': discount,
                    'Extra Charges': extra,
                    'Return Total': rtvTotal,
                    'Total(incl.tax,etc)': item['totalPrice'],
                    vendorInvoiceDate: convert_datetime_to_date(grn.get('invoiceDate')),
                    grnDate: convert_datetime_to_date(grn.get('grnDocumentDate')),
                    'Transportation': ut.truncate_and_floor(float(grn['otherCharges']) / len(_entry), 2),
                    'Approval Status': appStatus if appStatus is not None else np.nan,
                    'Approval Status Detailed': approvalStatus if approvalStatus is not None else np.nan,
                    'Remarks': grn.get('remarks', ''),
                    'Payment Terms': grn.get('paymentTerms', ''),
                    'PO Terms': grn['poTerms'] if "poTerms" in grn.keys() else "",
                    'Payment Methods': grn['paymentMethod'] if "paymentMethod" in grn.keys() else ""
                }

                for tax_name in all_tax_names:
                    entry[tax_name] = 0
                if 'otherTax' in grn:
                    other_tax_total = 0
                    for tax in grn['otherTax']:
                        tax_name = tax['taxName']
                        if tax_name in all_tax_names:
                            entry[tax_name] = float(tax['value']) / len(_entry)
                            tax_value = float(tax['value']) / len(_entry)
                            other_tax_total += tax_value

                entry['Grand Total'] = ut.truncate_and_floor(entry['Total(incl.tax,etc)'] + entry['Transportation'] + other_tax_total ,2)
                entries.append(entry)

                if len(entries) >= batch_size:
                    df = pd.concat([df, pd.DataFrame(entries)], ignore_index=True)

                    entries.clear()

    if entries:
        df = pd.concat([df, pd.DataFrame(entries)], ignore_index=True)


    if len(df) > 0:
        primary = [
            'Location',
            'Vendor Id',
            'Vendor Name',
            'Invoice Id',
            vendorInvoiceDate,
            grnDate,
            systemEntryDate,
            'Creator',
            'PO Id',
            'GRN Id',
            'Category',
            'Sub Category',
            'Ledger',
            'Item Code',
            'HSN/SAC',
            'Item Name',
            'Package Name',
            'Order Qty' ,
            'Received Qty',
            'Return Qty'
            'Pending Qty',
            'Unit Cost',
            'Tax(%)',
            'Tax Amount',
            'Cess',
            'Discount',
            'Extra Charges',
            'Total(excl.tax)',
            'Return Total',
            'Total(incl.tax,etc)',
            'Transportation',
            'Grand Total',
            'Remarks',
            'Payment Terms',
            'PO Terms',
            'Payment Methods',
        ]
        if isApproval:
            primary.append('Approval Status')
            primary.append('Approval Status Detailed')
        if PI:
            primary.insert(9, 'PI Id')
    else:
        df = pd.DataFrame()

    return df

def rtvReport(job, delete=False):
    selectedRestaurants = job['details']['selectedRestaurants']
    startDate = job['details']['startDate'].replace(hour=0, minute=0, second=0)
    endDate = job['details']['endDate'].replace(hour=23, minute=59, second=59)
    filter = {
        'restaurantId': {'$in': selectedRestaurants},
        'createdAt': {'$gte': startDate, '$lte': endDate}
    }
    location1 = getLocation(job['tenantId'])
    if delete:
        rtvList = list(db.deletedrtvCol.find(filter))
    else:
        rtvList = list(db.rtvdataCol.find(filter))
    entries =[]
    for entry in rtvList:
        for item in entry['grnItems']:
            cess = item['cessAmt'] if 'cessAmt' in item else 0
            discount = item['discAmt'] if 'discAmt' in item else 0
            extra = item['extraAmt'] if 'extraAmt' in item else 0
            remark = item['reason'] if 'reason' in item else "N/A"
            for package in item['packages']:
                e = {
                    'Location': location1.get(entry['restaurantId'], 'Location not found'),
                    'RTV Id': entry['rtvId'],
                    'Credit Note No': entry['creditNo'],
                    'Credit Note Date': (entry['creditDate']).date(),
                    'RTV Date': (entry['returnDate']).date(),
                    'Created Date': (entry['createdAt']).date(),
                    'Created Time': format_time_to_ampm(entry['createdAt'].strftime("%H:%M:%S")),
                    'Created By': entry['createdBy'],
                    'Item Code': item['itemCode'],
                    'Item Name': item['itemName'],
                    'Package Name': package["packageName"],
                    'Received Qty': item['quantity'],
                    'Return Qty': item['receivedQty'],
                    'Unit Cost': package["packagePrice"],
                    'Tax Amount': item['taxAmount'],
                    'Cess': cess,
                    'Discount': discount,
                    'Extra Charges': extra,
                    'Total(incl.tax,etc)' : item['totalPrice'],
                    'Item Remark': remark,
                    'RTV Remark': entry['remarks'] if 'remarks' in entry else "N/A"
                }
                if delete:
                    e['Deleted By'] = entry.get('deletedBy')
                    e['Deleted Date'] = entry.get('deletedDate').date()
                    e['Deleted Time'] = format_time_to_ampm(entry['deletedDate'].strftime("%H:%M:%S")).strip("(),'")
                entries.append(e)
    df = pd.DataFrame(entries)
    if not df.empty:
        totals = df[['Total(incl.tax,etc)']].sum()
        df.insert(0, 'S.No', range(1, len(df) + 1))
        df = df._append(pd.Series(['Total',
            (totals['Total(incl.tax,etc)'])
        ], index=['S.No','Total(incl.tax,etc)']), ignore_index=True)
        if len(df)>0 and 'requiredColumns' in job['details']:
            df = df[job['details']['requiredColumns']]
    return df

def supplierBillWise(job, userReportsDirectory, reportType, requestDate, reportNo):
    tenantEntry = tenantsCol.find_one({'tenantId': job['tenantId']})
    startDate = job['details']['startDate'].replace(hour=0, minute=0, second=0)
    endDate = job['details']['endDate'].replace(hour=23, minute=59, second=59)
    vendorDict = defaultdict(lambda: 'vendor not found')
    location1 = getLocation(job['tenantId'])
    baseDateMap = {
        'deliveryDate': ('createTs', ' | Based on GRN Date(System Entry Date)', 'Based on GRN Date(System Entry Date)', 'Vendor Invoice Date', 'Goods Received Date'),
        'invoiceDate': ('invoiceDate', ' | Based on Vendor Invoice Date', 'GRN Date(System Entry Date)', 'Based on Vendor Invoice Date', 'Goods Received Date'),
        'grnDate': ('grnDocumentDate', ' | Based on Goods Received Date', 'GRN Date(System Entry Date)', 'Vendor Invoice Date', 'Based on Goods Received Date')
    }
    selectedBaseDate = job['details'].get('selectedBaseDate', 'deliveryDate')
    baseDate, headerDateTitle, systemEntryDate, vendorInvoiceDate, grnDate = baseDateMap.get(selectedBaseDate, baseDateMap['deliveryDate'])
    for vendor in tenantEntry['vendors']:
        vendorDict[vendor['vendorId']] = vendor['vendorName']
    grnSearchFilter = {
        'restaurantId': {'$in': job['details']['selectedRestaurants']},
        'vendorId': {'$in': job['details']['selectedVendors']},
        'grnType': 'po',
        baseDate: {'$gte': startDate, '$lte': endDate}
    }
    grns = grnsCol.find(grnSearchFilter).sort([('_id', 1)])

    def generate_grn_entries(grns):
        for grn in grns:
            entry = {
                'Location': location1.get(grn['restaurantId'], 'Location not found'),
                'Vendor ID': grn['vendorId'],
                'Vendor Name': vendorDict.get(grn['vendorId']),
                'Invoice ID': grn['invoiceId'],
                'PO ID': grn['details']['poId'],
                'GRN ID': grn['grnId'],
                systemEntryDate: str(convert_datetime_to_date(grn.get('createTs'))),
                vendorInvoiceDate: str(convert_datetime_to_date(grn.get('invoiceDate'))),
                grnDate: str(convert_datetime_to_date(grn.get('grnDocumentDate'))),
                'Transportation': grn.get('otherCharges', 0),
                'Grand Total': grn.get('grandTotal', 0),
                'Creator': grn.get('creator', '-'),
                'Other Taxes': grn.get('otherTax', []),
                'items': []
            }
            entry['creditNos'] = db.rtvList.distinct("creditNo",{'restaurantId': grn['restaurantId'],'grnId':grn['grnId']})
            for item in grn.get('grnItems', []):
                for package in item.get('packages', []):
                    taxRate = convert_to_float(item.get('taxRate', 0))
                    cess = item.get('cessAmt', 0)
                    discount = item.get('discAmt', 0)
                    extra = item.get('extraAmt', 0)
                    receivedQty = item.get('receivedQty', 0)
                    if receivedQty == 0:
                        continue
                    priceType = f"({item.get('priceType')})" if item.get('priceType') else ''
                    entry['items'].append({
                        'Item Code': item['itemCode'],
                        'Item Name': f"{item['itemName']}{priceType}",
                        'Package Name': package['packageName'],
                        'Quantity': receivedQty,
                        'Unit Cost': ut.truncate_and_floor(float(item['subTotal']) / receivedQty ,2),
                        'Tax(%)': taxRate,
                        'Total(excl.tax)': ut.truncate_and_floor(item.get('subTotal', 0), 2),
                        'Tax Amount': item.get('taxAmount', 0),
                        'Cess': cess,
                        'Discount': discount,
                        'Extra Charges': extra,
                        'Credit Note Amount': item.get('RTVAmount', 0),
                        'Total(incl.tax,etc)': (item.get('totalPrice', 0) - item.get('RTVAmount', 0))
                    })
            yield entry

    dateAndTime = getCreatedTime(job)
    output_file = f"{userReportsDirectory}/{reportNo}_{reportType}_{dateAndTime}.xlsx"
    writer = pd.ExcelWriter(output_file, engine='xlsxwriter')
    workbook = writer.book
    worksheet = workbook.add_worksheet('Invoices')

    # Define formats
    summary_format = workbook.add_format({'bold': True, 'border': 1, 'align': 'center', 'font_size': 12})
    summary_format1 = workbook.add_format({'bold': True, 'border': 1, 'align': 'center', 'font_size': 10})
    header_format = workbook.add_format({'bold': True, 'bg_color': '#02b8d4', 'border': 1, 'align': 'center', 'font_size': 10})
    item_header_format = workbook.add_format({'bold': True, 'bg_color': '#e6e8e8', 'border': 1, 'align': 'center', 'font_size': 10})
    cell_format = workbook.add_format({'border': 1, 'text_wrap': True, 'font_size': 10})
    parent_format = workbook.add_format({'border': 1,'bold': True, 'font_size': 10})
    total_format = workbook.add_format({'bold': True, 'bg_color': '#e6e8e8', 'border': 1, 'align': 'right', 'font_size': 10})
    invoice_total_format = workbook.add_format({'bold': True, 'bg_color': '#e6e8e8', 'border': 1, 'align': 'right', 'font_size': 10})

    # Write summary information
    worksheet.merge_range('A1:H1', tenantEntry['tenantName'].upper(), summary_format)
    worksheet.merge_range('A2:H2', 'Supplier Bill Wise'.upper(), summary_format)
    genAt = job['createTs'].strftime("%d-%m-%Y/%I:%M_%p")
    startAt = job['details']['startDate'].strftime('%d-%b-%Y')
    endAt = job['details']['endDate'].strftime('%d-%b-%Y')
    worksheet.merge_range('A3:H3', f"Generated On :{genAt} | {startAt} to {endAt} {headerDateTitle}", summary_format1)
    row = 4

    # Initialize grand total dictionary with other taxes
    grand_total = {
        'Total(excl.tax)': 0,
        'Cess': 0,
        'Discount': 0,
        'Extra Charges': 0,
        'Transportation': 0,
        'Credit Note Amount': 0,
        'Total(incl.tax,etc)': 0,
        'Other Taxes': defaultdict(float)  # Track other taxes across all entries
    }

    for entry in generate_grn_entries(grns):
        worksheet.merge_range(row, 0, row, 2,f"Location: {entry['Location']}",parent_format)
        worksheet.merge_range(row, 3, row, 8,f"Creator: {entry['Creator']}",parent_format )
        row += 1
        worksheet.merge_range(row, 0, row, 2,f"PO ID: {entry['PO ID']}",parent_format)
        worksheet.merge_range(row, 3, row, 8,f"{systemEntryDate}: {entry[systemEntryDate]}", parent_format)
        row += 1
        worksheet.merge_range(row, 0, row, 2,f"GRN ID: {entry['GRN ID']}", parent_format)
        worksheet.merge_range(row, 3, row, 8,f"{grnDate}: {entry[grnDate]}", parent_format)
        row += 1
        worksheet.merge_range(row, 0, row, 2, f"Vendor ID: {entry['Vendor ID']}",parent_format)
        worksheet.merge_range(row, 3, row, 8,f"Invoice ID: {entry['Invoice ID']}",parent_format)
        row += 1
        worksheet.merge_range(row, 0, row, 2,f"Vendor Name: {entry['Vendor Name']}", parent_format)
        worksheet.merge_range(row, 3, row, 8,f"{vendorInvoiceDate}: {entry[vendorInvoiceDate]}",parent_format)
        row += 1
        credit_nos = ", ".join(entry['creditNos']) if entry['creditNos'] else "N/A"
        worksheet.merge_range(row, 0, row, 2,f"Credit Note: {credit_nos}", parent_format)
        row += 2

        worksheet.write(row, 0, 'Item Code', item_header_format)
        worksheet.write(row, 1, 'Item Name', item_header_format)
        worksheet.write(row, 2, 'Pkg Name', item_header_format)
        worksheet.write(row, 3, 'Quantity', item_header_format)
        worksheet.write(row, 4, 'Unit Cost', item_header_format)
        worksheet.write(row, 5, 'Tax(%)', item_header_format)
        worksheet.write(row, 6, 'Total(excl.tax)', item_header_format)
        worksheet.write(row, 7, 'Cess', item_header_format)
        worksheet.write(row, 8, 'Discount', item_header_format)
        worksheet.write(row, 9, 'Credit Note Amount', item_header_format)
        worksheet.write(row, 10, 'Extra Charges', item_header_format)
        worksheet.write(row, 11, 'Total(incl.tax,etc)', item_header_format)
        row += 1

        subtotal = 0
        cessSum = 0
        discSum = 0
        extraSum = 0
        creditSum = 0
        totalSum = 0

        for item in entry["items"]:
            worksheet.write(row, 0, item["Item Code"], cell_format)
            worksheet.write(row, 1, item["Item Name"], cell_format)
            worksheet.write(row, 2, item["Package Name"], cell_format)
            worksheet.write(row, 3, item["Quantity"], cell_format)
            worksheet.write(row, 4, item["Unit Cost"], cell_format)
            worksheet.write(row, 5, item["Tax(%)"], cell_format)
            worksheet.write(row, 6, item["Total(excl.tax)"], cell_format)
            worksheet.write(row, 7, item["Cess"], cell_format)
            worksheet.write(row, 8, item["Discount"], cell_format)
            worksheet.write(row, 9, item["Credit Note Amount"], cell_format)
            worksheet.write(row, 10, item["Extra Charges"], cell_format)
            worksheet.write(row, 11, item["Total(incl.tax,etc)"], cell_format)
            subtotal += item["Total(excl.tax)"]
            cessSum += item["Cess"]
            discSum += item["Discount"]
            extraSum += item["Extra Charges"]
            creditSum += item["Credit Note Amount"]
            totalSum += item["Total(incl.tax,etc)"]
            row += 1

        transportation = entry.get("Transportation", 0)

        # Write subtotal
        worksheet.write(row, 10, 'Subtotal', total_format)
        worksheet.write(row, 11, totalSum, total_format)
        row += 1

        # Write transportation
        worksheet.write(row, 10, 'Transportation', total_format)
        worksheet.write(row, 11, transportation, total_format)
        row += 1

        # Write other taxes
        other_taxes_total = 0
        for tax in entry.get('Other Taxes', []):
            tax_name = tax.get('taxName', 'Unknown Tax')
            tax_value = float(tax.get('value', 0))
            worksheet.write(row, 10, f"Other Tax ({tax_name})", total_format)
            worksheet.write(row, 11, tax_value, total_format)
            other_taxes_total += tax_value
            grand_total['Other Taxes'][tax_name] += tax_value
            row += 1

        # Write grand total
        worksheet.write(row, 10, 'Grand Total', invoice_total_format)
        worksheet.write(row, 11, (totalSum + transportation + other_taxes_total), invoice_total_format)

        # Update grand totals
        grand_total['Total(excl.tax)'] += subtotal
        grand_total['Cess'] += cessSum
        grand_total['Discount'] += discSum
        grand_total['Extra Charges'] += extraSum
        grand_total['Credit Note Amount'] += creditSum
        grand_total['Transportation'] += transportation
        grand_total['Total(incl.tax,etc)'] += totalSum

        row += 2

    # Write overall summary
    worksheet.merge_range(row, 0, row, 11, 'Overall Summary', summary_format)
    row += 1

    # Write summary headers
    base_columns = ['Total(excl.tax)', 'Cess', 'Discount', 'Extra Charges', 'Transportation','Credit Note Amount']
    other_tax_names = sorted(grand_total['Other Taxes'].keys())
    all_columns = base_columns + other_tax_names + ['Total (incl.tax,etc)', 'Grand Total']

    # Write headers
    for col, header in enumerate(all_columns):
        worksheet.write(row, col, header, header_format)
    row += 1

    # Write values
    for col, header in enumerate(base_columns):
        worksheet.write(row, col, grand_total[header], cell_format)

    # Write other tax values
    col = len(base_columns)
    total_other_taxes = 0
    for tax_name in other_tax_names:
        tax_value = grand_total['Other Taxes'][tax_name]
        worksheet.write(row, col, tax_value, cell_format)
        total_other_taxes += tax_value
        col += 1

    # Write totals
    worksheet.write(row, col, grand_total['Total(incl.tax,etc)'], cell_format)
    col += 1
    final_grand_total = grand_total['Total(incl.tax,etc)'] + grand_total['Transportation'] + total_other_taxes
    worksheet.write(row, col, final_grand_total, cell_format)

    # Set column widths
    column_widths = [12, 24, 10, 8, 8, 6, 12, 6, 6, 8, 16, 14]
    for i, width in enumerate(column_widths):
        worksheet.set_column(i, i, width)

    writer._save()

def billWiseReport(job, userReportsDirectory, reportType, requestDate, reportNo):
    startDate = job['details']['startDate'].replace(hour=0, minute=0, second=0)
    endDate = job['details']['endDate'].replace(hour=23, minute=59, second=59)

    if 'selectedBaseDate' in job['details'] and job['details']['selectedBaseDate'] == 'invoiceDate':
        baseDate = 'invoiceDate'
        systemEntryDate = 'GRN Date(System Entry Date)'
        headerDateTitle = ' | Based on Vendor Invoice Date'
        vendorInvoiceDate = 'Based on Vendor Invoice Date'
        searchFilter = 'Based on Vendor Invoice Date'
        grnDate = 'Goods Received Date'
    elif 'selectedBaseDate' in job['details'] and job['details']['selectedBaseDate'] == 'grnDate':
        baseDate = 'grnDocumentDate'
        systemEntryDate = 'GRN Date(System Entry Date)'
        vendorInvoiceDate = 'Vendor Invoice Date'
        headerDateTitle = ' | Based on Goods Received Date'
        grnDate = 'Based on Goods Received Date'
        searchFilter = 'Based on Goods Received Date'
    else:
        baseDate = 'createTs'
        headerDateTitle = ' | Based on GRN Date(System Entry Date)'
        systemEntryDate = 'Based on GRN Date(System Entry Date)'
        searchFilter = 'Based on GRN Date(System Entry Date)'
        vendorInvoiceDate = 'Vendor Invoice Date'
        grnDate = 'Goods Received Date'

    filters = {
        "restaurantId": {'$in': job['details']['selectedRestaurants']},
        'vendorId': {'$in': job['details']['selectedVendors']},
        baseDate: {"$gte": startDate, "$lte": endDate},
        'grnType': 'po'
    }
    location1 = getLocation(job['tenantId'])
    grnList = list(grnsCol.aggregate([
        {'$match': filters},
        {"$addFields": {"amount": {"$sum": "$grnItems.totalPrice"}}},
        {"$addFields": {"status": "$status.inwardStatus"}},
        {"$lookup": {
            "from": "tenants",
            "let": {"tenantId": "$vendorId", "servingTenantId": "$tenantId"},
            "pipeline": [{"$match": {"$expr": {"$and": [
                {"$eq": ["$tenantId", "$$tenantId"]},
                {"$eq": ["$servingTenantId", "$$servingTenantId"]}
            ]}}}],
            "as": "vendorDetails"
        }},
        {"$unwind": "$vendorDetails"},
        {"$addFields": {"vendorName": "$vendorDetails.tenantName"}},
        {"$unwind": "$grnItems"},
        {"$group": {
            "_id": "$_id",
            "Location": {"$first": "$restaurantId"},
            "Vendor Id": {"$first": "$vendorId"},
            "Vendor Name": {"$first": "$vendorName"},
            "Invoice Id": {"$first": "$invoiceId"},
            vendorInvoiceDate: {"$first": {
                "$dateToString": {
                    "format": "%Y-%m-%d",
                    "date": "$invoiceDate"
                }
            }},
            searchFilter: {"$first": {
                "$dateToString": {
                    "format": "%Y-%m-%d",
                    "date": "$invoiceDate"
                }
            }},
            systemEntryDate: {"$first": {
                "$dateToString": {
                    "format": "%Y-%m-%d",
                    "date": "$createTs"
                }
            }},
            grnDate: {"$first": {
                "$dateToString": {
                    "format": "%Y-%m-%d",
                    "date": "$grnDocumentDate"
                }
            }},
            "GRN Id": {"$first": "$grnId"},
            'Total(excl tax)': {"$sum": "$grnItems.subTotal"},
            "Tax Amount": {"$sum": "$grnItems.taxAmount"},
            "Credit Note Amount": {"$sum": {"$ifNull": ["$grnItems.RTVAmount", 0]}},
            'Total(incl tax,etc)': {"$sum": "$grnItems.totalPrice"},  
            "Transportation": {"$first": {"$toDouble": "$otherCharges"}},
            "Labour": {"$first": {"$toDouble": "$labourCharges"}},
            "otherTax": {"$first": "$otherTax"},
            "Status": {"$first": "$status"}
        }},
        { "$addFields": {
            'Total(incl tax,etc)': {
                "$subtract": [
                    "$Total(incl tax,etc)",
                    "$Credit Note Amount"  
                ]
            },
            "Other Charges": {
                "$subtract": [
                    "$Total(incl tax,etc)",
                    { "$sum": ["$Total(excl tax)", "$Tax Amount"] }
                ]
            }
        }},
        {"$project": {
            "_id": 0,
            "Location": 1,
            "Vendor Id": 1,
            "Vendor Name": 1,
            "Invoice Id": 1,
            searchFilter: 1,
            systemEntryDate: 1,
            grnDate: 1,
            vendorInvoiceDate: 1,
            "GRN Id": 1,
            'Total(excl tax)': 1,
            "Tax Amount": 1,
            "Other Charges": 1,
            "Credit Note Amount": 1,
            'Total(incl tax,etc)': 1, 
            "Transportation": 1,
            "otherTax": 1,
            "Labour": 1,
            "Status": 1
        }},
        {"$sort": {searchFilter: 1}}
    ]))







    def tran(val):
        return location1[val]

    df = pd.DataFrame(grnList)
    other_tax_columns = []

    if not df.empty:
        # Convert dates
        df[vendorInvoiceDate] = pd.to_datetime(df[vendorInvoiceDate], errors='coerce')
        df[vendorInvoiceDate] = df[vendorInvoiceDate].apply(convert_datetime_to_date)

        df[systemEntryDate] = pd.to_datetime(df[systemEntryDate], errors='coerce')
        df[systemEntryDate] = df[systemEntryDate].apply(convert_datetime_to_date)

        df[grnDate] = pd.to_datetime(df[grnDate], errors='coerce')
        df[grnDate] = df[grnDate].apply(convert_datetime_to_date)

        # Transform location
        df['Location'] = df['Location'].apply(tran)

        # Handle otherTax columns if "otherTaxes" is in requiredColumns
        if 'otherTax' in df.columns:
            tax_names = set()
            for taxes in df['otherTax'].dropna():
                for tax in taxes:
                    tax_names.add(tax['taxName'])

            # Create columns for each tax
            for tax_name in sorted(tax_names):
                column_name = f"Other Tax ({tax_name})"
                other_tax_columns.append(column_name)
                df[column_name] = df['otherTax'].apply(
                    lambda x: next((float(tax['value']) for tax in x if tax['taxName'] == tax_name), 0)
                    if isinstance(x, list) else 0
                )

        # Drop the original otherTax column
        if 'otherTax' in df.columns:
            df = df.drop('otherTax', axis=1)

        # Calculate grandTotal including other taxes
        other_tax_sum = sum(df[col].fillna(0) for col in other_tax_columns)
        df['Grand Total'] = df['Total(incl tax,etc)'] + df['Transportation'].fillna(0) + df['Labour'].fillna(0) + other_tax_sum

    return df

def ibtReport(job):
    stockDict ={}
    startDate = job['details']['startDate'].replace(hour=0, minute=0, second=0)
    endDate = job['details']['endDate'].replace(hour=23, minute=59, second=59)
    selectedRestaurants = job['details']['selectedRestaurants']

    if 'selectedBaseDate' in job['details'] and job['details']['selectedBaseDate'] == 'dispatchedDate':
        baseDate = 'dispatchedDate'
        systemEntryDate = 'Requested Date'
        dispatchedDateHeader = 'Based on Dispatched Date'
        reportDateHeader = ' | Based on Dispatched Date'
        receivedDate = 'Received Date'
    elif 'selectedBaseDate' in job['details'] and job['details']['selectedBaseDate'] == 'receivedDate':
        baseDate = 'receivedDate'
        systemEntryDate = 'Requested Date'
        dispatchedDateHeader = 'Dispatched Date'
        receivedDate = 'Based on Received Date'
        reportDateHeader = ' | Based on Received Date'
    else:
        baseDate = 'createTs'
        systemEntryDate = 'Based on Requested Date'
        reportDateHeader = ' | Based on Requested Date'
        receivedDate = 'Received Date'
        dispatchedDateHeader = 'Dispatched Date'

    ibtSearchFilter = {
        'tenantId': job['tenantId'],
        baseDate: {"$gte": startDate, "$lte": endDate},
    }
    if 'isSource' in job['details'] and job['details']['isSource']:
        ibtSearchFilter['fromBranch.restaurantId'] = {'$in': selectedRestaurants}
    else:
        ibtSearchFilter['toBranch.restaurantId'] = {'$in': selectedRestaurants}

    ibtList = list(ibtsCol.find(ibtSearchFilter))
    finalList =[]
    itemPriceDict ={}
    for ibt in ibtList:
        dispatchStatus = ibt['status']['dispatched'].capitalize()
        deliveredStatus = ibt['status']['delivered'].capitalize()
        dispatchedDate = ibt.get('dispatchedDate', 'N/A')
        if dispatchedDate == 'N/A':
            dispatchedTime = 'N/A'
        else :
            dispatchedTime = format_time_to_ampm(dispatchedDate.strftime("%H:%M:%S"))
            dispatchedDate = dispatchedDate.date()
        if deliveredStatus == "Pending" or deliveredStatus == "Closed":
            deliveredDate = 'N/A'
        else :
            deliveredDate = ibt['receivedDate']

        if deliveredDate == 'N/A':
            deliveredTime = 'N/A'
        else :
            deliveredTime = format_time_to_ampm(deliveredDate.strftime("%H:%M:%S"))
            deliveredDate = deliveredDate.date()

        restaurantId = ibt['fromBranch']['restaurantId']
        if restaurantId not in itemPriceDict.keys():
            itemPriceDict[restaurantId] ={}
        getStockDict(restaurantId, stockDict)
        for item in ibt['items']:
            itemCode = item['itemCode']
            item['entryType'] = item.get('entryType') if item.get('entryType', None) is not None else 'N/A'
            item['packageName'] = item.get('packageName') if item.get('packageName', None) is not None else 'N/A'
            item['uom'] = item.get('uom', 'N/A')

            category ='N/A'
            subCategory ='N/A'
            hsn ='N/A'
            taxRate = 0
            sv = next((x for x in stockDict[restaurantId] if x['itemCode'] == itemCode and x['packageName'] == item['packageName']), None)
            if sv:
                category = sv['category']
                subCategory = sv['subCategory']
                taxRate = sv['taxRate']
                hsn =sv['hsnCode']

            caseType = 0
            if item['packageName'] != "N/A" and item['entryType'] == "package":
                caseType = 1
                searchKey = itemCode + "|" + item['packageName'].upper()
            elif item['entryType'] == "open":
                caseType = 3
                searchKey = itemCode + "|" + item['entryType'].upper()
            else:
                caseType = 3
                searchKey = itemCode

            price = float(item.get('unitPrice', 0) if item.get('unitPrice') is not None else 0)
            if price:
                price = price
            elif searchKey in itemPriceDict[restaurantId]:
                price = itemPriceDict[restaurantId][searchKey]
            else:
                svEntry = None
                if caseType == 1:
                    svEntry = Stockvalues.find_one({'restaurantId': restaurantId, 'itemCode': itemCode, 'packageName': item['packageName']})
                elif caseType ==2:
                    svEntry = Stockvalues.find_one({'restaurantId': restaurantId, 'itemCode': itemCode, 'entryType':'open'})
                else:
                    svEntry = Stockvalues.find_one({'restaurantId': restaurantId, 'itemCode': itemCode})
                if svEntry:
                    price = svEntry.get('withTaxPrice', 0)
                    itemPriceDict[restaurantId][searchKey] = price

            rececive = (item['quantity'] - item['recPendingQty']) - sum(item['shortageHistory'])
            rRubTotal = rececive * price
            dRubTotal = (item['quantity'] - item['pendingQty']) * price
            temp = {
                'Category': category,
                'Sub Category': subCategory,
                systemEntryDate: ibt['createTs'].date(),
                dispatchedDateHeader: dispatchedDate,
                receivedDate : deliveredDate,
                'ID': ibt['ibtId'],
                'Source': ibt['fromBranch']['location'],
                'Destination': ibt['toBranch']['location'],
                'Workarea' : ibt['workArea'],
                'Disptach Status': dispatchStatus,
                'Receive Status':  deliveredStatus,
                'Item Name': item['itemName'].split("|")[-1],
                'Item Code': item['itemCode'],
                'HSN/SAC': hsn,
                'Package Name': item['packageName'],
                'Requested Qty': item['quantity'],
                'Dispatched Qty': item['quantity'] - item['pendingQty'],
                'Received Qty': rececive,
                'Pending Qty': item['pendingQty'],
                "WAC(incl.tax,etc)" : ut.truncate_and_floor(price, 2),
                'Tax%' : taxRate,
                'UOM':item['uom'],
                'Dispatched Total(incl.tax,etc)' :ut.truncate_and_floor(dRubTotal, 2),
                'Received Total(incl.tax,etc)' :ut.truncate_and_floor(rRubTotal, 2)
            }
            temp['Created By'] = '-' if 'creator' in ibt and ibt['creator'] == 'undefined' else ibt.get('creator', '-')
            temp['Requested Time'] = format_time_to_ampm(ibt['createTs'].strftime("%H:%M:%S"))
            temp['Dispatched Time'] = dispatchedTime
            temp['Received Time'] = deliveredTime


            finalList.append(temp)
    df = pd.DataFrame(finalList)
    if len(df) > 0:
        df = df[[
            'ID',
            'Created By',
            'Source',
            'Destination',
            'Workarea',
            systemEntryDate,
            'Requested Time',
            dispatchedDateHeader,
            'Dispatched Time',
            receivedDate ,
            'Received Time',
            'Disptach Status' ,
            'Receive Status',
            'Category',
            'Sub Category',
            'Item Code',
            'HSN/SAC',
            'Item Name',
            'Package Name',
            'UOM',
            'Tax%',
            'Requested Qty',
            'Dispatched Qty',
            'Received Qty',
            'Pending Qty',
            'WAC(incl.tax,etc)',
            'Dispatched Total(incl.tax,etc)',
            'Received Total(incl.tax,etc)'
        ]]
    else:
        df = pd.DataFrame()
    return df

def indentIssueReport(job,delete=False):
    stockDict ={}
    selectedRestaurants = job['details']['selectedRestaurants']
    startDate = job['details']['startDate'].replace(hour=0, minute=0, second=0)
    endDate = job['details']['endDate'].replace(hour=23, minute=59, second=59)
    location1 = getLocation(job['tenantId'])

    queries = []
    if 'selectedBaseDate' in job['details'] and job['details']['selectedBaseDate'] == 'modDate':
        systemEntryDate = 'Requested Date'
        indentIssuedDate = 'Based on Issued Date'
        reportDateHeader = ' | Based on Issued Date'
        indentDate = 'Indent Date'
        queries.append({
            'restaurantId': {'$in': selectedRestaurants},
            'modTs' : {'$gte': startDate, '$lte': endDate}
        })
    elif 'selectedBaseDate' in job['details'] and job['details']['selectedBaseDate'] == 'documentDate' :
        systemEntryDate = 'Requested Date'
        indentIssuedDate = 'Issued Date'
        indentDate = 'Based on Indent Date'
        reportDateHeader = ' | Based on Indent Date'
        queries.append({
            'restaurantId': {'$in': selectedRestaurants},
            'indentDocumentDate' : {'$gte': startDate, '$lte': endDate}
        })
    else:
        systemEntryDate = 'Based on Requested Date'
        reportDateHeader = ' | Based on Requested Date'
        indentIssuedDate = 'Issued Date'
        indentDate = 'Indent Date'
        queries.append({
            'restaurantId': {'$in': selectedRestaurants},
            'createTs' : {'$gte': startDate, '$lte': endDate}
        })

    issueList = []
    _category = [str(category).lower() for category in job['details']['selectedCategories'] if not str(category).isnumeric()]
    _subCategory = [str(subCategory).lower() for subCategory in job['details']['selectedSubCategories'] if not str(subCategory).isnumeric()]
    for query in queries :
        if len(job['details']['selectedWorkAreas']) > 0 :
            query['workArea'] = {'$in' : job['details']['selectedWorkAreas']}

        if delete:
            indents = list(deletedindentsCol.find(query).sort([('_id', 1)]))
        else:
            indents = list(indentlistsCol.find(query).sort([('_id', 1)]))

        for indent in indents:
            restaurantId = indent['restaurantId']
            getStockDict(restaurantId, stockDict)
            for item in indent['indentItems']:
                itemCode = item['itemCode']
                packageName = item.get("packageName", item['packages'][0]["packageName"] if "packages" in item else "N/A")
                category ='N/A'
                subCategory ='N/A'
                hsn ='N/A'
                taxRate = 0

                sv = next((x for x in stockDict[restaurantId] if x['itemCode'] == itemCode and x['packageName'] == packageName), None)
                if sv:
                    category = sv['category']
                    subCategory = sv['subCategory']
                    taxRate = sv['taxRate']
                    hsn =sv['hsnCode']
                if category.lower() not in _category and category != 'N/A' and ("all" not in _category):
                    continue
                if subCategory.lower() not in _subCategory and subCategory != 'N/A' and ("all" not in _subCategory):
                    continue

                item['entryType'] = item.get('entryType', 'N/A')
                item['issueQty'] = item.get('issueQty', 0) if item.get('issueQty') is not None else 0
                item['pendingQty'] = item.get('pendingQty', 0) if item.get('pendingQty') is not None else 0
                item['dispatchedQty'] = item['issueQty'] - item['pendingQty']
                if 'modTs' not in indent.keys(): indent['modTs'] = indent['createTs']

                if 'receivedQty' not in item.keys():
                    item['receivedQty'] = item.get('issueQty', 0)
                if item['receivedQty'] == 0:
                    continue

                entry ={}

                if 'type' in indent and indent['type'] == 'directIndent':
                    entry['Indent Type'] = "Direct issue"
                elif 'type' in indent and indent['type'] == 'centralStoreIndent':
                    entry['Indent Type'] = "Store issue(CSI)"
                else:
                    entry['Indent Type'] = "Store issue"

                entry['Location'] = location1[restaurantId]
                entry['WorkArea'] = indent['workArea']
                entry['Created By'] = '-' if 'creator' in indent and indent['creator'] == 'undefined' else indent.get('creator', '-')
                entry['Indent ID'] = indent['indentId']
                entry['Item Remark'] = item.get('remarks', '-')
                entry['Indent Remark'] = indent.get('remarks', '-')
                entry['Indent No'] = indent['indentNo']
                entry[systemEntryDate] = indent['createTs'].date()
                entry['Requested Time'] = format_time_to_ampm(indent['createTs'].strftime("%H:%M:%S"))
                entry['Category'] = category
                entry['Sub Category'] = subCategory
                entry['Item Code'] = itemCode
                entry['HSN/SAC'] = hsn
                entry['Item Name'] = item['itemName']
                entry['Package Name'] = packageName
                entry['Requested Qty'] = item['issueQty']
                entry['Issued Qty'] = item['dispatchedQty']
                entry['Pending Qty'] = item['pendingQty']
                entry['Entry Type']=  item['entryType']
                entry['Issued Time'] = format_time_to_ampm(indent['modTs'].strftime("%H:%M:%S"))
                entry[indentIssuedDate] = indent['modTs'].date()
                entry[indentDate] =  indent['indentDocumentDate'].date()
                if item['issueQty'] == item['dispatchedQty']:
                    entry['Indent Status'] = "Completed"
                elif entry['Requested Qty'] == entry['Pending Qty'] :
                    entry['Indent Status'] = "Pending"
                    entry[indentIssuedDate] = "N/A"
                    entry['Issued Time'] = "N/A"
                elif item['issueQty'] > item['dispatchedQty']:
                    entry['Indent Status'] = "Partial"
                if indent['status'] == 'closed':
                    entry['Indent Status'] = "Closed"

                price = 0
                if 'packages' in item.keys():
                    price = item['totalPrice'] / item['receivedQty']
                else:
                    price = item.get('price',0)

                entry['Tax (%)'] = taxRate
                entry['WAC(incl.tax,etc)'] = ut.truncate_and_floor(price, 2)
                entry['Total(incl.tax,etc)'] = ut.truncate_and_floor(item['dispatchedQty'] * price, 2)
                issueList.append(entry)
    df = pd.DataFrame(issueList)
    if len(df) > 0:
        df = df[[
            'Location',
            'WorkArea',
            'Created By',
            'Indent ID',
            systemEntryDate,
            'Requested Time',
            indentIssuedDate,
            'Issued Time',
            indentDate,
            'Category',
            'Sub Category',
            'Item Code',
            'HSN/SAC',
            'Item Name',
            'Entry Type',
            'Package Name',
            'Tax (%)',
            'Requested Qty',
            'Issued Qty',
            'Pending Qty',
            'WAC(incl.tax,etc)',
            'Total(incl.tax,etc)',
            'Indent Status',
            'Indent Type',
            'Item Remark',
            'Indent Remark'
        ]]
        df = pd.DataFrame(issueList)
    else:
        df = pd.DataFrame()
    return df

def consolidatedIndentreport(job):
    stockDict = {}
    selectedRestaurants = job['details']['selectedRestaurants']
    startDate = job['details']['startDate'].replace(hour=0, minute=0, second=0)
    endDate = job['details']['endDate'].replace(hour=23, minute=59, second=59)
    location1 = getLocation(job['tenantId'])
    query = {
        'restaurantId': {'$in': selectedRestaurants},
        'createTs': {'$gte': startDate, '$lte': endDate}
    }
    issueList = []
    _category = [str(category).lower() for category in job['details']['selectedCategories'] if not str(category).isnumeric()]
    _subCategory = [str(subCategory).lower() for subCategory in job['details']['selectedSubCategories'] if not str(subCategory).isnumeric()]

    indents = list(indentlistsCol.find(query).sort([('_id', 1)]))
    workAreas = indentlistsCol.distinct("workArea",query)

    required_data = []
    def get_package_name(item):
        if 'packages' in item and 'packageName' in item['packages']:
            return item['packages']['packageName']
        return item.get('packageName')

    restaurantId = job['details']['selectedRestaurants'][0]
    getStockDict(restaurantId, stockDict)
    for indent in indents:
        for item in indent['indentItems']:
            itemCode = item['itemCode']
            packageName = item.get("packageName", item['packages'][0]["packageName"] if "packages" in item else "N/A")
            category = 'N/A'
            subCategory = 'N/A'
            sv = next((x for x in stockDict[restaurantId] if x['itemCode'] == itemCode and x['packageName'] == packageName), None)

            if sv:
                category = sv['category']
                subCategory = sv['subCategory']
            if category.lower() not in _category and category != 'N/A' and ("all" not in _category):
                continue
            if subCategory.lower() not in _subCategory and subCategory != 'N/A' and ("all" not in _subCategory):
                continue

            package_name = get_package_name(item)
            existing_item = next(
                (x for x in required_data if x['itemCode'] == itemCode and x['packageName'] == package_name),
                None
            )
            if existing_item:
                existing_item['requestedQty'] += (
                    item.get('issueQty', 0) if indent['type'] == 'specialIndent' else item['quantity']
                )
                existing_item[indent['workArea']] += (
                    item.get('issueQty', 0) if indent['type'] == 'specialIndent' else item['quantity']
                )
            else:
                new_item = {
                    "itemName": item['itemName'],
                    "itemCode": itemCode,
                    "packageName": package_name,
                    "category": category,
                    "subCategory": subCategory,
                    "location": location1[restaurantId],
                    "requestedQty": item.get('issueQty', 0) if indent['type'] == 'specialIndent' else item['quantity'],
                }
                for wa in workAreas :
                    if indent['workArea'] == wa :
                        new_item[wa] = new_item['requestedQty']
                    else :
                        new_item[wa] = 0
                required_data.append(new_item)

    for item in required_data:
        entry = {}
        entry['Location'] = item['location']
        entry['Category'] = item['category']
        entry['Sub Category'] = item['subCategory']
        entry['Item Code'] = item['itemCode']
        entry['Item Name'] = item['itemName']
        entry['Package Name'] = item['packageName']
        entry['Total Requested Qty'] = item['requestedQty']
        for wa in workAreas :
            entry[wa] = item[wa]
        issueList.append(entry)

    df = pd.DataFrame(issueList)
    if len(df) > 0:
        columns = [
            'Location',
            'Category',
            'Sub Category',
            'Item Code',
            'Item Name',
            'Package Name',
            'Total Requested Qty',
        ]
        for wa in workAreas :
            columns.append(wa)

        df = df[columns]
        df.insert(0, 'S.No', range(1, len(df) + 1))
        empty_row = {col: None for col in df.columns}
        df = pd.concat([df, pd.DataFrame([empty_row])], ignore_index=True)
    else:
        df = pd.DataFrame()

    if not df.empty:
        requiredCol = job['details']['requiredColumns'] + workAreas
        if len(df) > 0:
            df = df[requiredCol]
    return df

def barVarianceReport(job, userReportsDirectory, reportType, requestDate, reportNo, internalCall = False, autobarList=[]):
    startTime = job['details']['startDate'].replace(hour=0, minute=0, second=0)
    endTime = job['details']['endDate'].replace(hour=23, minute=59, second=59)
    ###################### pre requists ##########################
    indent = getIndentDict(job, startTime, endTime)
    indentDict = indent[0]
    theoretical = getTheoreticalConsumption(job, startTime, endTime)
    theoreticalConsumptionDict = theoretical[0]
    openingUserDict = getUserDictWorkArea(job, 'opening')
    closingUserDict = getUserDictWorkArea(job, 'closing')
    ibtDict = getIntraBranchTransferDict(job, startTime, endTime)
    ibtInDict = ibtDict[0]
    ibtOutDict = ibtDict[1]
    overallList = []
    _category = [str(category).lower() for category in job['details']['selectedCategories'] if not str(category).isnumeric()]
    _subCategory = [str(subCategory).lower() for subCategory in job['details']['selectedSubCategories'] if not str(subCategory).isnumeric()]
    location1 = getLocation(job['tenantId'])
    for restaurantId in job['details']['selectedRestaurants']:
        queries=[
            {'restaurantId': restaurantId, 'entryType': 'open', 'ItemType':'Inventory' },
            {'restaurantId': restaurantId, 'uom': {'$in':['Nos','nos','NOS']}, 'ItemType':'Inventory' }
        ]
        for query in queries:
            orQueryForWorkAreas=[]
            for area in job['details']['selectedWorkAreas']:
                temp={}
                temp['workArea.{}'.format(area.strip())] = {'$exists': True }
                orQueryForWorkAreas.append(temp)
            if len(orQueryForWorkAreas) > 0:
                query['$or'] = orQueryForWorkAreas
            invItems = list(Stockvalues.find(query))
            for entry in invItems:
                if entry['itemCode'] in autobarList:
                    continue
                if entry['category'].lower() not in _category and ("all" not in _category):
                    continue
                if entry['subCategory'].lower() not in _subCategory and ("all" not in _subCategory):
                    continue
                itemCode = entry['itemCode'].strip()
                uom = entry['uom']
                searchKey = itemCode + '|' + uom.upper()
                if uom.lower() == 'kg':
                    weight = 1000
                    updatedUom = "GM"
                elif uom.lower() == 'litre' or uom.lower() == 'ltr':
                    weight = 1000
                    updatedUom = 'ML'
                elif uom.lower() == 'nos':
                    weight = 1
                    updatedUom = 'NOS'
                else:
                    continue

                ########## ibt in #############
                if restaurantId in ibtInDict:
                    if searchKey in ibtInDict[restaurantId]:
                        ibtInQty = ibtInDict[restaurantId][searchKey]
                    else:
                        ibtInQty = 0
                else:
                    ibtInQty = 0

                ########## ibt out ############
                if restaurantId in ibtOutDict:
                    if searchKey in ibtOutDict[restaurantId]:
                        ibtOutQty = ibtOutDict[restaurantId][searchKey]
                    else:
                        ibtOutQty = 0
                else:
                    ibtOutQty = 0

                ########## indent #############
                if restaurantId in indentDict:
                    if searchKey in indentDict[restaurantId]:
                        indentQty = indentDict[restaurantId][searchKey]
                    else:
                        indentQty = 0
                else:
                    indentQty = 0

                ########## Theoretical ConsumptionDict #############
                if restaurantId in theoreticalConsumptionDict:
                    if searchKey in theoreticalConsumptionDict[restaurantId]:
                        theoreticalConsumptionQty = theoreticalConsumptionDict[restaurantId][searchKey]
                    else:
                        theoreticalConsumptionQty = 0
                else:
                    theoreticalConsumptionQty = 0

                ########## Opening User  #############
                if restaurantId in openingUserDict:
                    if searchKey in openingUserDict[restaurantId]:
                        openingUserQty = openingUserDict[restaurantId][searchKey]
                    else:
                        openingUserQty = 0
                else:
                    openingUserQty = 0

                ########## Closing User #############
                if restaurantId in closingUserDict:
                    if searchKey in closingUserDict[restaurantId]:
                        closingUserQty = closingUserDict[restaurantId][searchKey]
                    else:
                        closingUserQty = 0
                else:
                    closingUserQty = 0
                tempDict = {}
                tempDict['Location'] = location1[entry['restaurantId']]
                tempDict['Item Code'] = entry['itemCode']
                tempDict['Item Name'] = entry['itemName']
                tempDict['Category'] = entry['category']
                tempDict['Sub Category'] = entry['subCategory']
                tempDict['UOM'] = updatedUom
                tempDict['Opening Stock'] = ut.truncate_and_floor(openingUserQty, 3)
                tempDict['Indent Qty'] = ut.truncate_and_floor((indentQty * weight), 3)
                tempDict['Intra Branch Transfer'] = ut.truncate_and_floor(((ibtInQty *weight) -(ibtOutQty*weight)), 3)
                tempDict['Total Stock'] = ut.truncate_and_floor((tempDict['Opening Stock'] + tempDict['Indent Qty'] +tempDict['Intra Branch Transfer']  ), 3)
                tempDict['Closing Stock'] = ut.truncate_and_floor(closingUserQty, 3)
                tempDict['Consumed Qty'] = ut.truncate_and_floor((tempDict['Total Stock'] - tempDict['Closing Stock']), 3)
                tempDict['Sold Qty'] = ut.truncate_and_floor((theoreticalConsumptionQty * weight), 3)
                tempDict['Variance Qty'] = ut.truncate_and_floor((tempDict['Sold Qty'] - tempDict['Consumed Qty'] ), 3)
                overallList.append(tempDict)
    df = pd.DataFrame(overallList)
    df.reset_index(drop=True)
    if len(df) > 0:
        df = df[[
            'Location',
            'Item Code',
            'Item Name',
            'Category',
            'Sub Category',
            'UOM',
            'Opening Stock',
            'Indent Qty',
            'Intra Branch Transfer',
            'Total Stock',
            'Closing Stock',
            'Consumed Qty',
            'Sold Qty',
            'Variance Qty'
        ]]
    
    return df


##################### Dummmy ##########################

def profitMarginReport(job, userReportsDirectory, reportType, requestDate, reportNo):
    getPosMenuSellingPrice = posSellingPrice(job['tenantId'],job['details']['priceTier']['id'])
    getPosModifiers = []
    dateAndTime = getCreatedTime(job)
    menuItems = prepareMenuItems(job['tenantId'],job['details']['selectedRestaurants'][0],getPosMenuSellingPrice,getPosModifiers)
    df2 = pd.DataFrame(menuItems)
    output_file = f"{userReportsDirectory}/{reportNo}_{reportType}_{dateAndTime}.xlsx"
    with pd.ExcelWriter(output_file, engine='xlsxwriter') as writer:
        workbook = writer.book
        branch = list(branchesCol.find({'restaurantIdOld': job['details']['selectedRestaurants'][0]}))
        tenantName = branch[0]['tenantName'] if branch else ""
        df2.to_excel(writer, sheet_name="Menu-Cost %", startrow=3, index=False)
        sheet = writer.sheets["Menu-Cost %"]
        sheet.set_zoom(90)
        adjustWidth(df2, writer, "Menu-Cost %")
        createHeader(workbook, sheet, job, tenantName, "Menu-Cost %")
        sheet.freeze_panes(4, 0)

def prepareMenuItems(tenantId,restaurantId,selling_price,modifiers) :
    getOverAllMenuItems =  list(servingsizerecipesCol.find({'tenantId': tenantId,'isSubRecipe' : False}))
    invItems1 = list(Stockvalues.find(
        {
            "tenantId": tenantId,
            "uom": "NOS",
            "ItemType": "Inventory",
            "restaurantId": restaurantId
        },
        {
            "_id": 0,
            "itemName": 1,
            "itemCode": 1,
            "category": 1,
            "subCategory": 1,
            "uom": 1,
            "price": 1,
            "packageQty": 1,
            "status": 1,
            "withTaxPrice": 1,
            "ItemType": 1
        }
    ))
    # Fetch invItems2
    invItems2 = list(Stockvalues.find(
        {
            "tenantId": tenantId,
            "entryType": "open",
            "ItemType": "Inventory",
            "restaurantId": restaurantId
        },
        {
            "_id": 0,
            "itemName": 1,
            "itemCode": 1,
            "category": 1,
            "subCategory": 1,
            "uom": 1,
            "price": 1,
            "packageQty": 1,
            "status": 1,
            "withTaxPrice": 1,
            "ItemType": 1
        }
    ))
    menuItems = list(Stockvalues.find(
        {
            "tenantId": tenantId,
            "ItemType": "Menu",
            "restaurantId": restaurantId
        },
        {
            "_id": 0,
            "itemName": 1,
            "itemCode": 1,
            "category": 1,
            "subCategory": 1,
            "uom": 1,
            "price": 1,
            "status": 1,
            "withTaxPrice": 1,
            "ItemType": 1
        }
    ))
    # Fetch invItems3
    invItems3 = list(Stockvalues.aggregate([
        {
            "$match": {
                "tenantId": tenantId,
                "ItemType": "SubRecipe",
                "restaurantId": restaurantId
            }
        },
        {
            "$addFields": {
                "packageQty": 1,
                "price": {"$ifNull": ["$price", 1]},
                "portionWeight": {"$ifNull": ["$portionWeight", 0]},
                "portion": {"$ifNull": ["$portion", 0]}
            }
        },
        {
            "$project": {
                "_id": 0,
                "itemName": 1,
                "itemCode": 1,
                "category": 1,
                "ItemType": 1,
                "subCategory": 1,
                "uom": 1,
                "price": 1,
                "portionWeight": 1,
                "withTaxPrice": 1,
                "portion": 1,
                "status": 1,
                "packageQty": 1,
                "ItemType": 1
            }
        }
    ]))

    # Combine arrays and sort by itemName
    combined_array = invItems1 + invItems2 + invItems3 + menuItems
    # Remove duplicates based on all item properties
    unique_combined_array = [dict(t) for t in {tuple(item.items()) for item in combined_array}]
    menu_items = []
    for el in getOverAllMenuItems:
        modifier_list = [
            item for item in el["Ingredients"]
            if "isModifier" in item and item["isModifier"] in ["Y", "y", "YES", "yes", "Yes"]
        ]
        if modifier_list:
            required_items = [
                item for item in el["Ingredients"]
                if not any(
                    selected["IngredientCode"] == item["IngredientCode"]
                    and selected["IngredientName"] == item["IngredientName"]
                    for selected in modifier_list
                )
            ]
            for mod in modifier_list:
                menu_item = copy.deepcopy(el)
                menu_item["modifierName"] = f"{mod['modifierName']} | {mod['IngredientName']}"
                menu_item["Ingredients"] = required_items + [mod]
                menu_items.append(menu_item)
            menu_item = copy.deepcopy(el)
            menu_item["Ingredients"] = [
                item for item in menu_item["Ingredients"]
                if item.get("isModifier", "").strip() not in ["Y", "y", "YES", "yes", "Yes"]
            ]
            menu_item["modifierName"] = "N/A"
            menu_items.append(menu_item)
        else:
            menu_item = copy.deepcopy(el)
            menu_item["modifierName"] = "N/A"
            menu_items.append(menu_item)
    required_data = []
    for ing in menu_items:
        obj = {}
        for el in ing['Ingredients']:
            required_item = next((item for item in unique_combined_array if item['itemCode'] == el['IngredientCode']), None)
            conversion_coefficient = 1 if required_item and required_item.get('uom') == 'NOS' else 1000
            # rate = (
            #     required_item['withTaxPrice'] /
            #     (required_item['packageQty'] * conversion_coefficient)
            #     if required_item and 'packageQty' in required_item else 0
            # )
            rate = 0
            if required_item and 'packageQty' in required_item :
                rate = (required_item['packageQty'] / conversion_coefficient ) * required_item['withTaxPrice']
            el['rate'] = rate
            el['finalRate'] = el['initialWeight'] * rate
        ing['costOfProd'] = sum(
            ingredient['finalRate'] for ingredient in ing['Ingredients']
            if ingredient.get("isModifier", "").lower() not in ["y", "yes"]
        )
        ing['costOfProdOfModifier'] = sum(
            ingredient['finalRate'] for ingredient in ing['Ingredients']
            if ingredient.get("isModifier", "").lower() in ["y", "yes"]
        )
        selling_cost = next((sp for sp in selling_price if sp['menuItemName'] == f"{ing['menuItemCode']}|{ing['servingSize']}"), None)
        req = f"{ing['modifierName'].split('|', 1)[0].strip()}|{ing['servingSize']}"  # Directly extract and format key
        modifier_cost = selling_cost.get(req, 0) if selling_cost else 0
        ing['sellingCost'] = selling_cost['price'] if selling_cost else 0
        ing['menuCostPercentage'] = 'N/A' if ing['sellingCost'] == 0 else  ut.truncate_and_floor((((ing['costOfProd'] + ing['costOfProdOfModifier']) / (ing['sellingCost'] + modifier_cost)) * 100),3)
        required_item = next((item for item in unique_combined_array if item['itemCode'] == ing['menuItemCode']), None)
        obj['Location'] = restaurantId.split('@')[1]
        obj['Category'] = required_item['category'] if required_item and 'category' in required_item else 'N/A'
        obj['SubCategory'] = required_item['subCategory'] if required_item and 'subCategory' in required_item else 'N/A'
        obj['Menu-Item Code'] = ing['menuItemCode']
        obj['Menu-Item Name'] = ing['menuItemName']
        obj['Serving Size'] = ing['servingSize']
        obj['Modifier Name'] = ing['modifierName']
        obj['Recipe Cost'] =  ut.truncate_and_floor(ing['costOfProd'],3)
        obj['Modifier Cost'] =  ut.truncate_and_floor(ing['costOfProdOfModifier'],3)
        obj['Total Cost including Modifier'] =  obj['Recipe Cost'] + obj['Modifier Cost']
        obj['Selling Price'] = ing['sellingCost']
        obj['Selling Modifier Price'] = modifier_cost
        obj['Total Selling Price Including Modifier'] = obj['Selling Price'] + obj['Selling Modifier Price']
        obj['Menu-Cost %'] = ing['menuCostPercentage']
        required_data.append(obj)
    return required_data


def store_variance_update(job, userReportsDirectory, reportType, requestDate, reportNo):
    selectedRestaurants = job['details']['selectedRestaurants']
    invSearchFilter = {
        'status': {"$ne" :"discontinued"},
        'ItemType' : {'$in': ['Inventory','SubRecipe']},
        'restaurantId' : {'$in': selectedRestaurants}
    }
    openingSystemDict = {}
    location1 = getLocation(job['tenantId'])
    ########################## prerequists #############################
    startTime = job['details']['startDate'].replace(hour=0, minute=0, second=0)
    endTime = job['details']['endDate'].replace(hour=23, minute=59, second=59)
    grnDict = getGrnDict(job, startTime, endTime, qtyWise=True)
    indentDict, _sr = getIndentDict(job, startTime, endTime, qtyWise=True)
    ibtInDict, ibtOutDict = getIbtDict(job, startTime, endTime, qtyWise=True)
    spoilageDict = getSpoilageDictStore(job, startTime, endTime, qtyWise=True)
    openDictStoreOpen, userStoreDictOpen = getUserDictStore(job, 'opening', 2)
    counter = 0
    for item in Stockvalues.find(invSearchFilter):
        counter +=1
        itemCode = item['itemCode']
        location = location1[item['restaurantId']]
        restaurantId = item['restaurantId']
        entryType = item.get('entryType', None)
        pkgName = item.get('packageName', None)
        item['entryType'] = item.get('entryType') if item.get('entryType', None) is not None else 'N/A'
        item['packageName'] = item.get('packageName') if item.get('packageName', None) is not None else 'N/A'
        item['uom'] = item.get('uom', 'N/A')
        taxRate = item.get('taxRate', 0)
        if item['ItemType'] == "SubRecipe":
            searchKey1 = itemCode + '|' + 'N/A' + '|' + 'N/A'
            measure = "open"
        else:
            measure = item['entryType']
            searchKey1 = itemCode + '|' + item['uom'].upper() + '|' + item['packageName'].upper()

        ###################### user opening ###################
        systemOpening = {'inStock' : 0}
        matchFound = False
        if item['entryType'] == "open":
            if item['restaurantId'] in openDictStoreOpen:
                if itemCode in openDictStoreOpen[item['restaurantId']]['store'].keys():
                    matchFound = True
                    systemOpening = {"inStock" : openDictStoreOpen[item['restaurantId']]['store'][itemCode]}
        else:
            if item['restaurantId'] in userStoreDictOpen:
                if searchKey1 in userStoreDictOpen[item['restaurantId']]['store'].keys():
                    matchFound = True
                    systemOpening = {"inStock" : userStoreDictOpen[item['restaurantId']]['store'][searchKey1][measure]}

        searchKey = itemCode + '|' + item['entryType'].upper() +  '|' + item['packageName'].upper()
        if not matchFound :
            ######################## system opening ########################
            if location not in openingSystemDict:
                system = dailybackupsCol.find_one({
                    'restaurantId': item['restaurantId'],
                    'createTs': {
                        '$gte': job['details']['startDate'].replace(hour=0, minute=0, second=0) - datetime.timedelta(days=1),
                        '$lte': job['details']['startDate'].replace(hour=23, minute=59, second=59) - datetime.timedelta(days=1)
                    }
                })
                openingSystemDict[location] = system['items'] if system else []
            if 'packageName' not in item.keys():
                item['packageName'] = None
            if 'entryType' not in item.keys():
                item['entryType'] = None
            systemOpening = next((i for i in openingSystemDict[location] if i.get('itemCode') == item['itemCode'] and i.get('packageName') == pkgName and i.get('entryType') == entryType), {})

        ############################### GRN ##############################
        if restaurantId in grnDict:
            if searchKey in grnDict[restaurantId]:
                grnQty = grnDict[restaurantId][searchKey]['received']
            else:
                grnQty = 0
        else:
            grnQty = 0

        ############################# ibt in #############################
        if restaurantId in ibtInDict:
            if searchKey in ibtInDict[restaurantId]:
                ibtInQty = ibtInDict[restaurantId][searchKey]
            else:
                ibtInQty = 0
        else:
            ibtInQty = 0

        ############################# ibt out #############################
        if restaurantId in ibtOutDict:
            if searchKey in ibtOutDict[restaurantId]:
                ibtOutQty = ibtOutDict[restaurantId][searchKey]
            else:
                ibtOutQty = 0
        else:
            ibtOutQty = 0

        ############################# indent #############################
        if restaurantId in indentDict:
            if searchKey in indentDict[restaurantId]:
                indentQty = indentDict[restaurantId][searchKey]['issued']
            else:
                indentQty = 0
        else:
            indentQty = 0

        ############################# spoilage #############################
        if restaurantId in spoilageDict:
            if searchKey in spoilageDict[restaurantId]:
                spoilageQty = spoilageDict[restaurantId][searchKey]
            else:
                spoilageQty = 0
        else:
            spoilageQty = 0

        entry = {
            'Location': location,
            'Category': item['category'],
            'Sub Category': item['subCategory'],
            'Item Code': itemCode,
            'Item Name': item['itemName'],
            'Entry Type': item['entryType'],
            'Package Name': item['packageName'],
            'UOM': item['uom'],
            "Tax(%)" : taxRate,
            'Opening Qty':  ut.truncate_and_floor(systemOpening.get('inStock', 0), 2),
            'Purchase Qty':  ut.truncate_and_floor(grnQty, 2),
            'Indent Qty':  ut.truncate_and_floor(indentQty, 2),
            'Ibt In Qty':  ut.truncate_and_floor(ibtInQty, 2),
            'Ibt Out Qty':  ut.truncate_and_floor(ibtOutQty, 2),
            'Spoilage Qty':  ut.truncate_and_floor(spoilageQty, 2)
        }

        inStock = (entry['Opening Qty'] + entry['Purchase Qty'] + entry['Ibt In Qty'] + entry['Spoilage Qty']) -(entry['Indent Qty'] + entry['Ibt Out Qty'])
        if inStock < 0:
            print(entry)
            inStock = 0
        Stockvalues.update_one({"_id" : item['_id']},{"$set" : {"inStock" : ut.truncate_and_floor(inStock, 2)}})