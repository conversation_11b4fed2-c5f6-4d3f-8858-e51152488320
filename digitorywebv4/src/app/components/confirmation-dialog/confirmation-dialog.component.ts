import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

export interface ConfirmationDialogData {
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  type?: 'warning' | 'danger' | 'info';
}

@Component({
  selector: 'app-confirmation-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule
  ],
  template: `
    <div class="confirmation-dialog">
      <div class="dialog-header" [ngClass]="getHeaderClass()">
        <mat-icon class="dialog-icon">{{ getIcon() }}</mat-icon>
        <h2 mat-dialog-title>{{ data.title }}</h2>
      </div>
      
      <mat-dialog-content class="dialog-content">
        <p [innerHTML]="data.message"></p>
      </mat-dialog-content>
      
      <mat-dialog-actions class="dialog-actions">
        <button mat-stroked-button 
                (click)="onCancel()"
                class="cancel-btn">
          {{ data.cancelText || 'Cancel' }}
        </button>
        <button mat-raised-button 
                [color]="getButtonColor()"
                (click)="onConfirm()"
                class="confirm-btn">
          {{ data.confirmText || 'Confirm' }}
        </button>
      </mat-dialog-actions>
    </div>
  `,
  styles: [`
    .confirmation-dialog {
      min-width: 400px;
      max-width: 500px;
    }
    
    .dialog-header {
      display: flex;
      align-items: center;
      padding: 20px 24px 16px;
      border-radius: 4px 4px 0 0;
    }
    
    .dialog-header.warning {
      background-color: #fff3cd;
      color: #856404;
    }
    
    .dialog-header.danger {
      background-color: #f8d7da;
      color: #721c24;
    }
    
    .dialog-header.info {
      background-color: #d1ecf1;
      color: #0c5460;
    }
    
    .dialog-icon {
      margin-right: 12px;
      font-size: 24px;
      width: 24px;
      height: 24px;
    }
    
    .dialog-content {
      padding: 16px 24px;
      color: #333;
      line-height: 1.5;
    }
    
    .dialog-content p {
      margin: 0;
      white-space: pre-line;
    }
    
    .dialog-actions {
      padding: 16px 24px 20px;
      justify-content: flex-end;
      gap: 12px;
    }
    
    .cancel-btn {
      min-width: 80px;
    }
    
    .confirm-btn {
      min-width: 80px;
    }
  `]
})
export class ConfirmationDialogComponent {
  constructor(
    public dialogRef: MatDialogRef<ConfirmationDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ConfirmationDialogData
  ) {}

  onCancel(): void {
    this.dialogRef.close(false);
  }

  onConfirm(): void {
    this.dialogRef.close(true);
  }

  getHeaderClass(): string {
    return this.data.type || 'warning';
  }

  getIcon(): string {
    switch (this.data.type) {
      case 'danger':
        return 'warning';
      case 'info':
        return 'info';
      case 'warning':
      default:
        return 'warning';
    }
  }

  getButtonColor(): string {
    switch (this.data.type) {
      case 'danger':
        return 'warn';
      case 'info':
        return 'primary';
      case 'warning':
      default:
        return 'warn';
    }
  }
}
