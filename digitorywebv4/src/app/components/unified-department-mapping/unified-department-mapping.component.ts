import { Component, OnInit, OnDestroy, Input, Output, EventEmitter, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormControl, FormBuilder, FormGroup, FormArray, ReactiveFormsModule, Validators } from '@angular/forms';
import { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';

// Material Design Modules
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatOptionModule } from '@angular/material/core';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';

// Third-party modules
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';

import { DepartmentService, DepartmentGroup, DepartmentGroupCategoryMapping, DepartmentGroupWorkareaMapping } from '../../services/department.service';
import { SmartDashboardService } from '../../services/smart-dashboard.service';
import { AuthService } from '../../services/auth.service';
import { ConfirmationDialogService } from '../../services/confirmation-dialog.service';

export interface Department {
  id: string;
  name: string;
  code?: string;
}



export interface CategoryWorkareaMapping {
  categoryName: string;
  workAreas: string[];
}

export interface WorkAreaData {
  restaurantIdOld: string;
  branchName: string;
  workAreas: string[];
  disabled: boolean;
}



@Component({
  selector: 'app-unified-department-mapping',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatOptionModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatDividerModule,
    MatSnackBarModule,
    MatButtonModule,
    MatTooltipModule,
    NgxMatSelectSearchModule
  ],
  templateUrl: './unified-department-mapping.component.html',
  styleUrls: ['./unified-department-mapping.component.scss']
})
export class UnifiedDepartmentMappingComponent implements OnInit, OnDestroy {
  private readonly destroy$ = new Subject<void>();

  // Inputs
  @Input() tenantId: string = '';
  @Input() autoEmit: boolean = true; // Whether to auto-emit changes
  @Input() showAsDialog: boolean = false; // Whether component is shown in a dialog

  // Outputs
  @Output() groupMappingsChanged = new EventEmitter<DepartmentGroupCategoryMapping[]>();
  @Output() categoryWorkareaMappingsChanged = new EventEmitter<CategoryWorkareaMapping[]>();
  @Output() dialogClosed = new EventEmitter<void>();

  // Data
  departments: Department[] = []; // Needed for create group dialog
  departmentGroups: DepartmentGroup[] = [];
  categories: string[] = [];
  workAreas: WorkAreaData[] = [];
  filteredDepartmentGroups: DepartmentGroup[] = [];

  // Form controls
  departmentGroupFilterCtrl = new FormControl('');
  selectedDepartmentGroupsCtrl = new FormControl<string[]>([]);
  groupMappingForm: FormGroup;

  // State
  isLoading = true;
  isSaving = false;
  isFormStable = false; // Flag to prevent unnecessary form rebuilds
  formsInitialized = false; // Flag to track if forms are ready
  currentGroupMappings: DepartmentGroupCategoryMapping[] = [];
  currentGroupWorkareaMappings: DepartmentGroupWorkareaMapping[] = [];
  categoryWorkareaMappings: CategoryWorkareaMapping[] = []; // @deprecated - for backward compatibility
  allWorkAreas: string[] = [];

  // Create Group Dialog State
  showCreateGroupDialog = false;
  isCreatingGroup = false;
  createGroupForm: FormGroup;

  // Edit group state
  isEditingGroup = false;
  editingGroupId: string | null = null;

  constructor(
    private fb: FormBuilder,
    private departmentService: DepartmentService,
    private smartDashboardService: SmartDashboardService,
    private authService: AuthService,
    private snackBar: MatSnackBar,
    private cdr: ChangeDetectorRef,
    private confirmationDialogService: ConfirmationDialogService
  ) {
    // Initialize forms first to prevent null reference errors
    this.initializeGroupMappingForm();
    this.initializeCreateGroupForm();
    this.formsInitialized = true;
  }

  ngOnInit(): void {
    if (!this.tenantId) {
      const user = this.authService.getCurrentUser();
      this.tenantId = user?.tenantId || '';
    }

    this.setupFormSubscriptions();
    this.loadData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeGroupMappingForm(): void {
    try {
      this.groupMappingForm = this.fb.group({
        mappings: this.fb.array([])
      });
    } catch (error) {
      console.error('Error initializing group mapping form:', error);
      // Fallback initialization
      this.groupMappingForm = this.fb.group({
        mappings: this.fb.array([])
      });
    }
  }

  private initializeCreateGroupForm(): void {
    try {
      this.createGroupForm = this.fb.group({
        groupName: ['', [Validators.required, Validators.minLength(2)]],
        departmentIds: [[], []]  // Remove required validator initially
      });
    } catch (error) {
      console.error('Error initializing create group form:', error);
      // Fallback initialization
      this.createGroupForm = this.fb.group({
        groupName: ['', [Validators.required, Validators.minLength(2)]],
        departmentIds: [[], []]
      });
    }
  }

  private setupFormSubscriptions(): void {
    // Department group filter subscription
    this.departmentGroupFilterCtrl.valueChanges
      .pipe(
        debounceTime(300),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe(filterValue => {
        this.filterDepartmentGroups(filterValue || '');
      });

    // Selected department groups subscription with debounce to prevent multiple calls
    this.selectedDepartmentGroupsCtrl.valueChanges
      .pipe(
        debounceTime(100), // Small debounce to prevent rapid fire
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe(selectedIds => {
        // Always update the form when groups are selected
        this.updateGroupMappingForm(selectedIds || []);

        // Only emit if auto-emit is enabled and we're not in initial loading
        if (this.autoEmit && !this.isLoading) {
          this.emitCurrentGroupMappings();
        }
      });

    // Group mapping form subscription to detect category changes
    this.groupMappingForm.valueChanges
      .pipe(
        debounceTime(150), // Slightly longer debounce to ensure form is updated
        takeUntil(this.destroy$)
      )
      .subscribe(() => {
        console.log('DEBUG: Group mapping form value changed:', this.groupMappingForm.value);

        // Trigger change detection when categories are selected/deselected
        this.cdr.detectChanges();



        // Emit changes if auto-emit is enabled
        if (this.autoEmit && !this.isLoading) {
          this.emitCurrentGroupMappings();
        }
      });
  }

  private loadData(): void {
    this.isLoading = true;

    // Load departments, groups, categories, workareas and mappings in parallel
    Promise.all([
      this.loadDepartments(),
      this.loadDepartmentGroups(),
      this.loadCategories(),
      this.loadWorkAreas(),
      this.loadExistingGroupMappings(),
      this.loadGroupWorkareaMappings(),
      this.loadCategoryWorkareaMappings() // Keep for backward compatibility
    ]).then(() => {
      this.extractAllWorkAreas();

      console.log('DEBUG: All data loaded successfully');
      console.log('DEBUG: Department groups:', this.departmentGroups.length);
      console.log('DEBUG: Current group mappings:', this.currentGroupMappings.length);
      console.log('DEBUG: Category workarea mappings:', this.categoryWorkareaMappings.length);
      console.log('DEBUG: Selected department groups control value:', this.selectedDepartmentGroupsCtrl.value);

      // Update the form with both group mappings and category-workarea mappings
      if (this.currentGroupMappings.length > 0) {
        const selectedGroupIds = this.selectedDepartmentGroupsCtrl.value || [];
        console.log('DEBUG: Updating form with selected group IDs:', selectedGroupIds);
        console.log('DEBUG: Current group mappings:', this.currentGroupMappings);
        console.log('DEBUG: Category workarea mappings:', this.categoryWorkareaMappings);

        // Force a complete form rebuild to ensure all groups are captured
        const mappingsArray = this.groupMappingForm.get('mappings') as FormArray;
        while (mappingsArray.length !== 0) {
          mappingsArray.removeAt(0);
        }

        this.updateGroupMappingForm(selectedGroupIds);

        // Verify the form was built correctly
        console.log('DEBUG: Form array length after update:', mappingsArray.length);
        console.log('DEBUG: Form controls after update:', mappingsArray.controls.map(c => ({
          groupId: c.get('groupId')?.value,
          groupName: c.get('groupName')?.value,
          categories: c.get('categories')?.value,
          workAreas: c.get('workAreas')?.value
        })));
      }

      this.isLoading = false;
      this.isFormStable = true; // Mark form as stable after initial setup
      this.cdr.detectChanges();

      // Emit initial group mappings if they exist
      if (this.autoEmit && this.currentGroupMappings.length > 0) {
        console.log('DEBUG: Emitting initial group mappings:', this.currentGroupMappings);
        this.groupMappingsChanged.emit(this.currentGroupMappings);
      }
    }).catch((error) => {
      console.error('DEBUG: Error loading data:', error);
      this.isLoading = false;
      this.cdr.detectChanges();
    });
  }



  private loadDepartmentGroups(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.departmentService.getDepartmentGroups(this.tenantId)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (groups) => {
            this.departmentGroups = groups;
            this.filteredDepartmentGroups = [...groups];
            resolve();
          },
          error: (error) => {
            this.departmentGroups = [];
            this.filteredDepartmentGroups = [];
            reject(error);
          }
        });
    });
  }

  private loadDepartments(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.departmentService.getDepartments(this.tenantId)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (departments) => {
            this.departments = departments;
            this.updateCreateGroupFormValidation();
            resolve();
          },
          error: (error) => {
            this.departments = [];
            this.updateCreateGroupFormValidation();
            reject(error);
          }
        });
    });
  }

  private updateCreateGroupFormValidation(): void {
    const departmentIdsControl = this.createGroupForm.get('departmentIds');
    if (departmentIdsControl) {
      if (this.departments.length > 0) {
        departmentIdsControl.setValidators([Validators.required]);
      } else {
        departmentIdsControl.clearValidators();
      }
      departmentIdsControl.updateValueAndValidity();
    }
  }

  private loadCategories(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.smartDashboardService.getCategories(this.tenantId)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response) => {
            if (response && response.categories) {
              // Handle if categories are strings directly or objects
              this.categories = response.categories.map((cat: any) => {
                if (typeof cat === 'string') {
                  return cat;
                } else if (typeof cat === 'object') {
                  return cat.name || cat.categoryName || cat.category || cat.Category || cat.CATEGORY || Object.keys(cat)[0] || 'Unknown';
                }
                return String(cat);
              });
            } else {
              this.categories = [];
            }
            resolve();
          },
          error: (error) => {
            this.categories = [];
            reject(error);
          }
        });
    });
  }



  private loadExistingGroupMappings(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.departmentService.getDepartmentGroupCategoryMappings(this.tenantId)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (mappings) => {
            this.currentGroupMappings = mappings;
            this.restoreGroupSelectionFromMappings(mappings);
            resolve();
          },
          error: (error) => {
            this.currentGroupMappings = [];
            reject(error);
          }
        });
    });
  }

  private loadGroupWorkareaMappings(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.departmentService.getDepartmentGroupWorkareaMappings(this.tenantId)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (mappings) => {
            this.currentGroupWorkareaMappings = mappings;
            console.log('DEBUG: Loaded group-workarea mappings:', mappings);
            resolve();
          },
          error: (error) => {
            console.error('DEBUG: Error loading group-workarea mappings:', error);
            this.currentGroupWorkareaMappings = [];
            resolve(); // Don't fail the entire load process
          }
        });
    });
  }

  private loadWorkAreas(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // Get work areas from user's restaurant access data
        const user = this.authService.getCurrentUser();
        console.log('DEBUG: Current user for work areas:', user);
        const workAreaData: WorkAreaData[] = [];

        if (user && user.restaurantAccess) {
          console.log('DEBUG: User restaurant access:', user.restaurantAccess);
          console.log('DEBUG: Restaurant access length:', user.restaurantAccess.length);
          user.restaurantAccess.forEach((restaurant: any, index: number) => {
            console.log(`DEBUG: Processing restaurant ${index}:`, restaurant);
            console.log(`DEBUG: Restaurant ${index} workAreas:`, restaurant.workAreas);
            if (restaurant.workAreas && restaurant.workAreas.length > 0) {
              const workAreaEntry = {
                restaurantIdOld: restaurant.restaurantIdOld,
                branchName: restaurant.branchName,
                workAreas: restaurant.workAreas,
                disabled: false
              };
              workAreaData.push(workAreaEntry);
              console.log(`DEBUG: Added work area entry:`, workAreaEntry);
            } else {
              console.log(`DEBUG: Restaurant ${index} has no work areas or empty work areas`);
            }
          });
        } else {
          console.log('DEBUG: No user or no restaurant access');
        }

        this.workAreas = workAreaData;
        console.log('DEBUG: Final work areas data:', this.workAreas);
        console.log('DEBUG: Final work areas count:', this.workAreas.length);
        resolve();
      } catch (error) {
        console.log('DEBUG: Error loading work areas:', error);
        this.workAreas = [];
        reject(error);
      }
    });
  }

  private loadCategoryWorkareaMappings(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.departmentService.getCategoryWorkareaMappings(this.tenantId)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (mappings) => {
            // Process mappings to ensure work areas are in correct format
            this.categoryWorkareaMappings = mappings.map(mapping => {
              let processedWorkAreas = mapping.workAreas || [];

              // Handle case where workAreas might be stored as string instead of array
              if (typeof processedWorkAreas === 'string') {
                processedWorkAreas = [processedWorkAreas];
              }

              return {
                ...mapping,
                workAreas: processedWorkAreas
              };
            });

            resolve();
          },
          error: (error) => {
            this.categoryWorkareaMappings = [];
            reject(error);
          }
        });
    });
  }

  private restoreGroupSelectionFromMappings(mappings: DepartmentGroupCategoryMapping[]): void {
    if (mappings.length > 0) {
      const selectedGroupIds = mappings.map(m => String(m.groupId));

      // Set the value without triggering subscriptions during initial load
      this.selectedDepartmentGroupsCtrl.setValue(selectedGroupIds, { emitEvent: false });

      // Manually update the group mapping form with the restored selections
      this.updateGroupMappingForm(selectedGroupIds);
    }
  }

  private filterDepartmentGroups(filterValue: string): void {
    if (!filterValue) {
      this.filteredDepartmentGroups = [...this.departmentGroups];
    } else {
      const filter = filterValue.toLowerCase();
      this.filteredDepartmentGroups = this.departmentGroups.filter(group =>
        group.name.toLowerCase().includes(filter) ||
        (group.description && group.description.toLowerCase().includes(filter))
      );
    }
  }



  private updateGroupMappingForm(selectedGroupIds: string[]): void {
    console.log('DEBUG: updateGroupMappingForm called with:', selectedGroupIds);
    const mappingsArray = this.groupMappingForm.get('mappings') as FormArray;

    // Ensure all group IDs are strings
    const stringGroupIds = selectedGroupIds.map(id => String(id));

    // Check if the form already has the correct groups to avoid unnecessary rebuilds
    const currentGroupIds = mappingsArray.controls.map(control => control.get('groupId')?.value).filter(id => id);
    const currentSet = new Set(currentGroupIds.map(id => String(id)));
    const newSet = new Set(stringGroupIds);

    const isSameSelection = currentSet.size === newSet.size &&
                           [...currentSet].every(id => newSet.has(id));

    // Only skip rebuild if we have the exact same selection and form is not empty
    // BUT always rebuild during initial load to ensure all groups are captured
    if (isSameSelection && mappingsArray.length > 0 && this.isFormStable) {
      console.log('DEBUG: Skipping group form rebuild - same selection and form is stable');
      return;
    }

    console.log('DEBUG: Rebuilding group form array for groups:', stringGroupIds);

    // Store current form values to preserve user selections
    const currentFormValues = new Map<string, { categories: string[], workAreas: string[] }>();
    for (let i = 0; i < mappingsArray.length; i++) {
      const control = mappingsArray.at(i);
      const groupId = control.get('groupId')?.value;
      const categories = control.get('categories')?.value || [];
      const workAreas = control.get('workAreas')?.value || [];
      if (groupId) {
        currentFormValues.set(String(groupId), { categories, workAreas });
      }
    }

    // Clear existing form array
    while (mappingsArray.length !== 0) {
      mappingsArray.removeAt(0);
    }

    // Add form groups for selected department groups
    stringGroupIds.forEach(groupId => {
      const group = this.departmentGroups.find(g => String(g.id) === String(groupId));

      if (group) {
        // Try to get values from current form values first, then from existing mappings
        const currentFormData = currentFormValues.get(String(groupId));
        const existingMapping = this.currentGroupMappings.find(m => String(m.groupId) === String(groupId));

        const categories = currentFormData?.categories || (existingMapping ? existingMapping.categories : []);

        // Get work areas from current form values or from category-workarea mappings
        let workAreas = currentFormData?.workAreas || [];

        // If no work areas from form, try to get them from group-workarea mappings
        if (workAreas.length === 0) {
          console.log(`DEBUG: Looking for work areas for group ${group.name} (ID: ${groupId})`);
          console.log('DEBUG: Available group-workarea mappings:', this.currentGroupWorkareaMappings);

          const groupWorkareaMapping = this.currentGroupWorkareaMappings.find(m => m.groupId === String(groupId));
          if (groupWorkareaMapping && groupWorkareaMapping.workAreas) {
            workAreas = [...groupWorkareaMapping.workAreas];
            console.log(`DEBUG: Found group-workarea mapping for group ${group.name}:`, workAreas);
          } else {
            // Fallback to category-workarea mappings for backward compatibility
            console.log(`DEBUG: No group-workarea mapping found, falling back to category-workarea mappings for categories:`, categories);
            const workAreasFromMappings = new Set<string>();
            categories.forEach((categoryName: string) => {
              const categoryMapping = this.categoryWorkareaMappings.find(m => m.categoryName === categoryName);
              if (categoryMapping && categoryMapping.workAreas) {
                categoryMapping.workAreas.forEach(wa => workAreasFromMappings.add(wa));
              }
            });
            workAreas = Array.from(workAreasFromMappings);
            console.log(`DEBUG: Resolved work areas from category mappings for group ${group.name}:`, workAreas);
          }
        }

        console.log(`DEBUG: Creating form group for ${group.name} with:`, {
          groupId: String(groupId),
          categories,
          workAreas,
          categoriesCount: categories.length,
          workAreasCount: workAreas.length
        });

        const mappingGroup = this.fb.group({
          groupId: [String(groupId)],
          groupName: [group.name],
          categories: [categories],
          workAreas: [workAreas]
        });

        // Subscribe to category changes for this group with debounce
        mappingGroup.get('categories')?.valueChanges
          .pipe(
            debounceTime(200), // Debounce to prevent rapid fire on multi-select
            takeUntil(this.destroy$)
          )
          .subscribe(() => {
            console.log(`DEBUG: Categories changed for group ${group.name}:`, mappingGroup.get('categories')?.value);



            if (this.autoEmit && !this.isLoading) {
              this.emitCurrentGroupMappings();
            }
          });

        mappingsArray.push(mappingGroup);
      }
    });

    this.cdr.detectChanges();
  }



  private emitCurrentGroupMappings(): void {
    if (!this.autoEmit || this.isLoading) {
      return;
    }

    const mappingsArray = this.groupMappingForm.get('mappings') as FormArray;
    const currentMappings: DepartmentGroupCategoryMapping[] = mappingsArray.value;

    // Filter out mappings without categories
    const validMappings = currentMappings.filter(m => m.categories && m.categories.length > 0);

    // Emit group mappings to parent component
    console.log('DEBUG: Emitting group mappings:', validMappings);
    this.groupMappingsChanged.emit(validMappings);
  }

  // Public methods for external access
  getCurrentGroupMappings(): DepartmentGroupCategoryMapping[] {
    if (!this.groupMappingForm) {
      console.log('DEBUG: getCurrentGroupMappings - no groupMappingForm');
      return [];
    }
    const mappingsArray = this.groupMappingForm.get('mappings') as FormArray;
    if (!mappingsArray) {
      console.log('DEBUG: getCurrentGroupMappings - no mappingsArray');
      return [];
    }
    const mappings = mappingsArray.value;
    console.log('DEBUG: getCurrentGroupMappings - raw mappings from form:', mappings);
    console.log('DEBUG: getCurrentGroupMappings - mappingsArray length:', mappingsArray.length);
    console.log('DEBUG: getCurrentGroupMappings - mappingsArray controls:', mappingsArray.controls.map(c => c.value));
    return mappings || [];
  }





  // Group-related getters
  get selectedDepartmentGroupsCount(): number {
    return this.selectedDepartmentGroupsCtrl.value?.length || 0;
  }

  get mappedGroupsCount(): number {
    const mappings = this.getCurrentGroupMappings();
    return mappings.filter(m => m.categories && m.categories.length > 0).length;
  }

  get totalCategoriesAssigned(): number {
    const mappings = this.getCurrentGroupMappings();
    return mappings.reduce((total, m) => total + (m.categories ? m.categories.length : 0), 0);
  }

  get hasSelectedDepartmentGroups(): boolean {
    return this.selectedDepartmentGroupsCount > 0;
  }

  get groupMappingsFormArray(): FormArray {
    return this.groupMappingForm?.get('mappings') as FormArray || this.fb.array([]);
  }

  getAvailableCategoriesForGroup(groupIndex: number): string[] {
    const mappingsArray = this.groupMappingForm.get('mappings') as FormArray;
    const usedCategories = new Set<string>();

    // Collect categories used by other groups
    for (let i = 0; i < mappingsArray.length; i++) {
      if (i !== groupIndex) {
        const categories = mappingsArray.at(i).get('categories')?.value || [];
        categories.forEach((cat: string) => usedCategories.add(cat));
      }
    }

    // Return categories not used by other groups
    const availableCategories = this.categories.filter(cat => !usedCategories.has(cat));
    return availableCategories;
  }

  // UI Helper methods
  trackByIndex(index: number): number {
    return index;
  }

  // Group UI Helper methods
  toggleAllDepartmentGroups(event: Event): void {
    event.stopPropagation();

    const allSelected = this.selectedDepartmentGroupsCtrl.value?.length === this.filteredDepartmentGroups.length;
    if (allSelected) {
      this.selectedDepartmentGroupsCtrl.setValue([]);
    } else {
      this.selectedDepartmentGroupsCtrl.setValue(this.filteredDepartmentGroups.map(g => g.id));
    }
  }

  areAllDepartmentGroupsSelected(): boolean {
    return this.selectedDepartmentGroupsCtrl.value?.length === this.filteredDepartmentGroups.length;
  }

  trackByGroupId(_index: number, item: any): string {
    // Handle both regular objects and form groups
    if (item.get) {
      return item.get('groupId')?.value || item.get('groupName')?.value || _index.toString();
    }
    return item.groupId || item.groupName || _index.toString();
  }

  // ===== CREATE GROUP METHODS =====
  openCreateGroupDialog(): void {
    this.showCreateGroupDialog = true;
    this.createGroupForm.reset();
    this.createGroupForm.patchValue({
      groupName: '',
      departmentIds: []
    });
    // Ensure validation is up to date
    this.updateCreateGroupFormValidation();
  }

  closeCreateGroupDialog(): void {
    this.showCreateGroupDialog = false;
    this.createGroupForm.reset();
  }

  createDepartmentGroup(): void {
    if (this.createGroupForm.invalid) {
      // Mark all fields as touched to show validation errors
      this.createGroupForm.markAllAsTouched();
      this.showError('Please fill in all required fields');
      return;
    }

    const formValue = this.createGroupForm.value;

    // Additional validation
    if (!formValue.groupName || formValue.groupName.trim().length < 2) {
      this.showError('Group name must be at least 2 characters long');
      return;
    }

    if (this.departments.length > 0 && (!formValue.departmentIds || formValue.departmentIds.length === 0)) {
      this.showError('Please select at least one department');
      return;
    }

    this.isCreatingGroup = true;

    this.departmentService.createDepartmentGroup(
      this.tenantId,
      formValue.groupName.trim(),
      '',
      formValue.departmentIds || []
    ).pipe(takeUntil(this.destroy$))
    .subscribe({
      next: (result) => {
        this.isCreatingGroup = false;
        this.showSuccess('Department group created successfully!');
        this.closeCreateGroupDialog();

        // Reload department groups to include the new one
        this.loadDepartmentGroups().then(() => {
          // Auto-select the newly created group
          if (result.groupId) {
            const currentSelection = this.selectedDepartmentGroupsCtrl.value || [];
            this.selectedDepartmentGroupsCtrl.setValue([...currentSelection, result.groupId]);
          }
        });
      },
      error: (error) => {
        this.isCreatingGroup = false;
        this.showError('Failed to create department group. Please try again.');
        console.error('Error creating department group:', error);
      }
    });
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }

  // ===== EDIT GROUP METHODS =====
  editDepartmentGroup(groupId: string): void {
    const group = this.departmentGroups.find(g => g.id === groupId);
    if (!group) {
      this.showError('Group not found');
      return;
    }

    // Set editing state
    this.isEditingGroup = true;
    this.editingGroupId = groupId;
    this.showCreateGroupDialog = true;

    // Populate form with existing group data
    this.createGroupForm.patchValue({
      groupName: group.name,
      departmentIds: group.departmentIds || []
    });

    // Update validation
    this.updateCreateGroupFormValidation();
  }

  updateDepartmentGroup(): void {
    if (this.createGroupForm.invalid || !this.editingGroupId) {
      this.createGroupForm.markAllAsTouched();
      return;
    }

    this.isCreatingGroup = true;
    const formValue = this.createGroupForm.value;

    const updatedGroup: DepartmentGroup = {
      id: this.editingGroupId,
      name: formValue.groupName.trim(),
      departmentIds: formValue.departmentIds || [],
      isActive: true
    };

    this.departmentService.updateDepartmentGroup(this.tenantId, updatedGroup).subscribe({
      next: () => {
        this.isCreatingGroup = false;
        this.showSuccess('Department group updated successfully!');
        this.closeEditGroupDialog();

        // Reload department groups to reflect changes
        this.loadDepartmentGroups().then(() => {
          // Maintain selection of the updated group
          const currentSelection = this.selectedDepartmentGroupsCtrl.value || [];
          if (!currentSelection.includes(this.editingGroupId!)) {
            this.selectedDepartmentGroupsCtrl.setValue([...currentSelection, this.editingGroupId!]);
          }
        });
      },
      error: (error: any) => {
        this.isCreatingGroup = false;
        this.showError('Failed to update department group. Please try again.');
        console.error('Error updating department group:', error);
      }
    });
  }

  closeEditGroupDialog(): void {
    this.isEditingGroup = false;
    this.editingGroupId = null;
    this.showCreateGroupDialog = false;
    this.createGroupForm.reset();
  }

  deleteDepartmentGroup(groupId: string, groupName: string): void {
    if (!groupId) {
      this.showError('Invalid group ID');
      return;
    }

    // Show custom confirmation dialog
    this.confirmationDialogService.confirmDelete(
      groupName,
      'This will also remove all category and work area mappings for this group.'
    ).subscribe(confirmed => {
      if (!confirmed) {
        return;
      }

      this.departmentService.deleteDepartmentGroup(this.tenantId, groupId)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (success) => {
            if (success) {
              this.showSuccess(`Group "${groupName}" deleted successfully!`);

              // Remove from local arrays
              this.departmentGroups = this.departmentGroups.filter(g => g.id !== groupId);
              this.filteredDepartmentGroups = this.filteredDepartmentGroups.filter(g => g.id !== groupId);

              // Remove from selected groups
              const currentSelected = this.selectedDepartmentGroupsCtrl.value || [];
              const updatedSelected = currentSelected.filter(id => id !== groupId);
              this.selectedDepartmentGroupsCtrl.setValue(updatedSelected);

              // Remove from current mappings
              this.currentGroupMappings = this.currentGroupMappings.filter(m => m.groupId !== groupId);
              this.currentGroupWorkareaMappings = this.currentGroupWorkareaMappings.filter(m => m.groupId !== groupId);

              // Rebuild the form to reflect changes
              this.updateGroupMappingForm(this.selectedDepartmentGroupsCtrl.value || []);
            } else {
              this.showError('Failed to delete group');
            }
          },
          error: (error) => {
            this.showError('Failed to delete group: ' + error.message);
          }
        });
    });
  }

  deleteCurrentEditingGroup(): void {
    if (!this.editingGroupId) {
      this.showError('No group selected for deletion');
      return;
    }

    const groupName = this.createGroupForm.get('name')?.value || 'Unknown';

    // Close the dialog first
    this.closeEditGroupDialog();

    // Then delete the group
    this.deleteDepartmentGroup(this.editingGroupId, groupName);
  }

  // ===== CATEGORY-WORKAREA MAPPING METHODS =====

  get selectedCategoriesFromGroups(): string[] {
    const allSelectedCategories: string[] = [];

    // Get categories from group mappings
    if (this.groupMappingsFormArray && this.groupMappingsFormArray.controls) {
      this.groupMappingsFormArray.controls.forEach((control, index) => {
        const groupName = control.get('groupName')?.value;
        const categories = control.get('categories')?.value || [];
        console.log(`DEBUG: selectedCategoriesFromGroups - Group ${index} (${groupName}):`, categories);
        allSelectedCategories.push(...categories);
      });
    }

    // Remove duplicates
    const result = [...new Set(allSelectedCategories)];
    console.log('DEBUG: selectedCategoriesFromGroups final result:', result);
    return result;
  }

  get hasSelectedCategories(): boolean {
    const result = this.selectedCategoriesFromGroups.length > 0;
    console.log('DEBUG: hasSelectedCategories called, result:', result, 'categories:', this.selectedCategoriesFromGroups);
    return result;
  }

  get hasValidMappings(): boolean {
    if (!this.groupMappingForm) {
      return false;
    }
    const mappingsArray = this.groupMappingForm.get('mappings') as FormArray;
    if (!mappingsArray) {
      return false;
    }
    const currentMappings: DepartmentGroupCategoryMapping[] = mappingsArray.value || [];
    return currentMappings.some(m => m.categories && m.categories.length > 0);
  }

  onCategoryWorkareaMapping(mappings: CategoryWorkareaMapping[]): void {
    this.categoryWorkareaMappings = mappings;
  }



  saveAndClose(): void {
    if (!this.hasValidMappings) {
      this.showError('Please assign at least one category to a group before saving.');
      return;
    }

    // Validate form data before saving
    if (!this.validateFormData()) {
      return;
    }

    this.isSaving = true;

    // Get all mappings from the combined form
    const allGroupMappings = this.getCurrentGroupMappings();
    console.log('DEBUG: All group mappings from form:', allGroupMappings);
    console.log('DEBUG: Selected department groups:', this.selectedDepartmentGroupsCtrl.value);
    console.log('DEBUG: Form array length:', (this.groupMappingForm.get('mappings') as FormArray).length);

    const groupMappingsToSave = allGroupMappings.filter(m => {
      const hasCategories = m.categories && m.categories.length > 0;
      return hasCategories;
    });

    console.log('DEBUG: Filtered group mappings to save:', groupMappingsToSave);
    console.log('DEBUG: Number of groups with categories:', groupMappingsToSave.length);

    // Get category-workarea mappings from the combined group form
    const currentCategoryWorkareaMappings = this.getCategoryWorkareaMappingsFromGroupForm();
    console.log('DEBUG: Raw category-workarea mappings from group form:', currentCategoryWorkareaMappings);
    console.log('DEBUG: Number of category mappings:', currentCategoryWorkareaMappings.length);

    // Log each mapping in detail
    currentCategoryWorkareaMappings.forEach((mapping, index) => {
      console.log(`DEBUG: Mapping ${index + 1}:`, {
        categoryName: mapping.categoryName,
        workAreas: mapping.workAreas,
        workAreasCount: mapping.workAreas.length
      });
    });

    // Process intelligent mappings to create virtual workareas for conflicts
    const processedWorkareaMappings = currentCategoryWorkareaMappings.length > 0
      ? this.processIntelligentMappings(currentCategoryWorkareaMappings)
      : [];

    console.log('DEBUG: Processed workarea mappings:', processedWorkareaMappings);

    console.log('DEBUG: Simplified data being sent to backend:');
    console.log('- Group Mappings:', groupMappingsToSave.length);
    console.log('- Category Workarea Mappings:', processedWorkareaMappings.length);
    console.log('Group mappings:', JSON.stringify(groupMappingsToSave, null, 2));
    console.log('Category workarea mappings:', JSON.stringify(processedWorkareaMappings, null, 2));

    // Convert to group-centric format with work areas at group level
    const groupCentricMappings = groupMappingsToSave.map(mapping => ({
      ...mapping,
      workAreas: this.getWorkAreasForGroup(mapping.groupId)
    }));

    console.log('DEBUG: Group-centric mappings being sent:', groupCentricMappings);

    // Use the new group-centric API
    this.departmentService.saveGroupCentricMappings(
      this.tenantId,
      groupCentricMappings
    ).pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (success) => {
          this.isSaving = false;
          if (success) {
            this.showSuccess('All mappings saved successfully!');

            // Update local state with saved data
            this.currentGroupMappings = groupMappingsToSave;

            // Update group-workarea mappings
            this.currentGroupWorkareaMappings = groupCentricMappings.map(mapping => ({
              groupId: mapping.groupId,
              groupName: mapping.groupName,
              workAreas: mapping.workAreas || []
            }));

            console.log('DEBUG: Updated local state after save:');
            console.log('- Current group mappings:', this.currentGroupMappings);
            console.log('- Current group workarea mappings:', this.currentGroupWorkareaMappings);

            // Emit changes to parent components
            this.emitCurrentGroupMappings();
            // For backward compatibility, still emit category-workarea mappings
            this.categoryWorkareaMappingsChanged.emit(processedWorkareaMappings);

            // Force form refresh to ensure work areas are populated
            const selectedGroupIds = this.selectedDepartmentGroupsCtrl.value || [];
            if (selectedGroupIds.length > 0) {
              console.log('DEBUG: Force refreshing form after save with group IDs:', selectedGroupIds);

              // Clear the form first to force a complete rebuild
              const mappingsArray = this.groupMappingForm.get('mappings') as FormArray;
              while (mappingsArray.length !== 0) {
                mappingsArray.removeAt(0);
              }

              // Then rebuild with updated data
              this.updateGroupMappingForm(selectedGroupIds);
            }

            this.closeDialog();
          } else {
            this.showError('Failed to save mappings. Please try again.');
          }
          this.cdr.detectChanges();
        },
        error: (error) => {
          this.isSaving = false;
          console.error('Error saving mappings:', error);
          this.showError('An error occurred while saving mappings. Please try again.');
          this.cdr.detectChanges();
        }
      });
  }



  private getCategoryWorkareaMappingsFromGroupForm(): CategoryWorkareaMapping[] {
    const mappings: CategoryWorkareaMapping[] = [];
    const mappingsArray = this.groupMappingForm.get('mappings') as FormArray;

    for (let i = 0; i < mappingsArray.length; i++) {
      const groupControl = mappingsArray.at(i) as FormGroup;
      const categories = groupControl.get('categories')?.value || [];
      const workAreas = groupControl.get('workAreas')?.value || [];

      // Create category-workarea mappings for each category in this group
      categories.forEach((categoryName: string) => {
        const existingMapping = mappings.find(m => m.categoryName === categoryName);
        if (existingMapping) {
          // Merge work areas if category already exists (shouldn't happen with proper UI, but safety check)
          const combinedWorkAreas = [...new Set([...existingMapping.workAreas, ...workAreas])];
          existingMapping.workAreas = combinedWorkAreas;
        } else {
          mappings.push({
            categoryName,
            workAreas: [...workAreas]
          });
        }
      });
    }

    return mappings;
  }





  getAvailableWorkAreasForGroup(groupIndex: number): string[] {
    const mappingsArray = this.groupMappingForm.get('mappings') as FormArray;
    const groupFormGroup = mappingsArray.at(groupIndex) as FormGroup;
    const groupName = groupFormGroup?.get('groupName')?.value;
    console.log(`DEBUG: Getting available work areas for group ${groupIndex} (${groupName}):`, this.allWorkAreas);
    return this.allWorkAreas;
  }

  getWorkAreasForGroup(groupId: string): string[] {
    // First try to get from form
    const mappingsArray = this.groupMappingForm.get('mappings') as FormArray;
    for (let i = 0; i < mappingsArray.length; i++) {
      const control = mappingsArray.at(i);
      if (control.get('groupId')?.value === groupId) {
        const workAreas = control.get('workAreas')?.value || [];
        if (workAreas.length > 0) {
          return workAreas;
        }
      }
    }

    // Fallback to stored group-workarea mappings
    const groupWorkareaMapping = this.currentGroupWorkareaMappings.find(m => m.groupId === groupId);
    if (groupWorkareaMapping) {
      return groupWorkareaMapping.workAreas || [];
    }

    return [];
  }

  onGroupWorkAreasChange(groupIndex: number, selectedWorkAreas: string[]): void {
    // Update the combined group mapping form
    const mappingsArray = this.groupMappingForm.get('mappings') as FormArray;
    const formGroup = mappingsArray.at(groupIndex) as FormGroup;

    // Update the work areas form control
    formGroup?.get('workAreas')?.setValue(selectedWorkAreas);

    // Trigger change detection
    this.cdr.detectChanges();
  }

  // Method to manually refresh work areas for all groups
  refreshWorkAreasForAllGroups(): void {
    console.log('DEBUG: Manually refreshing work areas for all groups');
    const selectedGroupIds = this.selectedDepartmentGroupsCtrl.value || [];
    if (selectedGroupIds.length > 0) {
      // Force a complete form rebuild
      const mappingsArray = this.groupMappingForm.get('mappings') as FormArray;
      while (mappingsArray.length !== 0) {
        mappingsArray.removeAt(0);
      }
      this.updateGroupMappingForm(selectedGroupIds);
    }
  }











  private extractAllWorkAreas(): void {
    const workAreaSet = new Set<string>();
    this.workAreas.forEach((branch) => {
      if (branch.workAreas && Array.isArray(branch.workAreas)) {
        branch.workAreas.forEach(wa => {
          workAreaSet.add(wa);
        });
      }
    });

    this.allWorkAreas = Array.from(workAreaSet).sort();
  }



  private processIntelligentMappings(mappings: CategoryWorkareaMapping[]): CategoryWorkareaMapping[] {

    // Smart mapping logic - handle conflicts by creating virtual workareas
    const processedMappings: CategoryWorkareaMapping[] = [];
    const workareaUsage = new Map<string, string[]>(); // workarea -> categories using it

    // Track workarea usage
    mappings.forEach(mapping => {
      mapping.workAreas.forEach(workarea => {
        if (!workareaUsage.has(workarea)) {
          workareaUsage.set(workarea, []);
        }
        workareaUsage.get(workarea)!.push(mapping.categoryName);
      });
    });



    // Process each mapping - simplified without virtual workareas
    mappings.forEach(mapping => {
      const processedMapping: CategoryWorkareaMapping = {
        categoryName: mapping.categoryName,
        workAreas: mapping.workAreas // Use workareas as-is
      };

      processedMappings.push(processedMapping);
    });


    return processedMappings;
  }

  trackByCategoryName(index: number, item: any): string {
    return item.get('categoryName')?.value || index.toString();
  }

  trackByWorkArea(_index: number, item: string): string {
    return item;
  }



  private validateFormData(): boolean {
    const mappingsArray = this.groupMappingForm.get('mappings') as FormArray;

    if (!mappingsArray || mappingsArray.length === 0) {
      this.showError('No department groups selected for mapping.');
      return false;
    }

    let hasValidMappings = false;
    const errors: string[] = [];

    for (let i = 0; i < mappingsArray.length; i++) {
      const groupControl = mappingsArray.at(i) as FormGroup;
      const groupName = groupControl.get('groupName')?.value;
      const categories = groupControl.get('categories')?.value || [];
      const workAreas = groupControl.get('workAreas')?.value || [];

      if (categories.length > 0) {
        hasValidMappings = true;

        // Validate that categories are strings
        const invalidCategories = categories.filter((cat: any) => typeof cat !== 'string' || !cat.trim());
        if (invalidCategories.length > 0) {
          errors.push(`Group "${groupName}" has invalid categories.`);
        }

        // Validate that work areas are strings
        const invalidWorkAreas = workAreas.filter((wa: any) => typeof wa !== 'string' || !wa.trim());
        if (invalidWorkAreas.length > 0) {
          errors.push(`Group "${groupName}" has invalid work areas.`);
        }
      }
    }

    if (!hasValidMappings) {
      this.showError('Please assign at least one category to a group before saving.');
      return false;
    }

    if (errors.length > 0) {
      this.showError('Validation errors: ' + errors.join(' '));
      return false;
    }

    console.log('DEBUG: Form validation passed successfully');
    return true;
  }

  closeDialog(): void {
    this.dialogClosed.emit();
  }
}
