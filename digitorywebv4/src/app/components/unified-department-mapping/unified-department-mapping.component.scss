
// Compact design
.unified-department-mapping {
  padding: 8px;
  background: #fafafa;

  &.dialog-mode {
    padding: 6px;
    background: white;
  }
}

// Header
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 8px 12px;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  margin-bottom: 12px;

  .header-left {
    display: flex;
    align-items: center;
    gap: 8px;

    mat-icon {
      font-size: 20px;
      width: 20px;
      height: 20px;
      color: #ff6b35;
    }

    h2 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
  }

  button {
    height: 32px;
    font-weight: 500;
    font-size: 13px;
  }
}

// Loading state
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  background: white;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);

  p {
    margin-top: 12px;
    color: #666;
    font-size: 14px;
  }
}

// Main content
.mapping-content {
  max-width: 1000px;
  margin: 0 auto;
}

// Step sections
.step-section {
  background: white;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  margin-bottom: 12px;
  overflow: hidden;

  .step-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;

    .step-number {
      background: #ff6b35;
      color: white;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: 600;
      margin-right: 8px;
    }

    h3 {
      margin: 0;
      font-size: 14px;
      font-weight: 600;
      color: #333;
      flex: 1;
    }

    .summary-badge {
      background: #ff6b35;
      color: white;
      padding: 2px 6px;
      border-radius: 10px;
      font-size: 10px;
      font-weight: 600;
    }
  }

  .step-content {
    padding: 12px;
  }
}

// Group mapping section
.group-mapping-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 24px;
  overflow: hidden;

  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #e0e0e0;

    .step-indicator {
      display: flex;
      align-items: center;
      gap: 12px;

      mat-icon {
        color: #ff6b35;
        font-size: 24px;
        width: 24px;
        height: 24px;
      }

      .step-number {
        background: #ff6b35;
        color: white;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        font-weight: 600;
      }
    }

    .step-content {
      flex: 1;
      margin-left: 16px;

      h3 {
        margin: 0 0 4px 0;
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }

      .step-description {
        margin: 0;
        font-size: 14px;
        color: #666;
      }
    }

    .section-summary {
      .summary-badge {
        background: #ff6b35;
        color: white;
        padding: 6px 12px;
        border-radius: 16px;
        font-size: 12px;
        font-weight: 600;
      }
    }
  }
}

// Form fields
.group-select {
  width: 100%;
  margin-bottom: 8px;

  ::ng-deep {
    .mat-mdc-form-field-wrapper {
      background: #f8f9fa;
      border-radius: 4px;
      height: 40px;
    }

    .mat-mdc-form-field-infix {
      padding: 6px 12px;
      min-height: 28px;
    }

    .mat-mdc-form-field-label-wrapper {
      height: 28px;
    }

    .mat-mdc-form-field-label {
      font-size: 13px;
    }

    .mat-mdc-select-value {
      font-size: 13px;
      line-height: 28px;
    }

    .mat-mdc-select-arrow-wrapper {
      height: 28px;
    }
  }
}

// Empty states
.no-data-message {
  text-align: center;
  padding: 24px;
  color: #666;

  mat-icon {
    font-size: 32px;
    width: 32px;
    height: 32px;
    color: #ccc;
    margin-bottom: 8px;
  }

  p {
    margin: 8px 0 16px 0;
    font-size: 14px;
  }
}

// Group mapping containers
.mappings-container {
  padding: 0 12px 12px;
}

.mapping-card {
  background: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  margin-bottom: 8px;

  .card-content {
    padding: 8px;
  }

  .group-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 4px 0;
    margin-bottom: 8px;
    border-bottom: 1px solid #e0e0e0;

    .group-title {
      display: flex;
      align-items: center;
      gap: 6px;
      flex: 1;

      mat-icon {
        color: #ff6b35;
        font-size: 16px;
        width: 16px;
        height: 16px;
      }

      .group-name {
        font-weight: 600;
        color: #333;
        font-size: 13px;
      }

      .group-count {
        font-size: 10px;
        color: #666;
        margin-left: 4px;
      }
    }

    .group-actions {
      display: flex;
      gap: 2px;

      button {
        width: 24px;
        height: 24px;
        min-width: 24px;
        padding: 0;

        mat-icon {
          font-size: 14px;
          width: 14px;
          height: 14px;
        }
      }

      .delete-btn {
        color: #dc3545;
      }
    }
  }

  .selection-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;

    .field-compact {
      ::ng-deep {
        .mat-mdc-form-field-wrapper {
          background: white;
          border-radius: 4px;
          height: 36px;
        }

        .mat-mdc-form-field-infix {
          padding: 4px 8px;
          min-height: 28px;
        }

        .mat-mdc-form-field-subscript-wrapper {
          margin-top: 2px;
          font-size: 11px;
        }

        .mat-mdc-select-value {
          font-size: 12px;
          line-height: 28px;
        }

        .mat-mdc-select-arrow-wrapper {
          height: 28px;
        }

        .mat-mdc-form-field-hint-wrapper {
          font-size: 11px;
        }
      }
    }
  }
}

// Action section
.action-section {
  padding: 16px;
  border-top: 1px solid #ddd;
  background: #f9f9f9;

  .action-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-bottom: 8px;

    .save-button {
      min-width: 120px;

      .button-spinner {
        margin-right: 6px;
      }
    }
  }

  .save-status {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #dc3545;
    font-size: 12px;
    justify-content: center;

    .warning-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }
  }
}

// Empty states
.no-groups-message,
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #666;

  mat-icon,
  .empty-icon {
    font-size: 48px;
    width: 48px;
    height: 48px;
    color: #ccc;
    margin-bottom: 12px;
  }

  h3 {
    margin: 0 0 8px 0;
    color: #333;
    font-size: 16px;
  }

  p {
    margin: 0;
    font-size: 13px;
    max-width: 300px;
    margin: 0 auto;
    line-height: 1.4;
  }
}

// Summary section
.summary-section {
  margin-bottom: 16px;
  border: 1px solid #ddd;
  border-radius: 4px;

  .summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1px;
    background: #ddd;

    .summary-card {
      background: white;
      padding: 16px;
      text-align: center;

      .summary-number {
        font-size: 24px;
        font-weight: 700;
        color: #333;
        margin-bottom: 4px;
      }

      .summary-label {
        font-size: 12px;
        color: #666;
        font-weight: 500;
      }
    }
  }
}

// Simple dialog
.create-group-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.create-group-dialog {
  background: white;
  border-radius: 4px;
  width: 90%;
  max-width: 450px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  margin: 20px;

  .dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    background: #f5f5f5;
    border-bottom: 1px solid #ddd;

    h3 {
      margin: 0;
      display: flex;
      align-items: center;
      gap: 6px;
      color: #333;
      font-weight: 600;
      font-size: 16px;
    }

    button {
      color: #666;
      width: 32px;
      height: 32px;
      min-width: 32px;
      padding: 0;

      &:hover {
        background: #e9e9e9;
      }
    }
  }

  .dialog-content {
    padding: 20px;

    .full-width {
      width: 100%;
      margin-bottom: 12px;
    }
  }

  .dialog-actions {
    display: flex;
    gap: 8px;
    justify-content: space-between;
    padding: 12px 20px;
    border-top: 1px solid #ddd;
    background: #f9f9f9;

    .button-spinner {
      margin-right: 6px;
    }
  }
}
