<div class="unified-department-mapping" [class.dialog-mode]="showAsDialog">
  <!-- Header -->
  <div class="header">
    <div class="header-left">
      <mat-icon>account_tree</mat-icon>
      <h2>Department Group Mapping</h2>
    </div>
    <button mat-raised-button color="primary" (click)="openCreateGroupDialog()">
      <mat-icon>add</mat-icon>
      Create Group
    </button>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading || !formsInitialized" class="loading-container">
    <mat-spinner diameter="40"></mat-spinner>
    <p>Loading departments and categories...</p>
  </div>

  <!-- Main Content -->
  <div *ngIf="!isLoading && formsInitialized" class="mapping-content">

    <!-- Step 1: Select Groups -->
    <div class="step-section">
      <div class="step-header">
        <div class="step-number">1</div>
        <h3>Select Department Groups</h3>
      </div>

      <div class="step-content">
        <mat-form-field *ngIf="departmentGroups.length > 0" appearance="outline" class="group-select">
          <mat-label>Department Groups</mat-label>
          <mat-select [formControl]="selectedDepartmentGroupsCtrl" multiple>

          <!-- Search -->
          <mat-option>
            <ngx-mat-select-search [formControl]="departmentGroupFilterCtrl"
              placeholderLabel="Search department groups..."
              noEntriesFoundLabel="No department groups found">
            </ngx-mat-select-search>
          </mat-option>

          <!-- Select All / Deselect All -->
          <div class="select-all-custom-option" (click)="toggleAllDepartmentGroups($event)">
            <strong>{{areAllDepartmentGroupsSelected() ? 'Deselect All' : 'Select All'}}</strong>
          </div>
          <mat-divider></mat-divider>

          <!-- Department Group Options -->
          <mat-option *ngFor="let group of filteredDepartmentGroups; trackBy: trackByIndex" [value]="group.id">
            {{group.name}} <span *ngIf="group.description">({{group.description}})</span>
          </mat-option>
        </mat-select>
      </mat-form-field>

      <!-- Create Group Section (only show when not loading and no groups) -->
      <div *ngIf="!isLoading && departmentGroups.length === 0" class="no-data-message">
        <mat-icon>folder_open</mat-icon>
        <p>Create your first department group to organize departments and manage category mappings efficiently.</p>
        <button mat-raised-button color="primary" (click)="openCreateGroupDialog()">
          <mat-icon>add</mat-icon>
          Create Department Group
        </button>
      </div>


    </div>

    <!-- Step 2: Assign Categories & Work Areas -->
    <div class="step-section" *ngIf="hasSelectedDepartmentGroups">
      <div class="step-header">
        <div class="step-number">2</div>
        <h3>Assign Categories & Work Areas</h3>
        <div class="summary-badge">{{selectedDepartmentGroupsCount}} groups selected</div>
      </div>

      <form [formGroup]="groupMappingForm">
        <div formArrayName="mappings" class="mappings-container">
          <div *ngFor="let mappingGroup of groupMappingsFormArray.controls; let i = index; trackBy: trackByGroupId"
               [formGroupName]="i" class="mapping-card">

            <div class="card-content">
              <!-- Group Header -->
              <div class="group-header">
                <div class="group-title">
                  <mat-icon>folder</mat-icon>
                  <span class="group-name">{{mappingGroup.get('groupName')?.value}}</span>
                  <span class="group-count">({{mappingGroup.get('categories')?.value?.length || 0}} categories, {{mappingGroup.get('workAreas')?.value?.length || 0}} work areas)</span>
                </div>
                <div class="group-actions">
                  <button mat-icon-button
                          (click)="editDepartmentGroup(mappingGroup.get('groupId')?.value)"
                          matTooltip="Edit Group">
                    <mat-icon>edit</mat-icon>
                  </button>
                  <button mat-icon-button
                          (click)="deleteDepartmentGroup(mappingGroup.get('groupId')?.value, mappingGroup.get('groupName')?.value)"
                          matTooltip="Delete Group"
                          class="delete-btn">
                    <mat-icon>delete</mat-icon>
                  </button>
                </div>
              </div>

              <!-- Compact Selection Row -->
              <div class="selection-grid">
                <mat-form-field appearance="outline" class="field-compact">
                  <mat-select formControlName="categories" multiple placeholder="Select categories">
                    <mat-option *ngFor="let category of getAvailableCategoriesForGroup(i)" [value]="category">
                      {{category}}
                    </mat-option>
                  </mat-select>
                  <mat-hint>Categories</mat-hint>
                </mat-form-field>

                <mat-form-field appearance="outline" class="field-compact">
                  <mat-select formControlName="workAreas" multiple
                    placeholder="Select work areas"
                    (selectionChange)="onGroupWorkAreasChange(i, $event.value)">
                    <mat-option *ngFor="let workArea of getAvailableWorkAreasForGroup(i); trackBy: trackByWorkArea"
                      [value]="workArea">
                      {{ workArea }}
                    </mat-option>
                  </mat-select>
                  <mat-hint>Work Areas</mat-hint>
                </mat-form-field>
              </div>
            </div>
          </div>
        </div>
      </form>

      <!-- No Categories Available Message -->
      <div *ngIf="categories.length === 0" class="no-data-message">
        <mat-icon>info</mat-icon>
        <p>No categories available for mapping</p>
      </div>
    </div>




    <!-- Summary Section (only show when not in dialog) -->
    <div class="summary-section" *ngIf="hasSelectedDepartmentGroups && !showAsDialog">
      <div class="section-header">
        <mat-icon>summarize</mat-icon>
        <h4>Mapping Summary</h4>
      </div>

      <div class="summary-cards">
        <div class="summary-card">
          <div class="summary-number">{{selectedDepartmentGroupsCount}}</div>
          <div class="summary-label">Groups Selected</div>
        </div>

        <div class="summary-card">
          <div class="summary-number">{{mappedGroupsCount}}</div>
          <div class="summary-label">Groups Mapped</div>
        </div>

        <div class="summary-card">
          <div class="summary-number">{{totalCategoriesAssigned}}</div>
          <div class="summary-label">Total Categories Assigned</div>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="action-section" *ngIf="hasSelectedDepartmentGroups">
      <div class="action-buttons">
        <button mat-stroked-button (click)="closeDialog()" *ngIf="showAsDialog" class="cancel-btn">
          <mat-icon>close</mat-icon>
          Cancel
        </button>

        <button mat-raised-button color="primary"
                (click)="saveAndClose()"
                [disabled]="isSaving || !hasValidMappings"
                class="save-button">
          <mat-spinner *ngIf="isSaving" diameter="20" class="button-spinner"></mat-spinner>
          <mat-icon *ngIf="!isSaving">save</mat-icon>
          {{ isSaving ? 'Saving...' : (showAsDialog ? 'Save & Close' : 'Save Configuration') }}
        </button>
      </div>

      <div class="save-status" *ngIf="!hasValidMappings && selectedDepartmentGroupsCount > 0">
        <mat-icon class="warning-icon">warning</mat-icon>
        <span>Please assign at least one category to save the configuration</span>
      </div>
    </div>

    <!-- Empty State (when groups exist but none selected) -->
    <div *ngIf="!isLoading && departmentGroups.length > 0 && !hasSelectedDepartmentGroups" class="empty-state">
      <mat-icon class="empty-icon">folder</mat-icon>
      <h3>Select Department Groups to Begin</h3>
      <p>Choose one or more department groups from the dropdown above to start configuring category mappings.</p>
    </div>
  </div>
</div>

<!-- Create Group Dialog -->
<div class="create-group-overlay" *ngIf="showCreateGroupDialog" (click)="closeCreateGroupDialog()">
  <div class="create-group-dialog" (click)="$event.stopPropagation()">
    <div class="dialog-header">
      <h3>
        <mat-icon>{{ isEditingGroup ? 'edit' : 'add' }}</mat-icon>
        {{ isEditingGroup ? 'Edit Department Group' : 'Create Department Group' }}
      </h3>
      <button mat-icon-button (click)="isEditingGroup ? closeEditGroupDialog() : closeCreateGroupDialog()">
        <mat-icon>close</mat-icon>
      </button>
    </div>

    <div class="dialog-content">
      <!-- Loading state -->
      <div *ngIf="isLoading" class="dialog-loading">
        <mat-spinner diameter="30"></mat-spinner>
        <p>Loading departments...</p>
      </div>

      <!-- Form content -->
      <form [formGroup]="createGroupForm" *ngIf="!isLoading">
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Group Name</mat-label>
          <input matInput formControlName="groupName" placeholder="Enter group name (e.g., Food Department)">
          <mat-error *ngIf="createGroupForm.get('groupName')?.hasError('required')">
            Group name is required
          </mat-error>
          <mat-error *ngIf="createGroupForm.get('groupName')?.hasError('minlength')">
            Group name must be at least 2 characters
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Select Departments</mat-label>
          <mat-select formControlName="departmentIds" multiple>
            <mat-option *ngIf="departments.length === 0" disabled>
              No departments available
            </mat-option>
            <mat-option *ngFor="let dept of departments" [value]="dept.id">
              {{dept.name}} <span *ngIf="dept.code">({{dept.code}})</span>
            </mat-option>
          </mat-select>
          <mat-hint *ngIf="departments.length > 0">Select departments to include in this group</mat-hint>
          <mat-hint *ngIf="departments.length === 0">No departments available to select</mat-hint>
          <mat-error *ngIf="createGroupForm.get('departmentIds')?.hasError('required')">
            At least one department must be selected
          </mat-error>
        </mat-form-field>
      </form>
    </div>

    <div class="dialog-actions">
      <button mat-stroked-button
              (click)="isEditingGroup ? closeEditGroupDialog() : closeCreateGroupDialog()"
              [disabled]="isCreatingGroup || isLoading">
        Cancel
      </button>
      <button *ngIf="isEditingGroup"
              mat-stroked-button
              color="warn"
              (click)="deleteCurrentEditingGroup()"
              [disabled]="isCreatingGroup || isLoading"
              style="margin-right: auto;">
        <mat-icon>delete</mat-icon>
        Delete Group
      </button>
      <button mat-raised-button color="primary"
              (click)="isEditingGroup ? updateDepartmentGroup() : createDepartmentGroup()"
              [disabled]="isCreatingGroup || isLoading || createGroupForm.invalid">
        <mat-spinner *ngIf="isCreatingGroup" diameter="20" class="button-spinner"></mat-spinner>
        <mat-icon *ngIf="!isCreatingGroup">save</mat-icon>
        {{ isCreatingGroup ? (isEditingGroup ? 'Updating...' : 'Creating...') : (isEditingGroup ? 'Update Group' : 'Create Group') }}
      </button>
    </div>
  </div>
</div>
