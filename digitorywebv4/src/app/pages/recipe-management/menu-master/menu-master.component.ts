import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, HostListener, Inject, QueryList, Renderer2, TemplateRef, ViewChild, ViewChildren, ViewEncapsulation,} from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatButtonModule } from '@angular/material/button';
import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';
import { MatAccordion, MatExpansionModule, MatExpansionPanel,} from '@angular/material/expansion';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSelect, MatSelectModule } from '@angular/material/select';
import { FormBuilder, FormControl, FormGroup, Validators, FormArray,} from '@angular/forms';
import { ReactiveFormsModule } from '@angular/forms';
import { ShareDataService } from 'src/app/services/share-data.service';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { InventoryService } from 'src/app/services/inventory.service';
import { AuthService } from 'src/app/services/auth.service';
import { MatRadioModule } from '@angular/material/radio';
import { MatStepperModule } from '@angular/material/stepper';
import { DomSanitizer } from '@angular/platform-browser';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatSlideToggleModule, _MatSlideToggleRequiredValidatorModule,} from '@angular/material/slide-toggle';
import { FormsModule } from '@angular/forms';
import { GradientCardComponent } from 'src/app/components/shared/gradient-card/gradient-card.component';
import { AngularEditorConfig, AngularEditorModule,} from '@kolkov/angular-editor';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MenuMappingComponent } from '../menu-mapping/menu-mapping.component';
import { Observable, ReplaySubject, Subject, first, map, startWith, takeUntil,} from 'rxjs';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import { NotificationService } from 'src/app/services/notification.service';
import { TimeLineComponent } from 'src/app/components/time-line/time-line.component';
import { AvatarComponent } from 'src/app/components/avatar/avatar.component';
import { MatTabGroup, MatTabsModule } from '@angular/material/tabs';
import { NgxSkeletonLoaderModule } from 'ngx-skeleton-loader';
import { MasterDataService } from 'src/app/services/master-data.service';
import { SelectionModel } from '@angular/cdk/collections';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatListModule } from '@angular/material/list';
import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { SessionCacheService } from 'src/app/services/session-cache.service';
import { A11yModule } from '@angular/cdk/a11y';
import { RecipeLlmComponent } from '../recipe-llm/recipe-llm.component';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import {MatChipInputEvent, MatChipsModule} from '@angular/material/chips';
import { EmptyStateComponent } from 'src/app/components/empty-state/empty-state.component';
@Component({
  selector: 'app-menu-master',
  standalone: true,
  imports: [
    CommonModule,
    AngularEditorModule,
    RecipeLlmComponent,
    NgxSkeletonLoaderModule,
    MatDialogModule,
    MatRadioModule,
    MatStepperModule,
    MatIconModule,
    MatSelectModule,
    MatTableModule,
    MatTooltipModule,
    MatExpansionModule,
    MatButtonToggleModule,
    AvatarComponent,
    MatFormFieldModule,
    ReactiveFormsModule,
    MatInputModule,
    MatCardModule,
    MatDividerModule,
    TimeLineComponent,
    MatAutocompleteModule,
    MatButtonModule,
    MatSlideToggleModule,
    FormsModule,
    GradientCardComponent,
    NgxMatSelectSearchModule,
    MatTabsModule,
    MatCheckboxModule,
    MatChipsModule,
    MatSidenavModule,
    MatListModule,
    A11yModule,
    EmptyStateComponent
  ],
  templateUrl: './menu-master.component.html',
  styleUrls: ['./menu-master.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  // encapsulation: ViewEncapsulation.None,
  encapsulation: ViewEncapsulation.Emulated
})
export class MenuMasterComponent {

  minWidth: string = '83vw';
  minHeight: string = '87vh';
  checkWidth: any = 1200;
  ingredientData: any[];
  filtered: any;
  searchTerm: any;
  discontinuedLocations: any;
  groupData: any;
  opConfigData: any;
  resKeys: any[] = [];
  subRecData: any[];
  dialogLocation: any;
  enableParty: boolean = false;
  checkNavigation: boolean;
  // CostData: { "cost of production": number; "Preparation Weight": number; "selling price": number; "Serving size": string; }[];

  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    const width = event.target.innerWidth;
    this.checkWidth = event.target.innerWidth
    if (width <= 480) {
      this.minWidth = '100vw';
      this.minHeight = '100vh';
    } else if (width <= 768) {
      this.minWidth = '95vw';
      this.minHeight = '95vh';
    } else if (width <= 1200) {
      this.minWidth = '90vw';
      this.minHeight = '90vh';
    } else {
      this.minWidth = '83vw';
      this.minHeight = '87vh';
    }
  }

  @ViewChildren('divs') divs: QueryList<ElementRef>;
  @ViewChildren('outsideDivs') outsideDivs: QueryList<ElementRef>;
  uom = ['KG', 'Litre', 'ML', 'NOS', 'GM'];
  categories: any[] = [];
  catBank: Observable<string[]>;
  subCategories: any[] = [];
  servingSizes: any[] = [];
  preparedAt: any[] = [];
  usedOutlet: any[] = [];
  dialogRef: MatDialogRef<any>;
  // workAreas: any[] = [];
  ingredientList: any[] = [];
  @ViewChild('fileInput') el: ElementRef;
  editFile: boolean = true;
  removeUpload: boolean = false;
  modified = ['Yes', 'No'];
  discontinued = ['yes', 'no'];
  closingUOM = ['KG', 'Litre', 'ML', 'NOS', 'GM'];
  ingredientClosingUOM = ['GM', 'ML', 'NOS', 'PORTION'];
  dataSource: MatTableDataSource<any> = new MatTableDataSource<any>();
  defaultDataSource: MatTableDataSource<any> = new MatTableDataSource<any>();
  costDataSource: MatTableDataSource<any> = new MatTableDataSource<any>();
  isDragging = false;
  question = 'Would you like to add "';
  editorConfig: AngularEditorConfig = {
    editable: true,
    spellcheck: true,
    height: '13.625rem',
    minHeight: '0',
    maxHeight: 'auto',
    width: 'auto',
    minWidth: '0',
    translate: 'yes',
    enableToolbar: true,
    showToolbar: true,
    placeholder: 'Enter text here...',
    defaultParagraphSeparator: '',
    defaultFontName: '',
    defaultFontSize: '',
    uploadWithCredentials: false,
    sanitize: false,
    toolbarPosition: 'top',
    toolbarHiddenButtons: [
      ['bold', 'italic'],
      ['fontSize', 'insertImage'],
    ],
  };
  getData: any[];
  data: any;
  editable = false;
  displayedColumns: string[];
  costDisplayedColumns = [ "servingSize", "preparationWeight","costOfProduction","sellingPrice"];
  enableUpdate: boolean = false;
  @ViewChild('addSteps') addSteps: TemplateRef<any>;
  @ViewChild('createIngredient') createIngredient: TemplateRef<any>;
  @ViewChild('ingredientSelect') ingredientSelect: TemplateRef<any>;
  ngxEditorValue = new FormControl('');
  user: any;
  checkSelection: boolean;
  recipeCode: any;
  baseData: any;
  currentStep = 0;
  selectedIndex = 0;
  tempRecipeForm: FormGroup;
  ingredientsForm: FormGroup<{
    ingredientName: FormControl<string>;
    ingredientCode: FormControl<string>;
    category: FormControl<string>;
    subCategory: FormControl<string>;
    modifierName: FormControl<string>;
    portionCount: FormControl<number>;
    isModifier: FormControl<string>;
    ConsumptionUOM: FormControl<string>;
    defaultUOM: FormControl<string>;
    InitialWeight: FormControl<number>;
    weightInUse: FormControl<number>;
    Yield: FormControl<number>;
    rate: FormControl<number>;
    finalRate: FormControl<number>;
    Discontinued: FormControl<string>;
    row_uuid: FormControl<any>;
  }>;

  addStepsDialogRef: MatDialogRef<unknown, any>;
  closeIngrediantRef: MatDialogRef<unknown, any>;
  closeCreateIngrediantRef: MatDialogRef<unknown, any>;
  @ViewChild('timepicker') timepicker: any;
  index: any = '';
  isEdit: boolean = false;
  isCreate: boolean = true;
  createRecipeForm: FormGroup;
  imageUrl: string | ArrayBuffer;
  isReadOnly: boolean = true;
  selectedPreparation: string = 'minutes';
  selectedCooking: string = 'minutes';
  tempData = '5kg';
  total = 5000;
  itemNameControl = new FormControl('');
  itemNameControlIngredients = new FormControl('');
  updatedInvData: any;
  public nameOptionsBank: any[] = [];
  public nameOptionsFilterCtrl: FormControl = new FormControl();
  public nameOptions: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public filteredOptions: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public recipeBank: any[] = [];
  public recipeOptionsFilterCtrl: FormControl = new FormControl();
  public recipeOptions: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  protected _onDestroy = new Subject<void>();
  BranchData: any;
  message: string = 'created successfully';
  isEnableName: boolean = false;
  isEnableCode: boolean = false;
  private regex: RegExp = new RegExp(/^\d*\.?\d{0,2}$/g);
  private specialKeys: Array<string> = [
    'Backspace',
    'Tab',
    'End',
    'Home',
    'ArrowLeft',
    'ArrowRight',
    'Del',
    'Delete',
  ];
  invItems: any;
  access: boolean = false;
  isDuplicate: boolean;
  costDialogkey: boolean = false;
  itemNameOptions: Observable<any[]>;
  updateBtnActive: boolean = false;
  questionIngredients;
  itemList: any[];
  showMatOpt: boolean = false;
  isIngredientsDataReady: boolean = false;
  public usedWorkAreaBank: any[] = [];
  public usedWorkAreaFilterCtrl: FormControl = new FormControl();
  public workAreas: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  menuCost: any = 0;
  profitMargin: number;
  selection = new SelectionModel<any>(true, []);
  profitPercentage: number;
  costPercentage: number;
  ratio: number = 1;
  // defaultDataSource: any;
  public ServingBank: any[] = [];
  public servingFilterCtrl: FormControl = new FormControl();
  public servingData: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public servingSalesBank: any[] = [];
  public servingSalesFilterCtrl: FormControl = new FormControl();
  public servingSalesData: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public resBank: any[] = [];
  public resFilterCtrl: FormControl = new FormControl();
  public resData: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public outletBank: any[] = [];
  public outletFilterCtrl: FormControl = new FormControl();
  public outletData: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public prepareBank: any[] = [];
  public prepareFilterCtrl: FormControl = new FormControl();
  public prepareData: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public modifierBank: any[] = [];
  public modifierFilterCtrl: FormControl = new FormControl();
  public modifierData: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public closingUomBank: any[] = [];
  public closingUomFilterCtrl: FormControl = new FormControl();
  public closingUomData: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public VendorBank: any[] = [];
  public vendorFilterCtrl: FormControl = new FormControl();
  public vendorsBanks: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  width: number;
  height: number;
  cardWidth: number = 300; // Initial width
  cardHeight: number = 200; // Initial height
  @ViewChild('resizableDiv', { static: false }) resizableDiv: ElementRef;
  modifierItems: any[];
  expandedPanel: any;
  // @ViewChild('panel') panel: MatExpansionPanel;
  @ViewChild(MatAccordion) accordion: MatAccordion;
  multi = true;
  catAndSubCat = {};
  newCategory: any;
  newSubCategory: any;
  subCatBank: Observable<string[]>;
  isDone: boolean = false;
  isCreateMode: boolean = false;
  priceList: any;
  modifiers: any;
  modifiersList: any[] = [];
  sellingPriceArray: any[] = [];
  posServingSizes: any[] = [];
  overAllSellingPrice: any;
  customSellingPrice: number;
  @ViewChild('openSalesDialog') openSalesDialog: TemplateRef<any>;
  @ViewChild('openPortionDialog') openPortionDialog: TemplateRef<any>;
  @ViewChild('openRenameDialog') openRenameDialog: TemplateRef<any>;
  @ViewChild('openCostDialog') openCostDialog: TemplateRef<any>;
  editRecipe: boolean;
  inventoryItem: boolean = false;
  sellingPriceControl = new FormControl();

  loadMmBtn: boolean = true;
  loadMrBtn: boolean = true;
  sidenavWidth = 20;
  ngStyle: string;
  opened: boolean = true;
  sidebarOpen: boolean = false;
  isSmallScreen$: Observable<boolean>;
  checkSmallScreen: boolean;
  currentMenuItemCode: any;
  posItems: any[] = [];
  currentItemName: string;
  currentItemCode: string;
  isChecked: boolean = false;
  public globalLocation: FormControl = new FormControl();
  public sellingLocation: FormControl = new FormControl();
  AccessibleUOM: any[] = [];
  currentInvItem: any = {};
  @ViewChild('openDeleteDialog') openDeleteDialog: TemplateRef<any>;
  updatedDatas: any[] = [];
  removedItem: any = [];
  tempIngrData: any[];
  showDeleteItems: boolean = false;
  discData: any=[];
  showWeightError: boolean = false;
  showYieldError: boolean = false;
  costData = [
    {
        "cost of production" : 10,
        "Preparation Weight" : 30,
        "selling price" : 100,
        "Serving size" : "30 ml"
    },
    {
        "cost of production" : 10,
        "Preparation Weight" : 60,
        "selling price" : 200,
        "Serving size" : "60 ml"
    },
    {
        "cost of production" : 10,
        "Preparation Weight" : 90,
        "selling price" : 300,
        "Serving size" : "90 ml"
    }
    ]
  @ViewChild('demo2tab') demo2tab: MatTabGroup;
  logo: any;
  status: string;
  modifierOptions: Observable<string[]>;
  ingredientNamesOptions: Observable<string[]>;
  globalLocationOptions: Observable<string[]>;
  @ViewChild('discontinuedSelectDialog') discontinuedSelectDialog: TemplateRef<any>;
  @ViewChild('setLocationDialog') setLocationDialog: TemplateRef<any>;
  selectedDropDown: any;
  selectedData: string;
  discontinuedOutletData: any = [];
  defaultOutletData: any = [];
  discontinuedUsedWorkAreaData: any = [];
  defaultUsedWorkAreaData: any = [];
  discontinuedPreLocData: any = [];
  defaultPreLocData: any = [];



  weightInUseGreaterThanZero(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const weightInUse = control.value;
      if (weightInUse <= 0) {
        return { weightInUseInvalid: true };
      }
      return null;
    };
  }

  yieldGreaterThanZero(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const Yield = control.value;
      if (Yield <= 0) {
        return { yieldInvalid: true };
      }
      return null;
    };
  }

  isOptionAccessible(option: string): boolean {
    return this.AccessibleUOM.includes(option);
  }

  uomChange(el){
    this.ingredientsForm.get('portionCount').setValue(1)
    this.getPortionWeightForSubRecipe()
  }

  getPortionWeightForSubRecipe(){
    let requiredItem = this.invItems.find(
      (item) => item.itemCode == this.ingredientsForm.value.ingredientCode
    );
    let portionWeight = this.notify.truncateAndFloor(requiredItem['portionWeight']) * this.ingredientsForm.value.portionCount
    this.ingredientsForm.get('weightInUse').setValue(portionWeight);
    this.setInitialWeight() ;
    this.makeTotalPrice() ;
  }

  convertPortionToUOM(el){
    this.getPortionWeightForSubRecipe() ;
    this.cd.detectChanges() ;
  }

  constructor(
    public formBuilder: FormBuilder,
    private cd: ChangeDetectorRef,
    private sharedData: ShareDataService,
    private cache: SessionCacheService,
    private router: Router,
    private activateRoute: ActivatedRoute,
    private api: InventoryService,
    private auth: AuthService,
    private sanitizer: DomSanitizer,
    public dialog: MatDialog,
    private cdRef: ChangeDetectorRef,
    public notify: NotificationService,
    private masterDataService: MasterDataService,
    @Inject(MAT_DIALOG_DATA) public dialogData: any,
    private ElementRef: ElementRef,
    private renderer: Renderer2,
    private breakpointObserver: BreakpointObserver,
    // private selectedBranchesService: ShareDataService,

  ) {
    this.user = this.auth.getCurrentUser();
    this.sellingPriceControl.setValue(0);
    this.isSmallScreen$ = this.breakpointObserver
      .observe([Breakpoints.Small, Breakpoints.XSmall])
      .pipe(map((result) => result.matches));
    this.resBank = this.user.restaurantAccess;
    let uniqueArray = [...new Set(this.resBank)];
    this.resData.next(uniqueArray.slice());
    this.resFilterCtrl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe(() => {
        this.resServing(this.resBank, this.resFilterCtrl, this.resData);
      });

    this.isDuplicate = this.dialogData.key;
    this.costDialogkey = this.dialogData.costDialogkey;

    this.sharedData.getRecipeNames.pipe(first()).subscribe((obj) => {
      this.itemList = obj;
      this.posItems = this.itemList.filter((item)=> item['category'] === 'POS ONLY')
      this.recipeBank = this.posItems
      this.recipeOptions.next(this.recipeBank.slice());
      this.recipeOptionsFilterCtrl.valueChanges
        .pipe(takeUntil(this._onDestroy))
        .subscribe(() => {
          this.Filter(
            this.recipeBank,
            this.recipeOptionsFilterCtrl,
            this.recipeOptions
          );
        });
      this.itemNameOptions = this.itemNameControl.valueChanges.pipe(
        startWith(''),
        map((value) => this._filter(value || '', this.itemList))
      );
    });

    this.VendorBank = this.user.restaurantAccess.filter(
      (branch) => branch && branch.branchName
    );
    // this.globalLocationOptions = this.globalLocation.valueChanges.pipe(startWith(''), map(value => this._filterSearchObj((value || ''), this.user.restaurantAccess)));
    this.vendorsBanks.next(this.VendorBank.slice());
    this.vendorFilterCtrl = new FormControl( '',Validators.pattern('[a-zA-Z0-9\\s]*'));
    this.vendorFilterCtrl.valueChanges
      .pipe(
        debounceTime(300),
        distinctUntilChanged()
      )
      .subscribe((newValue) => {
        this.vendorfilterBanks(newValue);
      });
    // let val = this.user.restaurantAccess.find(val => val.branchName == entry[0].branchName)
        this.globalLocation.setValue(this.user.restaurantAccess[0]);

   this.closingUomBank = this.closingUOM;
    this.closingUomData.next(this.closingUomBank.slice());
    this.closingUomFilterCtrl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe(() => {
        this.FilterLocation(
          this.closingUomBank,
          this.closingUomFilterCtrl,
          this.closingUomData
        );
      });

    this.createRecipeForm = this.formBuilder.group({
      recipeName: ['', Validators.required],
      recipeCode: ['', Validators.required],
      subCategory: ['', Validators.required],
      category: ['', Validators.required],
      weight: [0, Validators.required],
      servingSize: [0, Validators.required],
      // preparationSize: ['full-default', Validators.required],
      portion: [1, Validators.required],
      PerPortionWeight: [0, Validators.required],
      rate: [0, Validators.required],
      preparedAt: ['', Validators.required],
      usedOutlet: ['', Validators.required],
      usedWorkArea: ['', Validators.required],
      closingUOM: [0,],
      totalTime: [0, Validators.required],
      // preparationTime: [0, Validators.required],
      // cookTime: [0, Validators.required],
      Discontinued: ['no', Validators.required],
      // ingredients: this.formBuilder.array([]),
      instructions: this.formBuilder.array([]),
      image: [''],
      restaurant: [''],
      overallServingSize: [''],
      row_uuid: [''],
    });

    this.ingredientsForm = this.formBuilder.group({
      ingredientName: ['', Validators.required],
      ingredientCode: ['', Validators.required],
      category: ['', Validators.required],
      subCategory: ['', Validators.required],
      modifierName: ['N/A', Validators.required],
      isModifier: ['No', Validators.required],
      ConsumptionUOM: ['', Validators.required],
      defaultUOM: ['', Validators.required],
      InitialWeight: [0, Validators.required],
      portionCount: [0],
      // weightInUse: [0, this.validateWeightGreaterThanZero()],
      weightInUse: [0, [Validators.required, this.validateWeightGreaterThanZero]],
      // Yield: [0, this.yieldGreaterThanZero()],
      Yield: [0, [Validators.required, this.validateYieldGreaterThanZero]],
      rate: [0, Validators.required],
      finalRate: [0, Validators.required],
      Discontinued: ['no', Validators.required],
      row_uuid: [''],
    });

    this.tempRecipeForm = this.formBuilder.group({
      recipeName: [null],
      recipeCode: [{ value: null, disabled: true }]
    });

    // this.isSmallScreen$.subscribe((isSmallScreen) => {
    //   this.checkSmallScreen = isSmallScreen;
      // if (this.checkSmallScreen === true) {
      //   this.displayedColumns = [
        // 'select',
        // 'position',
        // 'ingredientNames',
        // 'actions',
        // 'ingredientCode',
        // 'uom',
        // 'modifierName',
        // 'initialWeight',
        // 'yield',
        // 'weightInUse',
        // 'prices',
        // 'totalPrices',
        // 'discontinued',
        //   ];
      // } else {
        this.displayedColumns = [
          'select',
          'position',
          'ingredientName',
          'uom',
          'weightInUse',
          'yield',
          'initialWeight',
          'price',
          'totalPrice',
        ];
      // }
    // });



    this.sharedData.getItemNames.subscribe((obj) => {
      this.ingredientList = obj.itemNames;
      this.updatedInvData = obj.updatedInvData;
    });
    if(this.dialogData.elements){
      this.posServingSizes = this.dialogData.elements.hasOwnProperty(
        'servingSize'
      )
        ? this.dialogData.elements['servingSize'].split(',')
        : [];
    }

    this.sharedData.getViewRecipe.subscribe(obj => {
      if(Object.keys(obj).length > 0){
        this.checkNavigation = obj.createParty
      }
    })

    this.data = this.sharedData.getData();
    this.baseData = this.sharedData.getBaseData().value;
    // this.priceList = this.sharedData.getPriceList().value;
    this.modifiers = this.sharedData.getModifiers().value;
    this.modifiersList = this.modifiers.map((item) => item.name);
    this.modifierBank = this.modifiersList;
    let uniquePrepareArray = [...new Set(this.modifierBank)];
    this.modifierOptions = this.ingredientsForm.get('modifierName').valueChanges.pipe(startWith(''), map(value => this._filterSearch((value || ''), uniquePrepareArray)));
    this.modifierData.next(uniquePrepareArray.slice());
    this.modifierFilterCtrl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe(() => {
        this.FilterModifier(
          this.modifierBank,
          this.modifierFilterCtrl,
          this.modifierData
        );
      });
    this.baseData['servingsize conversion'] = this.baseData[
      'servingsize conversion'
    ].sort((a, b) => {
      const servingA = a['Serving Size'];
      const servingB = b['Serving Size'];
      const indexA = this.posServingSizes.indexOf(servingA);
      const indexB = this.posServingSizes.indexOf(servingB);
      if (indexA !== -1 && indexB !== -1) {
        return indexA - indexB;
      }
      if (indexA !== -1) return -1;
      if (indexB !== -1) return 1;
      return 0;
    });
    this.servingSizes = this.baseData['servingsize conversion'];
    this.ServingBank = this.servingSizes;
    let uniqueData = [...new Set(this.ServingBank)];
    this.servingData.next(uniqueData.slice());
    this.servingFilterCtrl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe(() => {
        this.FilterServing(
          this.ServingBank,
          this.servingFilterCtrl,
          this.servingData
        );
      });
  }

  protected _filterSearch(value: string, input: string[] ): string[] {
    let filterValue = value.toLowerCase();
      this.filtered = input.filter(option => option.toLowerCase().includes(filterValue));
      if (this.filtered.length == 0) {
        this.filtered = ['No Item Found'];
      }
    return this.filtered
  }

  protected _filterSearchObj(value: string, input: string[] ): string[] {
    let filterValue = value.toLowerCase();
      this.filtered = input.filter(option => option.toLowerCase().includes(filterValue));
      if (this.filtered.length == 0) {
        this.filtered = [{ branchName : 'No Item Found'}];
      }
    return this.filtered
  }

  protected FilterModifier(bank, form, data) {
    if (!bank) {
      return;
    }
    let search = form.value;
    if (!search) {
      data.next(bank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    data.next(bank.filter((data) => data.toLowerCase().indexOf(search) > -1));
  }

  ngOnInit(): void {
    this.costDataSource.data = this.costData
    this.createRecipeForm.patchValue({ overallServingSize: 'full-default' });
    this.createRecipeForm.patchValue({
      restaurant: this.user.restaurantAccess[0],
    });
    this.newCategory = this.baseData['menu master'].map((cat) =>
      cat.category?.toUpperCase()
    );
    if (this.dialogData != null && this.dialogData.key == false) {
      if(this.dialogData.createNew == true){
        this.createRecipeForm.patchValue({
          recipeName: this.dialogData.recipeName,
        })
        this.dataSource.data = [];
        this.isIngredientsDataReady = true;
        this.itemNameControl.setValue(
          this.cleanSpaces(this.createRecipeForm.value.recipeName).toUpperCase()
        )
        this.addOption(null)
      }else{
        this.isCreate = false;
        this.createRecipeForm.patchValue({
          recipeName: this.dialogData.elements.menuItemName,
          recipeCode: this.dialogData.elements.menuItemCode,
        })
      }
    }
    if(this.costDialogkey == true){
      this.isCreate = false;
    }

    let tenantId = this.user.tenantId
    this.api.getRolesListDiscontinuedLocations(tenantId)
    .subscribe((res) => {
      if(res['result'] == 'success' && res['discontinuedLocations']){
        this.discontinuedLocations = res['discontinuedLocations'];
      }
      this.cd.detectChanges();
      this.getLocationCall();
      this.getCategories();
      this.getMenuRecipes();
      this.readIPConfig();
    });
  }

  protected vendorfilterBanks(newValue?: unknown) {
    if (!this.VendorBank) {
      return;
    }
    let search = this.vendorFilterCtrl.value;
    if (!search) {
      this.vendorsBanks.next(this.VendorBank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    if (!search.includes(' ')) {
      this.vendorsBanks.next(
        this.VendorBank.filter((branch) =>
          branch.branchName.toLowerCase().replace(/\s/g, '').includes(search)
        )
      );
    } else {
      const searchTerms = search
        .split(' ')
        .filter((term) => term.trim() !== '');
      this.vendorsBanks.next(
        this.VendorBank.filter((branch) => {
          const branchNameLowerCase = branch.branchName.toLowerCase();
          return searchTerms.every((term) =>
            branchNameLowerCase.includes(term)
          );
        })
      );
    }
  }

  protected FilterServing(bank, form, data) {
    if (!bank) {
      return;
    }
    let search = form.value;
    if (!search) {
      data.next(bank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    data.next(
      bank.filter(
        (data) => data['Serving Size'].toLowerCase().indexOf(search) > -1
      )
    );
  }

  protected resServing(bank, form, data) {
    if (!bank) {
      return;
    }
    let search = form.value;
    if (!search) {
      data.next(bank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    data.next(
      bank.filter((data) => data.branchName.toLowerCase().indexOf(search) > -1)
    );
  }

  bindData(data) {
    this.isCreate = false;
    let preparedAt = data.preparedAt ? data.preparedAt.split(',') : [];
    let entry = this.BranchData.filter((branch) =>
      preparedAt.includes(branch.abbreviatedRestaurantId)
    );
    const restaurantIds = entry.map((branch) => branch.branchName);
    let desiredIds = data.usedAtOutlet.split(',');
    let filteredOutletBranches = this.BranchData.filter((branch) =>
      desiredIds.includes(branch.abbreviatedRestaurantId)
    ).map((branch) => branch.branchName);
    this.locationChange(filteredOutletBranches);
    this.currentMenuItemCode = data.menuItemCode;

    this.defaultOutletData = filteredOutletBranches
    this.defaultUsedWorkAreaData = data.usedInWorkArea
    this.defaultPreLocData = restaurantIds
    if((this.discontinuedLocations && this.discontinuedLocations != undefined) && this.discontinuedLocations.menuRecipeLocations){
      this.discontinuedPreLocData.push(...this.discontinuedLocations.menuRecipeLocations.procuredAtDiscontinued)
      this.discontinuedOutletData.push(...this.discontinuedLocations.menuRecipeLocations.outLetDiscontinued)
      this.discontinuedUsedWorkAreaData.push(...this.discontinuedLocations.menuRecipeLocations.issuedToDiscontinued)
    }

    this.createRecipeForm.patchValue({
      recipeName: data.menuItemName,
      recipeCode: data.menuItemCode,
      category: data.category,
      subCategory: data.subCategory,
      preparedAt: restaurantIds,
      usedOutlet: filteredOutletBranches,
      usedWorkArea: data.usedInWorkArea.split(','),
      servingSize: data.servingSize.split(','),
      closingUOM: data.closingUOM,
      portion: data.hasOwnProperty('portion') && (!['', null].includes(data.portion)) ? this.notify.truncateAndFloor(data.portion) : 1,
      // Discontinued: data.Discontinued ?? data.discontinued,
      Discontinued: ['no','NO' ,'No', 'N', null,'',undefined].includes(data['Discontinued']) ? 'no' : 'yes',
      row_uuid: data.row_uuid,
    });
    // ['', null, undefined].includes(this.createRecipeForm.get('Discontinued').value)
    // ? this.createRecipeForm.get('Discontinued').setValue('no')
    // : undefined;
    if (
      typeof data.cookTime === 'number' &&
      typeof data.preparationTime === 'number'
    ) {
      this.createRecipeForm.patchValue({
        cookTime: data.cookTime,
        preparationTime: data.preparationTime,
      });
    } else {
      let cookingTime = data.hasOwnProperty('cookTime')
        ? data.cookTime.split(' ')[0]
        : '00';
      let preparationTime = data.hasOwnProperty('preparationTime')
        ? data.preparationTime.split(' ')[0]
        : '00';
      this.createRecipeForm.patchValue({
        cookTime: cookingTime,
        preparationTime: preparationTime,
      });

      this.selectedCooking = data.hasOwnProperty('cookTime')
        ? data.cookTime.split(' ')[1]
        : '';
      this.selectedPreparation = data.hasOwnProperty('preparationTime')
        ? data.preparationTime.split(' ')[1]
        : '';
    }
    this.loadMmBtn = false;
    let val = this.user.restaurantAccess.find(val => val.restaurantIdOld == entry[0].restaurantIdOld)
    this.globalLocation.setValue(val);
    this.cd.detectChanges();
  }

  create(tabGroup: MatTabGroup) {
    if (this.createRecipeForm.invalid) {
      tabGroup.selectedIndex = 1;
      this.createRecipeForm.markAllAsTouched();
      this.notify.snackBarShowWarning('Please fill out all required fields');
      this.cd.detectChanges();
    } else {
    this.createRecipeForm.get('portion').value < 1 ? (this.createRecipeForm.patchValue({ portion: 1 })) : undefined ;
    if (this.createRecipeForm.invalid) {
      this.createRecipeForm.markAllAsTouched();
      this.notify.snackBarShowWarning('Please fill out all required fields');
      this.cd.detectChanges();
    } else if (this.dataSource.data.length === 0) {
      tabGroup.selectedIndex = 0;
      this.createRecipeForm.markAllAsTouched();
      this.notify.snackBarShowWarning('Please add ingredients!');
      this.cd.detectChanges();
    } else {
      let portion = this.createRecipeForm.get('portion').value
      this.isDone = true;
      let tempObj = {};
      let data = this.baseData;
      let updatedData = this.convertMenuKeys();
      updatedData['modified'] = 'yes';
      updatedData['itemType'] = 'Menu';
      updatedData['preparedAt'] = this.getPreparedLocations(
        this.createRecipeForm.value.preparedAt,
        this.BranchData
      ).join(',');
      updatedData['usedAtOutlet'] = this.getPreparedLocations(
        this.createRecipeForm.value.usedOutlet,
        this.BranchData
      ).join(',');
      updatedData['servingSize'] = updatedData['servingSize'].join(',');
      updatedData['usedInWorkArea'] = updatedData['usedInWorkArea'].join(',');
      updatedData['rate'] = (this.getTotal('finalRate')/portion);
      updatedData['weight'] = (this.getTotal('InitialWeight')/portion);
      tempObj['menu master'] = data['menu master'];
      if (!tempObj['menu master']) {
        tempObj['menu master'] = [];
      }
      tempObj['menu master'].unshift(...[updatedData]);
      tempObj['menu master'] = tempObj['menu master'].filter(
        (item) => item.modified === 'yes'
      );
      this.dataSource.data.forEach((item) => {
        item.ConsumptionUOM = item.defaultUOM ;
        item.menuItemCode = this.createRecipeForm.value.recipeCode;
        item.menuItemName = this.createRecipeForm.value.recipeName;
        item.InitialWeight = item.InitialWeight * (1/portion);
        item.weightInUse = item.weightInUse * (1/portion);
      });
      tempObj['menu recipes'] = this.dataSource.data;
      let obj = {};
      obj['tenantId'] = this.user.tenantId;
      obj['userEmail'] = this.user.email;
      obj['data'] = tempObj;
      obj['type'] = 'recipe';
      obj['category'] = 'menu'
      this.api.updateData(obj).subscribe({
        next: (res) => {
          if (res['success']) {
            this.notify.snackBarShowSuccess('Created successfully!');
            this.close();
          } else {
            this.isDone = false;
            this.notify.snackBarShowError('Something went wrong!');
          }
        },
        error: (err) => {
          console.log(err);
        },
      });
      this.nameOptionsFilterCtrl.reset();
    }
  }
  }

  cancelEdit() {
    this.editable = false;
  }

  update(tabGroup: MatTabGroup) {
    if (this.createRecipeForm.get('overallServingSize').value != 'full-default'){
      this.servingSizeChange('full-default')
    }
    // this.deleteDataFormDB()
    this.createRecipeForm.get('portion').value < 1 ? (this.createRecipeForm.patchValue({ portion: 1 })) : undefined ;
    let entry = this.BranchData.filter((branch) =>
      this.createRecipeForm.value.preparedAt.includes(branch.branchName)
    );
    const filteredIds = this.BranchData.filter((el) =>
      this.createRecipeForm.value.usedOutlet.some((branchName) =>
        el.branchName.includes(branchName)
      )
    ).map((el) => el.abbreviatedRestaurantId);
    let usedOutletSplit = filteredIds.join(',');

    if (this.createRecipeForm.invalid) {
      this.createRecipeForm.markAllAsTouched();
      tabGroup.selectedIndex = 1;
      this.notify.snackBarShowWarning('Please fill out all required fields');
      this.cd.detectChanges();
    } else {
      this.isDone = true;
      let portion = this.createRecipeForm.get('portion').value
      if (Object.keys(this.baseData).length > 0) {
        this.setDiscontinuedDataInRolopos();
        let updatedData = this.convertMenuKeys();
        updatedData['modified'] = 'yes';
        updatedData['itemType'] = 'Menu';
        updatedData['rate'] = (this.getTotal('finalRate')/portion);
        updatedData['weight'] = (this.getTotal('InitialWeight')/portion);
        updatedData['preparedAt'] = entry[0]['abbreviatedRestaurantId'];
        updatedData['usedAtOutlet'] = usedOutletSplit;
        updatedData['servingSize'] = updatedData['servingSize'].join(',');
        updatedData['usedInWorkArea'] = updatedData['usedInWorkArea'].join(',');
        updatedData['preparedAtDiscontinued'] = this.discontinuedPreLocData.length > 0 ? this.discontinuedPreLocData.join(',') : '';
        updatedData['usedAtOutletDiscontinued'] = this.discontinuedOutletData.length > 0 ? this.discontinuedOutletData.join(',') : '';
        if(this.discontinuedUsedWorkAreaData.length > 0){
          const workAreas = this.discontinuedUsedWorkAreaData.flatMap(item => item.workAreas);
          updatedData['usedInWorkAreaDiscontinued'] = workAreas > 0 ? workAreas.join(',') : '';
        }
        let menuRecipe,menuMaster;
        this.dataSource.data = [...this.dataSource.data, ...this.removedItem, ...this.discData];
        this.dataSource.data.forEach((item) => {
          item.ConsumptionUOM = item.defaultUOM ;
          item.menuItemCode = this.createRecipeForm.value.recipeCode;
          item.menuItemName = this.createRecipeForm.value.recipeName;
          item.InitialWeight = item.InitialWeight * (1/portion);
          item.weightInUse = item.weightInUse * (1/portion);
        });
        menuRecipe = this.dataSource.data;
        this.dataSource.data = this.dataSource.data.filter(item => item.delete !== true);
        this.dataSource.data = this.dataSource.data.filter(item => item.Discontinued !== 'yes');
        menuMaster = this.baseData['menu master'];
        let requiredVendor = menuMaster.find(
          (el) => el.menuItemCode == this.currentMenuItemCode
        );
        let index = menuMaster.indexOf(requiredVendor);
        menuMaster[index] = updatedData;
        menuMaster = menuMaster.filter(
          (item) => item.modified === 'yes'
        );
        let tempObj: { 'menu recipes': any[], 'menu master': any[] } = {
          'menu recipes': menuRecipe,
          'menu master': menuMaster
        };
        let obj = {};
        obj['tenantId'] = this.user.tenantId;
        obj['userEmail'] = this.user.email;
        obj['data'] = tempObj;
        obj['type'] = 'recipe';
        obj['category'] = 'menu'
        this.api
          .updateData(obj)
          .pipe(first())
          .subscribe({
            next: (res) => {
              if (res['success']) {
                this.isDone = false;
                this.close();
                this.updateBaseData(tempObj)
                this.notify.snackBarShowSuccess('Item updated successfully');
                this.close();
              } else {
                this.isDone = false;
              }
              this.cd.detectChanges();
            },
            error: (err) => {
              console.log(err);
            },
          });
      }
    }
  }

  edit() {
    this.editable = true;
    this.enableUpdate = true;
  }

  goBack() {
    this.editable = false;
    this.enableUpdate = false;
    this.data = {};
    this.nameOptionsFilterCtrl.reset();
    this.router.navigate(['/dashboard/recipe']);
  }

  addIngredientForm(createIngredient) {
    this.itemNameControl.reset();
    const ingredient = this.formBuilder.group({
      ingredientName: ['', Validators.required],
      ingredientCode: ['', Validators.required],
      isModified: ['No'],
      modifierName: [],
      uom: ['', Validators.required],
      quantity: ['', Validators.required],
      initialWeight: ['', Validators.required],
      Yield: ['', Validators.required],
      weightInUse: ['', Validators.required],
      price: ['', Validators.required],
      totalPrice: ['', Validators.required],
      Discontinued: ['no', Validators.required],
    });

    let dialogRef = this.dialog.open(createIngredient, {
      maxHeight: '95vh',
    });
    dialogRef.afterClosed().subscribe((result) => {});
    this.closeCreateIngrediantRef = dialogRef;
  }

  closeIngredientDialog() {
    this.closeIngrediantRef.close();
    this.cd.detectChanges();
  }

  addIngredientRow(data: any, action) {
    if (action == 'create') {
      const ingredientGroup = this.formBuilder.group({
        ingredientName: [data.ingredientName, Validators.required],
        ingredientCode: [data.ingredientCode, Validators.required],
        packageQty: [data.packageQty],
        rate: [data.rate],
        initialWeight: [data.initialWeight, Validators.required],
        isModified: [data.isModifier, Validators.required],
        modifierName: [data.modifierName, Validators.required],
        Yield: [data.yield, Validators.required],
        weightInUse: [data.weightInUse, Validators.required],
        uom: [data.uom, Validators.required],
        quantity: [data.quantity, Validators.required],
        price: [data.price || 0],
        totalPrice: [data.totalPrice || 0],
        Discontinued: [data.Discontinued, Validators.required],
      });
    } else {
      const ingredientGroup = this.formBuilder.group({
        ingredientName: [data.ingredientName, Validators.required],
        ingredientCode: [data.ingredientCode, Validators.required],
        packageQty: [data.packageQty],
        rate: [data.rate],
        initialWeight: [data.initialWeight, Validators.required],
        isModified: [data.isModified, Validators.required],
        modifierName: [data.modifierName, Validators.required],
        Yield: [data.yield, Validators.required],
        weightInUse: [data.weightInUse, Validators.required],
        uom: [data.uom, Validators.required],
        quantity: [data.quantity, Validators.required],
        price: [data.price || 0],
        totalPrice: [data.totalPrice || 0],
        Discontinued: [data.Discontinued, Validators.required],
      });
      ingredientGroup['ingredientCode'] = data.ingredientCode;
      ingredientGroup['ingredientName'] = data.ingredientName;
      ingredientGroup['initialWeight'] = data.initialWeight;
      ingredientGroup
        .get('totalPrice')
        .setValue(
          (
            ingredientGroup.value.initialWeight * ingredientGroup.value.price
          )
        );
    }
  }

  get instructionArray(): FormArray {
    return this.createRecipeForm.get('instructions') as FormArray;
  }

  removeInstruction(index: number) {
    const instructionsArray = this.createRecipeForm.get(
      'instructions'
    ) as FormArray;
    instructionsArray.removeAt(index);
  }

  onDragOver(event: DragEvent) {
    event.preventDefault();
    this.isDragging = true;
  }

  onDragLeave(event: DragEvent) {
    event.preventDefault();
    this.isDragging = false;
  }

  onDrop(event: DragEvent) {
    event.preventDefault();
    this.isDragging = false;

    const files = event.dataTransfer?.files;
    if (files && files.length > 0) {
      const file = files[0];
      this.handleFile(file);
    }
  }

  handleFileInput(event: any) {
    const files = event.target.files;
    if (files && files.length > 0) {
      const file = files[0];
      this.handleFile(file);
    }
  }

  handleFile(file: File) {
    if (file) {
      this.imageUrl = URL.createObjectURL(file);
      this.editFile = false;
      this.removeUpload = true;
    }
  }

  ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  generateRecipeCode() {
    this.api.getRecipeCode(this.user.tenantId).subscribe({
      next: (res) => {
        res['success']
          ? (this.recipeCode = res['recipeCode'])
          : this.notify.snackBarShowError('Something Went wrong!');
        this.isCreateMode = true;
        this.createRecipeForm.get('recipeCode').setValue(this.recipeCode);
        this.currentMenuItemCode = this.recipeCode;
      },
      error: (err) => {
        console.log(err);
      },
    });
  }

  getBaseData() {
    this.baseData = this.sharedData.getBaseData().value;
    let obj = {};
    obj['tenantId'] = this.user.tenantId;
    obj['userEmail'] = this.user.email;
    obj['type'] = 'recipe';
    obj['specific'] = 'menu recipes';
    if (this.createRecipeForm.value.recipeCode) {
      obj['itemCode'] = this.createRecipeForm.value.recipeCode;
    }
    let requiredData = this.itemList.find(
      (el) => el.itemCode.toLowerCase() === obj['itemCode'].toLowerCase()
    );
    this.inventoryItem =
      requiredData && requiredData['category'] === 'INVENTORY ONLY'
        ? true
        : (!requiredData ? true : (requiredData['category'] === 'INVENTORY ONLY' ? true : false));
    // requiredData && requiredData['category'] === 'INVENTORY ONLY' ? (this.posItems.unshift(requiredData),this.currentItemName = requiredData['itemName'],this.currentItemCode = requiredData['itemCode']) : undefined ;

    if(requiredData && requiredData['category'] === 'INVENTORY ONLY'){
      this.recipeBank.push(requiredData)
      this.recipeOptions.next(this.recipeBank.slice());
      this.recipeOptionsFilterCtrl.valueChanges
        .pipe(takeUntil(this._onDestroy))
        .subscribe(() => {
          this.Filter(
            this.recipeBank,
            this.recipeOptionsFilterCtrl,
            this.recipeOptions
          );
        });
    }
    requiredData && requiredData['category'] === 'INVENTORY ONLY' ? (this.posItems.unshift(requiredData),this.tempRecipeForm.get('recipeName').patchValue(requiredData['itemName']),this.tempRecipeForm.get('recipeCode').setValue(requiredData['itemCode'])): undefined ;
    this.cd.detectChanges();
    this.api.getPresentData(obj).subscribe({
      next: (res) => {
        if (res['success']) {
          let currentRecipeIngredient = res['data'][0] ?? res['data'];
          let menuRecipe = currentRecipeIngredient.hasOwnProperty('menu recipes') ? currentRecipeIngredient['menu recipes'] : []
          this.baseData['menu recipes'].concat(menuRecipe)
          menuRecipe.sort((a, b) => {
            if (a.modified === 'yes' && b.modified !== 'yes') {
              return -1;
            }
            if (b.modified === 'yes' && a.modified !== 'yes') {
              return 1;
            }
            return 0;
          });
          this.dataSource.data = menuRecipe ;
          this.dataSource.data.forEach((el) => {
            el['Discontinued'] = ['no','NO' ,'No', 'N', null,''].includes(el['Discontinued']) ? 'no' : 'yes'
            let portion = this.createRecipeForm.get('portion').value
            let requiredItem = this.invItems.find(
              (item) => item.itemCode == el['ingredientCode']
            );
            let conversionCoefficient;
            requiredItem.hasOwnProperty('portionWeight') ? this.AccessibleUOM = [el['ConsumptionUOM'],"PORTION"] : undefined ;
            if (requiredItem && el.hasOwnProperty('portionCount') && (!['', null,undefined].includes(el.portionCount)) ){
               el['ConsumptionUOM'] = 'PORTION'
               el['portionCount'] = this.notify.truncateAndFloor(el['portionCount'])
               el['weightInUse'] = this.notify.truncateAndFloor(el['portionCount'] * (requiredItem['portionWeight']/portion)) ;
               el['InitialWeight'] = this.notify.truncateAndFloor(el['weightInUse'] /this.notify.truncateAndFloor( el['Yield']));
            }
            conversionCoefficient = requiredItem
              ? requiredItem['uom'] == 'NOS'
                ? 1
                : 1000
              : 0;
            let rate = requiredItem
            ? ((requiredItem.hasOwnProperty('packageQty') ? requiredItem['packageQty'] : 1) / conversionCoefficient )* requiredItem['withTaxPrice']
            : 0;
            el['rate'] =  this.notify.truncateAndFloor(rate);
            el['defaultUOM'] = el['ConsumptionUOM'] ;
            el['isSubRecipe'] = (requiredItem && requiredItem.hasOwnProperty('ItemType') && requiredItem['ItemType'] === 'SubRecipe') ? true : false ;
            el['InitialWeight'] = this.notify.truncateAndFloor(el['InitialWeight'] * portion);
            el['weightInUse'] = this.notify.truncateAndFloor(el['weightInUse'] * portion) ;
            el['finalRate'] = this.notify.truncateAndFloor(rate * el['InitialWeight']);
            let requiredStatus = this.nameOptionsBank.find(
              (item) => item['itemCode'] === el['ingredientCode']
            );
            el['inventoryStatus'] = requiredStatus
              ? requiredStatus['status']
              : 'discontinued';
          });
          this.selection = new SelectionModel<any>(
            true,
            this.dataSource.data.filter(
              (el) =>
                el['inventoryStatus'] === 'active' &&
                ['no', 'No', 'N', null,''].includes(el['Discontinued'])
            )
          );
          this.tempIngrData = this.dataSource.data
          this.dataSource.data = this.dataSource.data.filter(item =>
            !['yes', 'y'].includes((item.Discontinued || 'no').toLowerCase())
          );
          this.discData = this.tempIngrData.filter(item =>
            ['yes', 'y'].includes((item.Discontinued || 'no').toLowerCase())
          );
          this.isAllSelected();
          this.getPortionWeight();
          this.isIngredientsDataReady = true;
          this.cd.detectChanges();
        } else {
          this.baseData = [];
        }

        if(this.costDialogkey == true){
          this.openPortionData();
          // this.dialogRef = this.dialog.open(this.openCostDialog, {
          //   minWidth: '35vw',
          //   panelClass: 'smallCustomDialog',
          // });
        }
        // this.getMenuCost();
      },
      error: (err) => {
        console.log(err);
      },
    } );
  }

  isDisabled(option: any): boolean {
    let itemNames = this.dataSource.data.map(item => item.ingredientName);
    return itemNames.includes(option.itemName);
  }

  _filter(
    value,
    input,
  ): { category: string; items: string[]; }[] {
    let filterValue = value.toLowerCase();
      let filteredCategories = [];
      input.forEach((item) => {
        let category = item.category.toLowerCase();
        let itemName = item.itemName.toLowerCase();

        if (category.includes(filterValue) || itemName.includes(filterValue)) {
          let existingCategory = filteredCategories.find(
            (cat) => cat.category === category
          );

          if (!existingCategory) {
            existingCategory = { category: category, items: [] };
            filteredCategories.push(existingCategory);
          }

          existingCategory.items.push(itemName);
        }
      });
      return filteredCategories;

  }

  checkItem(event) {
    let invItem = this.sharedData.getDataForFillTheForm(
      event.target.value,
      'menu master'
    );
    if (invItem) {
      this.updateBtnActive = true;
    } else {
      this.updateBtnActive = false;
    }
  }

  get instructions() {
    return this.createRecipeForm.get('instructions') as FormArray;
  }

  removeStep(index: number) {
    this.instructions.removeAt(index);
    this.currentStep = Math.max(0, this.currentStep - 1);
  }

  htmlToPlainText(html) {
    var tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;
    return tempDiv.textContent || tempDiv.innerText || '';
  }

  addStep(addSteps) {
    let dialogRef = this.dialog.open(addSteps, {
      maxHeight: '95vh',
      maxWidth: '50vw',
    });
    this.addStepsDialogRef = dialogRef;
  }

  closeAddStepDialog() {
    this.index = '';
    this.addStepsDialogRef.close();
  }

  close() {
    this.dataSource.data = [];
    this.masterDataService.setNavigation('menu master');
    this.sharedData.setItemType(this.isCreate ? true : this.inventoryItem);
    this.router.navigate(['/dashboard/home']);
    this.dialog.closeAll();
  }

  editFun(element, addIngredients) {
    this.isEdit = false;
    this.ingredientsForm.patchValue({
      ingredientName: element['ingredientName'],
      ingredientCode: element['ingredientCode'],
      category: element['Category'],
      subCategory: element['Sub Category'],
      modifierName: element['modifierName'],
      ConsumptionUOM: element['ConsumptionUOM'],
      InitialWeight: element['InitialWeight'],
      weightInUse: element['weightInUse'],
      Yield: element['Yield'],
      defaultUOM: element['defaultUOM'],
      rate:
        this.notify.truncateAndFloor(element['rate']) ||
        this.notify.truncateAndFloor(element['finalRate']),
      finalRate: this.notify.truncateAndFloor(this.notify.truncateAndFloor(element['finalRate'])),
      Discontinued: ['no','NO' ,'No', 'N', null,''].includes(element['Discontinued']) ? 'no' : 'yes',
      row_uuid: element['row_uuid'],
    });
    if (!element['modifierName']) {
      this.ingredientsForm.patchValue({ modifierName: 'N/A' });
    }

    if (element.hasOwnProperty('portionCount')) {
      this.ingredientsForm.get('portionCount').setValue(element['portionCount']);
    }

    if (!element['isModifier']) {
      this.ingredientsForm.patchValue({ isModifier: 'No' });
    } else if (element['isModifier'] == 'N' || element['isModifier'] == 'No') {
      this.ingredientsForm.patchValue({ isModifier: 'No' });
    } else {
      this.ingredientsForm.patchValue({ isModifier: 'Yes' });
    }

    element['Discontinued'] = ['no','NO' ,'No', 'N', null,''].includes(element['Discontinued']) ? 'no' : 'yes'

    // if (
    //   element['Discontinued'] === 'Y' ||
    //   element['Discontinued'] === 'YES' ||
    //   element['Discontinued'] === 'yes'
    // ) {
    //   this.ingredientsForm.get('Discontinued').setValue('yes');
    // } else if (
    //   element['Discontinued'] === 'N' ||
    //   element['Discontinued'] === 'NO' ||
    //   element['Discontinued'] === 'no'
    // ) {
    //   this.ingredientsForm.get('Discontinued').setValue('no');
    // } else {
    //   this.ingredientsForm.get('Discontinued').setValue('no');
    // }

    let dialogRef = this.dialog.open(addIngredients, {
      maxHeight: '95vh',
      maxWidth: '50vw',
    });
    dialogRef.afterClosed().subscribe((result) => {});
    this.closeIngrediantRef = dialogRef;
    this.loadMrBtn = false;
    // this.ingredients.push(ingredient);
  }

  editExistingSubRecipeRecipe() {
    if (this.ingredientsForm.invalid) {
      this.ingredientsForm.markAllAsTouched();
      this.notify.snackBarShowWarning('Please fill out all required fields');
      this.cd.detectChanges();
    } else {
      let updatedMenuRecipeData = this.convertMenuRecipeKeys();
      updatedMenuRecipeData['modified'] = 'yes';
      let requiredPackage = this.dataSource.data.find(
        (el) =>
          el.ingredientName == updatedMenuRecipeData['ingredientName'] &&
          el.ingredientCode == updatedMenuRecipeData['ingredientCode']
      );
      if (requiredPackage) {

        let recipeData = this.tempIngrData.filter(item => item.Discontinued !== 'yes' )
        let tempDiscData : any = []
        tempDiscData = this.discData.filter(el =>
          el.ingredientCode === updatedMenuRecipeData['ingredientCode'] &&
          el.subRecipeCode === updatedMenuRecipeData['subRecipeCode']
        );
        if (tempDiscData.length > 0) {
          tempDiscData[0].Discontinued = 'no'
          tempDiscData[0].ingredientName = tempDiscData[0].ingredientName.toUpperCase();
          this.discData.pop(tempDiscData);
        }
        this.dataSource.data = [...recipeData, ...tempDiscData];

        let index = this.dataSource.data.indexOf(requiredPackage);
        updatedMenuRecipeData['inventoryStatus'] = 'active';
        updatedMenuRecipeData['isSubRecipe'] = updatedMenuRecipeData['Sub Category'] === 'SUBRECIPE' ? true : false ;
        this.dataSource.data[index] = updatedMenuRecipeData;
        let data = this.dataSource.data;
        this.dataSource = new MatTableDataSource<any>();
        this.dataSource.data = data;
        this.selection = new SelectionModel<any>(
          true,
          this.dataSource.data.filter(
            (el) =>
              el['inventoryStatus'] === 'active' &&
              ['no', 'No', 'N', null,''].includes(el['Discontinued'])
          )
        );
        let items = this.dataSource.data
        this.dataSource.data = items.filter(item =>
          !['yes', 'y'].includes((item.Discontinued || 'no').toLowerCase())
        );
        this.tempIngrData = this.dataSource.data;
        let disData = items.filter(item =>
          ['yes', 'y'].includes((item.Discontinued || 'no').toLowerCase())
        );
        this.discData.push(...disData)
        this.inventoryItem ? this.calculateRecipeCost() : this.getMenuCost();
        this.notify.snackBarShowSuccess('Updated successfully!');
        this.showDeleteItems = false
        this.cd.detectChanges();
      }
      this.clearForm();
    }
  }

  addNewSubRecipeRecipe() {
    if (this.ingredientsForm.invalid) {
      this.ingredientsForm.markAllAsTouched();
      this.cd.detectChanges();
    } else {
      let update = this.convertMenuRecipeKeys();
      update['modified'] = 'yes';
      update['inventoryStatus'] = 'active';
      update['isSubRecipe'] = update['Sub Category'] === 'SUBRECIPE' ? true : false ;
      Object.entries(update).forEach(([key, value]) => {
        if (value === null || value === undefined || value === '') {
            return;
        }
        if (typeof value === 'number') {
          update[key] = this.notify.truncateAndFloor(value);
        }
      });
      this.dataSource.data.unshift(update);
      let data = this.dataSource.data;
      this.dataSource.data = data;
      this.selection = new SelectionModel<any>(
        true,
        this.dataSource.data.filter(
          (el) =>
            el['inventoryStatus'] === 'active' &&
            ['no', 'No', 'N', null,''].includes(el['Discontinued'])
        )
      );
      this.showWeightError = false;
      this.showYieldError = false;
      this.tempIngrData = this.dataSource.data
      this.ingredientsForm.patchValue({
        ingredientName: '',
        ingredientCode: '',
        isModifier: 'no',
        InitialWeight: 0,
        weightInUse: 0,
        Yield: 0,
        ConsumptionUOM: '',
        rate: 0,
        finalRate: 0,
        Discontinued: 'no',
      });
      this.inventoryItem ? this.calculateRecipeCost() : this.getMenuCost();
      this.clearForm();
      this.cd.detectChanges();
    }
  }

  clearForm() {
    Object.keys(this.ingredientsForm.controls).forEach(key => {
      this.ingredientsForm.get(key)?.setErrors(null);
    });
  }

  removeDuplicates(array, key) {
    let seen = new Set();
    return array.filter((item) => {
      let keyValue = key ? item[key] : item;
      if (!seen.has(keyValue)) {
        seen.add(keyValue);
        return true;
      }
      return false;
    });
  }

  openFromIcon(timepicker: { open: () => void }) {
    timepicker.open();
  }

  createSteps() {
    const instructionGroup = this.formBuilder.group({
      steps: [this.htmlToPlainText(this.ngxEditorValue.value)],
      hmtlStringSteps: [this.ngxEditorValue.value],
      date: new Date(),
    });
    this.instructionArray.push(instructionGroup);
    this.ngxEditorValue.reset();
    setTimeout(() => {
      this.selectedIndex = this.instructionArray.length - 1;
      this.cd.detectChanges();
    }, 1000);
  }

  editSteps(stepForm, i, addSteps) {
    this.index = i;
    this.ngxEditorValue.patchValue(stepForm.value.steps);
  }

  updateSteps() {
    if (this.index >= 0 && this.index < this.instructionArray.length) {
      const instructionGroup = this.instructionArray.at(
        this.index
      ) as FormGroup;
      instructionGroup
        .get('steps')
        .setValue(this.htmlToPlainText(this.ngxEditorValue.value));
    }
    this.ngxEditorValue.reset();
    this.index = '';
    this.cd.detectChanges();

    setTimeout(() => {
      return this.createRecipeForm.get('instructions')['controls'];
    }, 1000);
  }

  preparationTime(unit: string) {
    this.selectedPreparation = unit;
  }

  cookingTime(unit: string) {
    this.selectedCooking = unit;
  }

  openMenuMapping(element) {
    let data = {
      baseData: this.baseData,
      parentData: element,
    };
    const dialogRef = this.dialog.open(MenuMappingComponent, {
      autoFocus: false,
      disableClose: true,
      maxHeight: '95vh',
      panelClass: 'smallCustomDialog',
      data: data,
    });

    dialogRef.afterClosed().subscribe((result) => {});
  }

  getTotal(key: string) {
    // this.selection.selected.filter(el => el.Discontinued === 'no')
    // let total = this.selection.selected.reduce((total, item) => {
    //   return total + this.notify.truncateAndFloor(item[key]);
    // }, 0);
    let filteredItems = this.selection.selected.filter(item => item.Discontinued === 'no');
    let total = filteredItems.reduce((total, item) => {
      return total + this.notify.truncateAndFloor(item[key]);
    }, 0);
    return this.notify.truncateAndFloor(total * this.ratio)
  }

  getModifierCost(modifierId, currentModifier) {
        let obj = {
      tenantId: this.user.tenantId,
      modifierId: modifierId,
    };
    this.api.getDetailedModifierList(obj).subscribe({
      next: (res) => {
        if (Object.keys(res).length > 0) {
          let currentPriceTier = res['tierPrices'].find(
              (el) =>
                el['tieredPriceGroupID'] ===
                this.sharedData.getDefaultPriceTier()
            ),
            requiredPrice,
            requiredServingSize;
          if (currentPriceTier) {
            if (currentPriceTier.hasOwnProperty('variantPrices')) {
              requiredServingSize = currentPriceTier['variantPrices'].find(
                (el) =>
                  el['servingSizeName'] ===
                  this.createRecipeForm.get('overallServingSize').value
              );
              requiredPrice = requiredServingSize
                ? requiredServingSize['priceStr'] === ''
                  ? 0
                  : this.notify.truncateAndFloor(requiredServingSize['priceStr'])
                : undefined;
            }
          } else {
            requiredPrice = 0;
          }
          let obj = {
            modifierName: currentModifier['modifierName'],
            modifierCost: requiredPrice,
            ingredientName: currentModifier['ingredientName'],
          };
          this.sellingPriceArray.push(obj);
        } else {
          this.notify.snackBarShowWarning('Modifiers Cost not available');
        }
        this.getMenuCost();
      },
      error: (err) => {
        console.log(err);
      },
    });
  }

  cleanSpaces(str: string): string {
    return str ? str.trim().replace(/\s+/g, ' ') : '';
  }

  optionSelected(type: string, option: any) {
    const cleanedValue = this.cleanSpaces(this.itemNameControl.value).toUpperCase();
    this.itemNameControl.setValue(cleanedValue);
    let invItem = this.sharedData.getDataForFillTheForm(
      this.cleanSpaces(option.value),
      'menu master'
    );
    if (invItem) {
      this.updateBtnActive = true;
    } else {
      this.updateBtnActive = false;
    }
    if (this.cleanSpaces(option.value).indexOf(this.question) === 0) {
      this.addOption(type);
    }
  }

  addOption(type: string) {
    const cleanedControlValue = this.cleanSpaces(this.itemNameControl.value);
    let invItem = this.sharedData.getDataForFillTheForm(
      cleanedControlValue,
      'menu master'
    );
    if (invItem) {
      this.bindData(invItem);
      this.getBaseData();
      this.isDuplicate = false;
    }
    let requiredData = this.itemList.find(
      (el) =>
        el.itemName.toLowerCase() === cleanedControlValue.toLowerCase()
    );
    if (requiredData && requiredData['category'] === 'POS ONLY') {
      let obj = {};
      obj['tenantId'] = this.user.tenantId;
      obj['menu_id'] = requiredData['menuId'];
      this.api.getPOS_MenuItemById(obj).subscribe({
        next: (res) => {
          if (res.hasOwnProperty('errCode') && res['errCode'] === 404) {
            this.notify.snackBarShowWarning('Item data missing in POS!');
          } else {
            this.createRecipeForm.patchValue({
              recipeName: res['name'],
              recipeCode: res['pluCode'],
            });
            this.dataSource.data = [];
            const { matchedData, mismatchedData } = res['servingSizes'].reduce(
              (acc, newItem) => {
                this.servingSizes = this.baseData['servingsize conversion'];
                const isMatched = this.servingSizes.some(
                  (oldItem) => oldItem['Serving Size'] === newItem.name
                );
                if (isMatched) {
                  acc.matchedData.push(newItem);
                } else {
                  acc.mismatchedData.push(newItem);
                }
                return acc;
              },
              { matchedData: [], mismatchedData: [] }
            );
            if (matchedData.length > 0) {
              let servingSizesData = res['servingSizes'].map(
                (item) => item.name
              );
              this.createRecipeForm.patchValue({
                servingSize: servingSizesData,
              });
            }
            this.itemNameControl.reset();
            this.isDuplicate = false;
            this.isIngredientsDataReady = true;
          }
        },
        error: (err) => {
          console.log(err);
        },
      });
    } else if (!requiredData) {
      this.createRecipeForm.controls['recipeName'].patchValue(
        this.removePromptFromOption(cleanedControlValue)
      );
      this.itemNameControl.reset();
      this.isDuplicate = false;
      this.dataSource.data = [];
      this.isIngredientsDataReady = true;
      this.generateRecipeCode();
    }
  }

  removePromptFromOption(option) {
    if (option.startsWith(this.question)) {
      option = option.substring(this.question.length, option.length - 1);
    }
    return option;
  }

  addOptionIngredients(addIngredients) {
    let data = this.ingredientData.filter(item => item.itemName.toLowerCase() == this.ingredientsForm.value.ingredientName.toLowerCase())
    // this.isChecked = false;
    // this.ingredientsForm.get('isModifier').setValue('No')
    this.isEdit = false;
    let invItem;
    invItem = this.dataSource.data.find(
      (item) => item.ingredientCode === data[0].itemCode
    );
    if (!invItem) {
      this.isEdit = true;
      invItem = this.findInvItem(data[0].itemCode);
    }
    this.currentInvItem = invItem
    if (invItem) {
      let uom;
      let uomData = invItem['uom'] ? invItem['uom'] : invItem['ConsumptionUOM']
      if (uomData == 'KG') {
        uom = 'GM';
      } else if (uomData == 'LITRE') {
        uom = 'ML';
      } else if (uomData == 'NOS') {
        uom = 'NOS';
      }

      let conversionCoefficient;
      conversionCoefficient = uomData == 'NOS' ? 1 : 1000;
      let rate = invItem
      ? ((invItem.hasOwnProperty('packageQty') ? invItem['packageQty'] : 1) / conversionCoefficient) * invItem['price']
      : 0;
      let modifierName = invItem['modifierName']
        ? invItem['modifierName']
        : 'N/A';
      let isModifier = invItem['isModifier'] ? invItem['isModifier'] : 'No';
      let initialWeight = invItem['InitialWeight']
        ? invItem['InitialWeight']
        : 0;
      let weightInUse = invItem['weightInUse'] ? invItem['weightInUse'] : 0;
      let _yield = invItem['Yield'] ? invItem['Yield'] : 1;
      this.AccessibleUOM = invItem['ItemType'] === 'SubRecipe' && invItem['portion'] != 0 ? [uom,"PORTION"] : [uom]
      this.ingredientsForm.patchValue({
        ingredientName: invItem['itemName'] ?? invItem['menuItemName'],
        ingredientCode: invItem['itemCode'] ?? invItem['menuItemCode'],
        category: invItem['category'] ?? invItem['Category'],
        subCategory: invItem['subCategory'] ?? invItem['Sub Category'],
        modifierName: modifierName ?? invItem['modifierName'],
        isModifier: isModifier,
        InitialWeight: initialWeight,
        weightInUse: weightInUse,
        Yield: _yield,
        ConsumptionUOM: uom,
        defaultUOM: uom,
        rate: invItem['rate'] ? invItem['rate'] : this.notify.truncateAndFloor(rate),
        finalRate: invItem['finalRate']
          ? invItem['finalRate']
          : rate * initialWeight,
        Discontinued: invItem['Discontinued'] ?? 'no',
        row_uuid: invItem['row_uuid'],
      });
    }
    // this.closeCreateIngrediantRef.close();
    // let dialogRef = this.dialog.open(addIngredients, {
    //   maxHeight: '95vh',
    //   maxWidth: '50vw',
    // });
    // dialogRef.afterClosed().subscribe((result) => {});
    // this.closeIngrediantRef = dialogRef;
    // this.closeCreateIngrediantRef = dialogRef;
    this.isDuplicate = false;
  }

  closeCreateIngredientDialog() {
    this.closeCreateIngrediantRef.close();
  }

  protected Filter(bank, form, data) {
    if (!bank) {
      return;
    }
    let search = form.value;
    if (!search) {
      data.next(bank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    data.next(
      bank.filter((data) => data['itemName'].toLowerCase().indexOf(search) > -1)
    );
  }

  makeTotalPrice() {
    let n;
    n = (
      this.ingredientsForm.value.rate * this.ingredientsForm.value.InitialWeight
    );
    this.ingredientsForm.get('finalRate').patchValue(n);
  }

  setInitialWeight() {
    this.ingredientsForm
      .get('InitialWeight')
      .patchValue(
        this.notify.truncateAndFloor(
          (
            this.ingredientsForm.value.weightInUse /
            this.ingredientsForm.value.Yield
          )
        )
      );
  }

  enableRecipeName() {
    this.isEnableName = true;
  }

  enableRecipeCode() {
    this.isEnableCode = true;
  }


  numberOnly(event): boolean {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  getCost(item) {
    let obj = {};
    obj['restaurantId'] = this.user.restaurantId;
    obj['itemCode'] = item.ingredientCode;

    this.api.getItemCost(obj).subscribe({
      next: (res) => {
        let index = this.dataSource.data.indexOf(item);
        res['success']
          ? (this.dataSource.data[index]['rate'] = res.data.price)
          : undefined;
        this.cd.detectChanges();
      },
      error: (err) => {
        console.log(err);
      },
    });
  }

  getLocationCall() {
    this.BranchData = this.sharedData.getLocation().value;
    this.preparedAt = this.BranchData.map((item) => item['branchName']);
    this.preparedAt = [...new Set(this.preparedAt)];
    if (this.preparedAt.length === 1) {
      this.createRecipeForm.get('preparedAt').setValue(this.preparedAt);
    }
    this.prepareBank = this.preparedAt;
    let uniquePrepareArray = [...new Set(this.prepareBank)];
    this.prepareData.next(uniquePrepareArray.slice());
    this.prepareFilterCtrl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe(() => {
        this.FilterLocation(
          this.prepareBank,
          this.prepareFilterCtrl,
          this.prepareData
        );
      });
    this.usedOutlet = this.BranchData.map((item) => item['branchName']);
    this.usedOutlet = [...new Set(this.usedOutlet)];
    if (this.usedOutlet.length === 1) {
      this.createRecipeForm.get('usedOutlet').setValue(this.usedOutlet);
      this.locationChange(this.usedOutlet);
    }
    this.outletBank = this.usedOutlet;
    let uniqueOutletArray = [...new Set(this.outletBank)];
    this.outletData.next(uniqueOutletArray.slice());
    this.outletFilterCtrl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe(() => {
        this.FilterLocation(
          this.outletBank,
          this.outletFilterCtrl,
          this.outletData
        );
      });
    if (this.BranchData) {
      if ((this.dialogData != null && this.dialogData.key == false) || this.dialogData.costDialogkey == true ) {
        if(!this.dialogData.createNew){
          this.bindData(this.dialogData.elements);
        }
      }
    }
  }

  protected FilterLocation(bank, form, data) {
    if (!bank) {
      return;
    }
    let search = form.value;
    if (!search) {
      data.next(bank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    data.next(bank.filter((data) => data.toLowerCase().indexOf(search) > -1));
  }

  locationChange(event) {
    const selectedWorkAreasArray = this.BranchData.filter((branch) =>
      event.includes(branch.branchName)
    );
    this.usedWorkAreaBank = selectedWorkAreasArray;
    if(this.discontinuedOutletData.length > 0){
      this.discontinuedOutletData.forEach(val => {
        this.usedWorkAreaBank = this.usedWorkAreaBank.map(item => {
          if (item.abbreviatedRestaurantId === val) {
            item.disabled = true;
          }
          return item;
        });
      })
    }
    this.workAreas.next(this.usedWorkAreaBank.slice());
    this.usedWorkAreaFilterCtrl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe(() => {
        this.FilterUsedWorkArea(
          this.usedWorkAreaBank,
          this.usedWorkAreaFilterCtrl,
          this.workAreas
        );
      });
  }

  protected FilterUsedWorkArea(bank, form, data) {
    if (!bank) {
      return;
    }
    let search = form.value;

    if (!search) {
      data.next(bank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    const filteredBank = bank.map((item) => {
      const filteredWorkAreas = item.workAreas.filter(
        (workArea) => workArea.toLowerCase().indexOf(search) > -1
      );
      return { ...item, workAreas: filteredWorkAreas };
    });
    data.next(filteredBank);
  }

  toggleSelectAllUsedWorkArea() {
    const control = this.createRecipeForm.controls['usedWorkArea'];
    let data = [...this.usedWorkAreaBank.map((location) => location.workAreas)];
    const flattenedArray = [].concat(...data);
    if (control.value.length - 1 === flattenedArray.length) {
      let defdata = []
      if (!Array.isArray(this.defaultUsedWorkAreaData)) {
        defdata = this.defaultUsedWorkAreaData.split(',');
      }
      control.setValue(defdata);
    } else {
      control.setValue(flattenedArray);
    }
  }

  readIPConfig() {
    this.api.readIPConfig(this.user.tenantId).subscribe({
      next: (res) => {
        if (res?.['success']) {
          const data = res['data'];
          this.logo = data.tenantDetails.logo
          this.opConfigData = res['data']
            if (res['data'].hasOwnProperty('defaultPriceTier')) {
            let resData =  Object.keys(this.opConfigData.defaultPriceTier)[0]
            this.resKeys =  Object.keys(this.opConfigData.defaultPriceTier)
            let val = this.user.restaurantAccess.find(val => val.restaurantIdOld == resData)
            // this.opConfigData.defaultPriceTier[resData]
            this.sellingLocation.setValue(val)
            this.getDetailedPriceList(val);
              // this.sharedData.setDefaultPriceTier(
              //   res['data']['defaultPriceTier']
              // );
            }
        }
      },
      error: (err) => { console.log(err) }
    });
  }

  printOption() {
    const discontinuedValue = this.createRecipeForm.get('Discontinued').value;
    this.status = discontinuedValue === 'yes' ? 'active' : 'Discontinued';

    const tableData = this.dataSource.data.map((element: any) => [
      element.ingredientName,
      element.ConsumptionUOM,
      this.notify.truncateAndFloor(element.weightInUse),
      this.notify.truncateAndFloor(element.Yield),
      this.notify.truncateAndFloor(element.InitialWeight),
      this.notify.truncateAndFloor(element.rate),
      this.notify.truncateAndFloor(element.finalRate),
    ]);

    let obj = {
      factory_name: this.user.name,
      recipe_details: {
        'Recipe Name': this.createRecipeForm.get('recipeName').value,
        'Recipe Code': this.createRecipeForm.get('recipeCode').value,
        'Category': this.createRecipeForm.get('category').value,
        'Sub Category': this.createRecipeForm.get('subCategory').value,
        'Status': this.createRecipeForm.get('Discontinued').value == 'no' ? 'Active' : 'Discontinued',
        'Selected Serving Size': this.createRecipeForm.get('overallServingSize').value,
        'Available Serving Size': this.createRecipeForm.get('servingSize').value.join(', '),
        'Preparatory Location': this.createRecipeForm.get('preparedAt').value.join(', '),
        'Sales Outlet': this.createRecipeForm.get('usedOutlet').value.join(', '),
        'Work Area': this.createRecipeForm.get('usedWorkArea').value.join(', ')
      },
      logo: this.logo,
      table_headers : ['Ingredient Name','UOM','Weight In Use','Yield','Initial Weight','Unit Cost','Final Rate'],
      table_data: tableData,
      summary: {
        'No Of Portion': this.notify.truncateAndFloor(this.createRecipeForm.get('portion').value),
        'Portion Weight': this.notify.truncateAndFloor(this.createRecipeForm.get('PerPortionWeight').value) + ' gm',
        'Gross Weight': this.notify.truncateAndFloor(this.getTotal('InitialWeight')) + ' gm',
        'Cost Of Production': this.notify.truncateAndFloor(this.getTotal('finalRate')) + ' Rs',
        'Selling Price': this.notify.truncateAndFloor(this.overAllSellingPrice) + ' Rs',
        'Cost Percentage': this.costPercentage + ' %',
        'Profit Margin': this.notify.truncateAndFloor(this.profitMargin) + ' Rs',
        'Profit Percentage': this.profitPercentage + ' %'
      },
      // remarks: `Remarks text goes here`,
      // footer: `Last updated by PF\nLast updated on 02/01/2024`
    };
    this.api.printInvoice(obj).subscribe({
      next: (data) => {
        this.api.globalPrintPdf(data.pdf_base64);
      },
      error: (err) => {
        console.error(err);
      }
    });
  }

  getMenuRecipes() {
    this.ingredientsForm.reset() ;
    // let currentInvItems = this.cache.getInvItems().value;
    let currentInvItems = {};
    if (
      currentInvItems.hasOwnProperty(
        this.globalLocation.value?.restaurantIdOld
      )
    ) {
      this.invItems =
        currentInvItems[
          this.globalLocation.value?.restaurantIdOld
        ];
      this.nameOptionsBank = this.invItems.map((item) => ({
        itemName: item.itemName,
        status: item.status,
        itemCode: item.itemCode,
        isSubRecipe : item.hasOwnProperty('ItemType') && item['ItemType'] === 'SubRecipe' ? true : false
      }));

      this.ingredientData = [...new Set(this.nameOptionsBank)];
      let val = this.ingredientData.map(item => item.itemName)
      this.ingredientNamesOptions = this.ingredientsForm.get('ingredientName').valueChanges.pipe(startWith(''), map(value => this._filterSearch((value || ''), val)));
      this.nameOptions.next(this.nameOptionsBank.slice());
      this.nameOptionsFilterCtrl.valueChanges
        .pipe(takeUntil(this._onDestroy))
        .subscribe(() => {
          this.Filter(
            this.nameOptionsBank,
            this.nameOptionsFilterCtrl,
            this.nameOptions
          );
        });
      !this.isCreate ? this.getBaseData() : undefined;
    } else {
      let obj = {};
      obj['tenantId'] = this.user.tenantId;
      obj['restaurantId'] = this.globalLocation.value?.restaurantIdOld
            this.api.getInvList(obj).subscribe({
        next: (res) => {
          if (res['success']) {
            this.invItems = res['invList'];
            // currentInvItems[
            //   this.globalLocation['restaurantIdOld']
            // ] = res['invList'];
            // this.cache.setInvItems(currentInvItems);

            this.nameOptionsBank = this.invItems.map((item) => ({
              itemName: item.itemName,
              status: item.status,
              itemCode: item.itemCode,
              isSubRecipe : item.hasOwnProperty('ItemType') && item['ItemType'] === 'SubRecipe' ? true : false
            }));
            this.ingredientData = [...new Set(this.nameOptionsBank)];
            this.subRecData = this.ingredientData
        .filter((item) => item.itemName && item.isSubRecipe === true)
        .map((item) => item.itemName);
            let val = this.ingredientData.map(item => item.itemName)
            this.ingredientNamesOptions = this.ingredientsForm.get('ingredientName').valueChanges.pipe(startWith(''), map(value => this._filterSearch((value || ''), val)));
            this.nameOptions.next(this.nameOptionsBank.slice());
            this.nameOptionsFilterCtrl.valueChanges
              .pipe(takeUntil(this._onDestroy))
              .subscribe(() => {
                this.Filter(
                  this.nameOptionsBank,
                  this.nameOptionsFilterCtrl,
                  this.nameOptions
                );
              });
            !this.isCreate ? this.getBaseData() : undefined;
          }
        },
        error: (err) => {
          console.log(err);
        },
      });
    }
  }

  findInvItem(itemCode) {
    let requiredItem = this.invItems.find((item) => item.itemCode == itemCode);
    return requiredItem;
  }

  convertMenuKeys() {
    const keyData = [
      ['menuItemName', 'recipeName'],
      ['menuItemCode', 'recipeCode'],
      ['weight', 'weight'],
      ['rate', 'rate'],
      ['preparedAt', 'preparedAt'],
      ['usedAtOutlet', 'usedOutlet'],
      ['usedInWorkArea', 'usedWorkArea'],
      ['closingUOM', 'closingUOM'],
      ['servingSize', 'servingSize'],
      ['Discontinued', 'Discontinued'],
      ['changed', 'changed'],
      ['modified', 'modified'],
      ['category', 'category'],
      ['subCategory', 'subCategory'],
      ['row_uuid', 'row_uuid'],
      ['portion', 'portion'],
    ];
    const updatedMenuData = {};
    keyData.forEach((key) => {
      let value = this.createRecipeForm.value[key[1]];
      if (key[0] == 'taxRate') {
        updatedMenuData[key[0]] = value || 0;
      } else {
        updatedMenuData[key[0]] = value || '';
      }
    });
    return updatedMenuData;
  }

  convertMenuRecipeKeys() {
    const keyData = [
      ['Category', 'category'],
      ['Sub Category', 'subCategory'],
      ['ingredientCode', 'ingredientCode'],
      ['ingredientName', 'ingredientName'],
      ['isModifier', 'isModifier'],
      ['modifierName', 'modifierName'],
      ['ConsumptionUOM', 'ConsumptionUOM'],
      ['defaultUOM', 'defaultUOM'],
      ['InitialWeight', 'InitialWeight'],
      ['Yield', 'Yield'],
      ['weightInUse', 'weightInUse'],
      ['rate', 'rate'],
      ['finalRate', 'finalRate'],
      ['Changed', 'Changed'],
      ['Discontinued', 'Discontinued'],
      ['row_uuid', 'row_uuid'],
      ['modified', 'modified'],
      // ['portion', 'portion'],
      ['portionCount', 'portionCount'],
    ];
    const updatedMenuRecipeData = {};
    keyData.forEach((key) => {
      let value = this.ingredientsForm.value[key[1]];
      if (key[0] == 'taxRate') {
        updatedMenuRecipeData[key[0]] = value || 0;
      } else if (key[0] == 'modifierName') {
        updatedMenuRecipeData[key[0]] =
          updatedMenuRecipeData['isModifier'] === 'No'
            ? undefined
            : value || '';
      } else {
        updatedMenuRecipeData[key[0]] = value || '';
      }
    });
    return updatedMenuRecipeData;
  }

  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  onSelectAll(event: any) {
    this.sellingPriceArray = [];
    if (event) {
      this.dataSource.data.forEach((row: any) => {
        row['inventoryStatus'] === 'active'
          ? this.selection.select(row)
          : undefined;
      });
    } else {
      this.selection.clear();
    }
    if (this.menuCost != 'N/A') {
      let currentModifiers = this.selection.selected.filter(
        (el) =>
          el['isModifier'] === 'Y' || el['isModifier'].toLowerCase() === 'yes'
      );
      if (currentModifiers.length > 0) {
        currentModifiers.forEach((el) => {
          let requiredItem = this.modifiers.find(
            (item) => item['name'] === el['modifierName']
          );
          this.getModifierCost(requiredItem ? requiredItem['id'] : 0, el);
        });
      } else {
        this.getMenuCost();
      }
    } else {
      this.inventoryItem ? this.calculateRecipeCost() : undefined;
    }
  }

  onRowSelect(checked: boolean, row: any) {
    this.sellingPriceArray = [];
    //  use this based on need
    // if (checked) {
    //   // console.log('Checkbox is checked for row:', row);
    // } else {
    //   // console.log('Checkbox is unchecked for row:', row);
    // }
    this.selection.toggle(row);
    if (this.menuCost != 'N/A') {
      let currentModifiers = this.selection.selected.filter(
        (el) =>
          el['isModifier'] === 'Y' || el['isModifier'].toLowerCase() === 'yes'
      );
      if (currentModifiers.length > 0) {
        currentModifiers.forEach((el) => {
          let requiredItem = this.modifiers.find(
            (item) => item['name'] === el['modifierName']
          );
          this.getModifierCost(requiredItem ? requiredItem['id'] : 0, el);
        });
      } else {
        this.getMenuCost();
      }
    } else {
      this.inventoryItem ? this.calculateRecipeCost() : undefined;
    }
  }

  getMenuCost() {
    let requiredData = this.priceList.find(
      (el) => {
        let serving = this.createRecipeForm.get('overallServingSize').value;
        return (
          el['pluCode'] === this.createRecipeForm.get('recipeCode').value &&
          el['servingSizeName'].toLowerCase() === serving.toLowerCase()
        );
      }
    );
        if (requiredData) {
      this.menuCost = this.notify.truncateAndFloor(requiredData['priceStr'].replace(/,/g, ''));
    } else {
      this.menuCost = 'N/A';
    }
    let modifiersCost = this.sellingPriceArray.reduce((total, item) => {
      return total + this.notify.truncateAndFloor(item['modifierCost']);
    }, 0);
this.overAllSellingPrice =
      this.menuCost === 'N/A' ? 'N/A' : this.menuCost + modifiersCost;
    let sellingPrice =
      this.menuCost === 'N/A' ? 0 : this.notify.truncateAndFloor(this.menuCost);
    let preparationCost = this.getTotal('finalRate');
    this.profitMargin =
      sellingPrice != 0
        ? this.notify.truncateAndFloor((this.overAllSellingPrice - preparationCost))
        : 0;
    let profitPercent = this.notify.truncateAndFloor(
      (((this.overAllSellingPrice - preparationCost) / this.overAllSellingPrice) * 100)
    );
    this.profitPercentage = sellingPrice != 0 ? profitPercent : 0;
    this.costPercentage =
      sellingPrice != 0
        ? this.notify.truncateAndFloor(((preparationCost / sellingPrice) * 100))
        : 0;
    this.cd.detectChanges();
  }

  focusFunction(formKey) {
    if (this.notify.truncateAndFloor(this.ingredientsForm.get(formKey).value) === 0) {
      this.ingredientsForm.get(formKey).setValue(null);
    }
  }

  focusOutFunction(formKey) {
    if (this.ingredientsForm.get(formKey)) {
      if (this.ingredientsForm.get(formKey).value === null) {
        this.ingredientsForm.get(formKey).setValue(0);
      }
    }
  }

  servingSizeChange(event) {
    if (event == 'full-default') {
      this.ratio = 1;
    } else {
      let currentServingSize = this.servingSizes.find(
        (el) => el['Serving Size'].toLowerCase() === event.toLowerCase()
      );
      this.ratio = currentServingSize ? currentServingSize['Ratio'] / 100 : 1;
      let requiredData = this.priceList.find(
        (el) =>
          el['pluCode'] === this.createRecipeForm.get('recipeCode').value &&
          el['servingSizeName'].toLowerCase() ===
          event.toLowerCase()
      );
      if (requiredData) {
        this.menuCost = this.notify.truncateAndFloor(requiredData['priceStr'].replace(/,/g, ''));
      } else {
        this.menuCost = 'N/A';
      }
      this.menuCost === 'N/A' ? this.getMenuCost() : this.onSelectAll(true);
    }
  }

  getInitialWeight(element) {
    return this.notify.truncateAndFloor(element['InitialWeight']) * this.ratio;
  }

  getYield(element) {
    return this.notify.truncateAndFloor(element['Yield']);
  }

  getWeightInUse(element) {
    return this.notify.truncateAndFloor(element['weightInUse']) * this.ratio;
  }

  getFinalRate(element) {
    return this.notify.truncateAndFloor(element['finalRate']) * this.ratio;
  }

  isPanelExpanded(category: any): boolean {
    return this.expandedPanel === category;
  }

  // Function to set the expanded panel
  setExpandedPanel(category: any): void {
    this.expandedPanel = category;
  }

  toggleExpansionPanel(value) {
    value = value.target.value;
    if (value !== '') {
      this.accordion.openAll();
    } else {
      this.accordion.closeAll();
    }
  }

  modifierChange(event) {
    event === 'Yes'
      ? this.ingredientsForm.patchValue({ modifierName: '' })
      : this.ingredientsForm.patchValue({ modifierName: 'N/A' });
  }

  getPreparedLocations(selectedBranches, branchList) {
    const branchSet = new Set(selectedBranches);
    return branchList
      .filter((branch) => branchSet.has(branch.branchName))
      .map((branch) => branch.abbreviatedRestaurantId);
  }

  getCategories() {
    this.catAndSubCat = this.sharedData.getCategories().value;
    let categoryData = Object.keys(this.sharedData.getCategories().value).map(
      (category) => category.toUpperCase()
    );
    let newCat = [...this.newCategory, ...categoryData];
    this.categories = [...new Set(newCat)];
    this.catBank = this.createRecipeForm.get('category').valueChanges.pipe(
      startWith(''),
      map((value) => this._catFilter(value || '', this.categories))
    );
  }

  addOptionCat() {
    this.createRecipeForm.controls['category'].patchValue(
      this.removePromptFromOption(this.createRecipeForm.value.category)
    );
    this.getSubCategories(this.createRecipeForm.value.category);
  }

  optionSelectedCat(option: any) {
    if (option.value.indexOf(this.question) === 0) {
      this.addOptionCat();
    } else {
      this.getSubCategories(this.createRecipeForm.value.category);
    }
    this.catBank = this.createRecipeForm.get('category').valueChanges.pipe(
      startWith(''),
      map((value) => this._catFilter(value || '', this.categories))
    );
  }

  protected _catFilter(value: string, input: string[]): string[] {
    let filterValue = value?.toLowerCase();
    let filtered = input.filter((option) =>
      option?.toLowerCase().includes(filterValue)
    );
    if (filtered.length == 0) {
      filtered = [this.question + value + '"'];
    }
    return filtered;
  }

  getSubCategories(val) {
    this.createRecipeForm.get('subCategory').setValue('');
    let data = this.baseData['menu master'].filter((item) => {
      item.category === val;
    });
    this.newSubCategory = data.map((subCat) => subCat.subCategory);
    if (!(val in this.catAndSubCat)) {
      this.catAndSubCat[val] = [];
    }
    let newSubCat = [...this.newSubCategory, ...this.catAndSubCat[val]];
    this.subCategories = [...new Set(newSubCat)];
    this.subCatBank = this.createRecipeForm
      .get('subCategory')
      .valueChanges.pipe(
        startWith(''),
        map((value) => this._catFilter(value || '', this.subCategories))
      );
  }

  optionSelectedSubCat(option: any) {
    if (option.value.indexOf(this.question) === 0) {
      this.addOptionSubCat();
    }
    this.subCatBank = this.createRecipeForm
      .get('subCategory')
      .valueChanges.pipe(
        startWith(''),
        map((value) => this._catFilter(value || '', this.subCategories))
      );
  }

  addOptionSubCat() {
    this.createRecipeForm.controls['subCategory'].patchValue(
      this.removePromptFromOption(this.createRecipeForm.value.subCategory)
    );
  }

  openSalesData() {
    this.dialogRef = this.dialog.open(this.openSalesDialog, {
      minWidth: '25vw',
      panelClass: 'smallCustomDialog',
    });
  }

  openPortionData() {
    this.dialogRef = this.dialog.open(this.openPortionDialog, {
      minWidth: '25vw',
      panelClass: 'smallCustomDialog',
      autoFocus: false,
      disableClose: true,
    });
  }

  openRenameData() {
    this.dialogRef = this.dialog.open(this.openRenameDialog, {
      maxHeight: '90vh',
      panelClass : 'smallCustomDialog'
    });
  }

  closeInfoDialog() {
    if(this.costDialogkey == true){
      this.close()
      if (this.dialogRef) {
        this.dialogRef.close();
      }
    }else{
      if (this.dialogRef) {
        this.dialogRef.close();
      }
    }
  }

  increase() {
    this.sidenavWidth = 20;
    console.log('increase sidenav width');
  }
  decrease() {
    this.sidenavWidth = 5;
    console.log('decrease sidenav width');
  }

  toggleSidebar() {
    this.sidebarOpen = !this.sidebarOpen;
    // You can add logic to open/close the sidebar here
  }

  calculateRecipeCost() {
    if (this.inventoryItem) {
      let sellingPrice = this.sellingPriceControl.value;
      let preparationCost = this.getTotal('finalRate');
      this.profitMargin =
        sellingPrice != 0
          ? this.notify.truncateAndFloor(sellingPrice - preparationCost)
          : 0;
      let profitPercent = this.notify.truncateAndFloor(
        (((this.overAllSellingPrice - preparationCost) / this.overAllSellingPrice) * 100)
      );
      this.profitPercentage = sellingPrice != 0 ? profitPercent : 0;
      this.costPercentage =
        sellingPrice != 0
          ? this.notify.truncateAndFloor(((preparationCost / sellingPrice) * 100))
          : 0;
    } else {
      this.getMenuCost();
    }
  }

  submitForm() {
    // this.createRecipeForm.get('recipeCode').setValue(this.tempRecipeForm.value.recipeName);
    // this.createRecipeForm.get('recipeName').setValue(this.tempRecipeForm.value.recipeCode);
    this.createRecipeForm.get('recipeName').setValue(this.tempRecipeForm.value.recipeName);
    this.createRecipeForm.get('recipeCode').setValue(this.tempRecipeForm.value.recipeCode ? this.tempRecipeForm.value.recipeCode : this.currentItemCode);
    this.dataSource.data.forEach((item) => (item['modified'] = 'yes'));
    this.notify.snackBarShowSuccess('Updated Successfully')
    this.cd.detectChanges();
  }

  // isOptionDisabled(group: any, option: string): boolean {
  //   const selectedValues = this.createRecipeForm.value.usedWorkArea || [];
  //   const selectedFromGroup = selectedValues.filter((value: string) =>
  //     group.workAreas.includes(value)
  //   );
  //   return selectedFromGroup.length > 0 && !selectedFromGroup.includes(option);
  // }

  PreparatoryToggleSelectAll() {
    const control = this.createRecipeForm.controls['preparedAt'];
    let uniquePrepareArray = [...new Set(this.prepareBank)]
    if (control.value.length - 1 === uniquePrepareArray.length) {
      control.setValue(this.defaultPreLocData);
    } else {
      control.setValue(uniquePrepareArray);
    }
  }

  servingToggleSelectAll(){
    const control = this.createRecipeForm.controls['servingSize'];
    let uniqueData = [...new Set(this.ServingBank)];
    if (control.value.length - 1 === uniqueData.length) {
      control.setValue([]);
    } else {
      let data = uniqueData.map(product => product["Serving Size"])
      control.setValue(data);
    }
  }

  salesOutletToggleSelectAll(){
    const control = this.createRecipeForm.controls['usedOutlet'];
    let uniqueOutletArray = [...new Set(this.outletBank)];
    if (control.value.length - 1 === uniqueOutletArray.length) {
      control.setValue(this.defaultOutletData);
    } else {
      control.setValue(uniqueOutletArray);
    }
    this.locationChange(this.createRecipeForm.value.usedOutlet)
  }

  recipeSelection(event) {
    let required = this.posItems.find((el) => el['itemName'] === event)
    this.tempRecipeForm.get('recipeCode').setValue(required['itemCode'])
    this.currentItemCode = required['itemCode'] ;
    this.cd.detectChanges();
  }

  getPortionWeight() {
    let currentWeightPerPortion = ((1 / this.createRecipeForm.get('portion').value) * this.getTotal('InitialWeight'))
    this.createRecipeForm.patchValue({ PerPortionWeight: this.notify.truncateAndFloor(currentWeightPerPortion) })
  }

  convertToPortion(){
    let initialWeight = this.getTotal('InitialWeight')
    this.createRecipeForm.get('PerPortionWeight').value < 0 ? this.createRecipeForm.get('PerPortionWeight').setValue(this.notify.truncateAndFloor(initialWeight)) : undefined ;
    let portionRatio = (initialWeight / this.createRecipeForm.get('PerPortionWeight').value)
    if (portionRatio < 1) {
      this.createRecipeForm.patchValue({ PerPortionWeight: this.notify.truncateAndFloor(initialWeight) }) ;
      portionRatio = 1 ;
    }
    this.createRecipeForm.patchValue({ portion: this.notify.truncateAndFloor(portionRatio) })
  }

  onToggleChange(value){
    let data = this.isChecked ? 'Yes': 'No'
    this.ingredientsForm.get('isModifier').setValue(data)
  }

  deleteFun(element) {
    this.isEdit = false;
    this.ingredientsForm.patchValue({
      ingredientName: element['ingredientName'],
      ingredientCode: element['ingredientCode'],
      category: element['Category'],
      subCategory: element['Sub Category'],
      modifierName: element['modifierName'],
      ConsumptionUOM: element['ConsumptionUOM'],
      InitialWeight: element['InitialWeight'],
      weightInUse: element['weightInUse'],
      Yield: element['Yield'],
      defaultUOM: element['defaultUOM'],
      rate:
        this.notify.truncateAndFloor(element['rate']) ||
        this.notify.truncateAndFloor(element['finalRate']),
      finalRate: this.notify.truncateAndFloor(this.notify.truncateAndFloor(element['finalRate'])),
      Discontinued:['no','NO' ,'No', 'N', null,''].includes(element['Discontinued']) ? 'no' : 'yes',
      row_uuid: element['row_uuid'],
    });
    if (!element['modifierName']) {
      this.ingredientsForm.patchValue({ modifierName: 'N/A' });
    }

    if (element.hasOwnProperty('portionCount')) {
      this.ingredientsForm.get('portionCount').setValue(element['portionCount']);
    }

    if (!element['isModifier']) {
      this.ingredientsForm.patchValue({ isModifier: 'No' });
    } else if (element['isModifier'] == 'N' || element['isModifier'] == 'No') {
      this.ingredientsForm.patchValue({ isModifier: 'No' });
    } else {
      this.ingredientsForm.patchValue({ isModifier: 'Yes' });
    }
    element['Discontinued'] = ['no','NO' ,'No', 'N', null,''].includes(element['Discontinued']) ? 'no' : 'yes'

    // if (
    //   element['Discontinued'] === 'Y' ||
    //   element['Discontinued'] === 'YES' ||
    //   element['Discontinued'] === 'yes'
    // ) {
    //   this.ingredientsForm.get('Discontinued').setValue('yes');
    // } else if (
    //   element['Discontinued'] === 'N' ||
    //   element['Discontinued'] === 'NO' ||
    //   element['Discontinued'] === 'no'
    // ) {
    //   this.ingredientsForm.get('Discontinued').setValue('no');
    // } else {
    //   this.ingredientsForm.get('Discontinued').setValue('no');
    // }

    this.dialogRef = this.dialog.open(this.openDeleteDialog, {
      minWidth: '25vw',
      panelClass: 'smallCustomDialog',
    });
  }

  deleteData(){
    const indexToRemove = this.dataSource.data.findIndex(
        (recipe) => recipe.ingredientCode === this.ingredientsForm.value.ingredientCode
      );
      if (indexToRemove !== -1) {
        let item = this.dataSource.data[indexToRemove];  // Store the removed item
        this.removedItem.push(item)
        this.dataSource.data = this.dataSource.data
          .slice(0, indexToRemove)
          .concat(this.dataSource.data.slice(indexToRemove + 1));
      }
      this.tempIngrData = this.dataSource.data

    this.ingredientsForm.reset();
        this.ingredientsForm.patchValue({
          ingredientName: this.createRecipeForm.value.menuItemCode,
          ingredientCode: this.createRecipeForm.value.menuItemName,
    });
    this.removedItem.forEach(item => {
      item['delete'] = true;
      // item['Discontinued'] = 'yes';
    });
    this.clearForm();
    this.closeInfoDialog();
  }

  deleteDataFormDB(){
    let obj = {};
    if(this.removedItem.length > 0){
      this.removedItem.forEach(item => {
        item['delete'] = true;
        // item['Discontinued'] = 'yes';
      });
      obj['tenantId'] = this.user.tenantId;
      obj['userEmail'] = this.user.email;
      obj['data'] = this.removedItem;
      obj['type'] = 'recipe';
      obj['category'] = 'menu'
      this.api
        .removeData(obj)
        .pipe(first())
        .subscribe({
          next: (res) => {
            if (res['success']) {
              this.cd.detectChanges();
              this.notify.snackBarShowSuccess(
                'item Deleted successfully'
              );
            }
          },
          error: (err) => {
            console.log(err);
          },
        });
    }

  }

  discontinueData(){
      let updatedMenuRecipeData = this.convertMenuRecipeKeys();
      updatedMenuRecipeData['modified'] = 'yes';
      updatedMenuRecipeData['Discontinued'] = 'yes';
      let requiredPackage = this.dataSource.data.find(
        (el) =>
          el.ingredientCode == updatedMenuRecipeData['ingredientCode'] &&
          el.subRecipeCode == updatedMenuRecipeData['subRecipeCode']
      );
      if (requiredPackage) {
        requiredPackage['modified'] = 'yes';
        requiredPackage['Discontinued'] = 'yes';
        let index = this.dataSource.data.indexOf(requiredPackage);
        this.discData.push(updatedMenuRecipeData)
        this.dataSource.data[index] = updatedMenuRecipeData;
        let data = this.dataSource.data.filter(item =>
          !['yes', 'y'].includes((item.Discontinued || 'no').toLowerCase())
        );
        this.dataSource.data = data
        // this.dataSource.paginator = this.paginator;
        this.notify.snackBarShowSuccess('Updated successfully');
        this.ingredientsForm.reset();
        this.ingredientsForm.patchValue({
          ingredientName: this.createRecipeForm.value.menuItemName,
          ingredientCode: this.createRecipeForm.value.menuItemCode,
        });
        this.closeInfoDialog();
        this.cd.detectChanges();
      }
  }

  showItems(){
    if(this.showDeleteItems == true){
      this.dataSource.data = this.discData
    }else if(this.showDeleteItems == false){
      this.dataSource.data = this.tempIngrData.filter(item =>
        !['yes', 'y'].includes((item.Discontinued || 'no').toLowerCase())
      );
    }
  }

  openSelect(select: MatSelect) {
    select.open();
  }

  applyFilter(filterValue: any) {
    this.costDataSource.filter = (filterValue.target.value).trim().toLowerCase();
  }

  updateBaseData(data){
    const index = this.baseData['menu master'].findIndex(item => item['menuItemCode'] === data['menu master'][0]['menuItemCode']);
    data['menu master'][0]['modified'] = 'no'
    if (index !== -1) {
      this.baseData['menu master'][index] = data['menu master'][0]
    } else {
      this.baseData['menu master'].push(data['menu master'][0])
    }
    data['menu recipes'].forEach((el)=> {
      const recipeIndex = this.baseData['menu recipes'].findIndex(item => item['menuItemCode'] === el['menuItemCode'] && item['ingredientCode'] === el['ingredientCode']);
      el['modified'] = 'no'
      if (recipeIndex !== -1) {
        this.baseData['menu recipes'][recipeIndex] = el
      } else {
        this.baseData['menu recipes'].push(el)
      }
    })
    this.sharedData.setBaseData(this.baseData)
  }

  restaurantChange(event) {
    console.log("🚀 ~ MenuMasterComponent ~ restaurantChange ~ event:", event)
  }

  onDelete(location: string, event: Event, select ,group) {
    event.stopPropagation();
    this.selectedDropDown = select
    this.selectedData = location
    this.groupData = group
    this.dialogRef = this.dialog.open(this.discontinuedSelectDialog, {
      width: '500px',
    });
    this.dialogRef.afterClosed().subscribe(result => {
    });
  }

  openLocations(location) {
    this.dialogLocation = location
    this.dialogRef = this.dialog.open(this.setLocationDialog, {
      width: '400px',
    });
    this.dialogRef.afterClosed().subscribe(result => {
    });
  }

  onRestore(location: string, event: Event, select ,group){
    event.stopPropagation();
    if(select === 'usedOutlet'){
      this.discontinuedOutletData = this.discontinuedOutletData.filter(item => item !== location);
      const selectedWorkAreasArray = this.BranchData.filter(branch => location.includes(branch.branchName))
      this.discontinuedUsedWorkAreaData = this.discontinuedUsedWorkAreaData.filter(item => !selectedWorkAreasArray[0].workAreas.includes(item));
      this.discontinuedOutletData = this.discontinuedOutletData.filter(item => item !== location);
      this.discontinuedUsedWorkAreaData = this.discontinuedUsedWorkAreaData.filter(item =>
        item.branchName !== location
      );

      this.usedWorkAreaBank =  this.usedWorkAreaBank.map(item => {
        if (item.branchName === location && item.hasOwnProperty('disabled')) {
          delete item.disabled;
        }
        return item;
      });
      this.workAreas.next(this.usedWorkAreaBank.slice());
      this.usedWorkAreaFilterCtrl.valueChanges
        .pipe(takeUntil(this._onDestroy))
        .subscribe(() => {
          this.FilterUsedWorkArea(
            this.usedWorkAreaBank,
            this.usedWorkAreaFilterCtrl,
            this.workAreas
          );
        });
    }else if(select === 'usedWorkArea'){
      this.discontinuedUsedWorkAreaData.forEach(item => {
        if (item.abbreviatedRestaurantId === group.abbreviatedRestaurantId) {
          item.workAreas = item.workAreas.filter(workArea => workArea !== location);
        }
      });
      this.discontinuedUsedWorkAreaData = this.discontinuedUsedWorkAreaData.filter(item => item.workAreas.length > 0);
      // this.discontinuedUsedWorkAreaData = this.discontinuedUsedWorkAreaData.filter(item => item !== location);
    }else if(select === 'preparatoryLocation'){
      this.discontinuedPreLocData = this.discontinuedPreLocData.filter(item => item !== location);
    }
    this.cd.detectChanges();
  }

  discontinuedSelectData(){
    if(this.selectedDropDown === 'usedOutlet'){
      this.discontinuedOutletData.push(this.selectedData)
      const selectedWorkAreasArray = this.BranchData.filter(branch => this.selectedData.includes(branch.branchName))
      this.discontinuedUsedWorkAreaData.push(selectedWorkAreasArray[0])
      this.usedWorkAreaBank = this.usedWorkAreaBank.map(item => {
        if (item.branchName === this.selectedData) {
          item.disabled = true;
        }
        return item;
      });
      this.workAreas.next(this.usedWorkAreaBank.slice());
      this.usedWorkAreaFilterCtrl.valueChanges
        .pipe(takeUntil(this._onDestroy))
        .subscribe(() => {
          this.FilterUsedWorkArea(
            this.usedWorkAreaBank,
            this.usedWorkAreaFilterCtrl,
            this.workAreas
          );
        });

    }else if(this.selectedDropDown === 'usedWorkArea'){
      [this.groupData].forEach(item => {
        const matchingIssued = this.discontinuedUsedWorkAreaData.find(issuedItem => issuedItem.abbreviatedRestaurantId === item.abbreviatedRestaurantId);
        if (matchingIssued) {
            matchingIssued.workAreas.push(this.selectedData);
        } else {
            const newObject = {
                abbreviatedRestaurantId: item.abbreviatedRestaurantId,
                workAreas: [this.selectedData]
            };
            this.discontinuedUsedWorkAreaData.push(newObject);
        }
      });
      const newArray = [this.groupData].map(item => {
          return {
              abbreviatedRestaurantId: item.abbreviatedRestaurantId,
              workAreas: item.workAreas.filter(workArea => workArea === this.selectedData)
          };
      });
      this.discontinuedUsedWorkAreaData.push(...newArray);
      // this.discontinuedUsedWorkAreaData.push(this.selectedData)
    }else if(this.selectedDropDown === 'preparatoryLocation'){
      this.discontinuedPreLocData.push(this.selectedData)
    }
    this.closeDiscontinuedDialog();
    this.cd.detectChanges();
  }

  closeDiscontinuedDialog(){
    this.dialogRef.close();
  }

  isOptionDisabled(data: string , group): boolean {
    return this.discontinuedUsedWorkAreaData.some(item =>
      item.abbreviatedRestaurantId === group.abbreviatedRestaurantId &&
      item.workAreas.includes(data)
    );
  }

  isCheckOptionDisabled(data: string , group): boolean {
    return this.discontinuedUsedWorkAreaData.some(item =>
      item.abbreviatedRestaurantId === group.abbreviatedRestaurantId &&
      item.workAreas.includes(data)
    );
  }

  setDiscontinuedDataInRolopos(){
    this.api.dicontinuedData({
      'tenantId' : this.user.tenantId,
      'userEmail' : this.user.email,
      'type' : 'menuRecipeLocations',
      'discontinuedLocations' : {
        'menuRecipeLocations' : {
          'procuredAtDiscontinued' : this.discontinuedPreLocData.length > 0 ? this.discontinuedPreLocData : [],
          'outLetDiscontinued' : this.discontinuedOutletData.length > 0 ? this.discontinuedOutletData : [],
          'issuedToDiscontinued' : this.discontinuedUsedWorkAreaData.length > 0 ? this.discontinuedUsedWorkAreaData : []
        }
      }
    }).pipe(first()).subscribe({
      next: (res) => {
        if (res['success']) {
          this.cd.detectChanges();
        }
      },
      error: (err) => { console.log(err) }
    });
  }

  // getConfigData(value) {
  //   if (this.sharedData.getDefaultPriceTier() === 0) {
  //     this.api.readIPConfig(this.user.tenantId).subscribe({
  //       next: (res) => {
  //         if (res['success']) {
  //           if (res['data'].hasOwnProperty('defaultPriceTier')) {
  //             this.getDetailedPriceList(res['data']['defaultPriceTier']);
  //             this.sharedData.setDefaultPriceTier(
  //               res['data']['defaultPriceTier']
  //             );
  //           }
  //         }
  //       },
  //       error: (err) => {
  //         console.log(err);
  //       },
  //     });
  //   }
  // }

  getDetailedPriceList(value) {
    let val = value.restaurantIdOld
    let obj = {
      tenantId: this.user.tenantId,
      priceId: this.opConfigData.defaultPriceTier[val].defaultPriceTier,
      restaurantId: val,
    };
    this.api.getDetailedPriceList(obj).subscribe({
      next: (res) => {
        if (Array.isArray(res)) {
          this.priceList = res;
          this.getMenuCost();
        }
        // this.sharedData.setPriceList(priceList);
      },
      error: (err) => {
        console.log(err);
      },
    });
  }

  closeLocDialog(){
    this.dialogRef.close();
  }

  checkNewIdsInAllData(restaurantIdOld: string): boolean {
    if (this.resKeys.length > 0) {
      const isAvailable = this.resKeys.includes(restaurantIdOld);
      return isAvailable;
    }
    return false;
  }

  addSellingPrice(){
    this.sharedData.checkNavigate(true);
    this.router.navigate(['/dashboard/setting']);
  }

  validateWeightGreaterThanZero(control: AbstractControl) {
    return control.value > 0 ? null : { weightInvalid: true };
  }

  validateYieldGreaterThanZero(control: AbstractControl) {
    return control.value > 0 ? null : { yieldInvalid: true };
  }

}
