import {
  ChangeDetectionStrategy,
  Component,
  Inject,
  OnInit,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { InventoryService } from 'src/app/services/inventory.service';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatNativeDateModule, MatOption } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSliderModule } from '@angular/material/slider';
import { MatSelectModule } from '@angular/material/select';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogModule,
} from '@angular/material/dialog';
import { AuthService } from 'src/app/services/auth.service';
import { MasterDataService } from 'src/app/services/master-data.service';
import { NotificationService } from 'src/app/services/notification.service';
import { Observable, first, map, startWith } from 'rxjs';
import { ShareDataService } from 'src/app/services/share-data.service';
import { MatRadioModule } from '@angular/material/radio';
import { MatExpansionModule } from '@angular/material/expansion';

@Component({
  selector: 'app-serving-size',
  standalone: true,
  templateUrl: './serving-size.component.html',
  styleUrls: ['./serving-size.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    FormsModule,
    MatDialogModule,
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatNativeDateModule,
    MatDatepickerModule,
    MatInputModule,
    MatSliderModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatSelectModule,
    MatAutocompleteModule,
    MatTableModule,
    MatRadioModule,
    MatExpansionModule
  ],
})
export class ServingSizeComponent implements OnInit {
  units: string[] = ['%', 'GM', 'ML'];
  registrationForm!: FormGroup;
  baseData: any;
  action: string = 'view';
  dataSource = new MatTableDataSource<any>([]);
  user: any;
  displayedColumns: string[];
  isChecked: boolean = false;
  isDuplicate: any;
  question = 'Would you like to add "';
  updateBtnActive: boolean = false;
  itemNameControl = new FormControl('');
  itemNameOptions: Observable<any[]>;
  isUpdateActive: boolean = false;
  isReadOnly: boolean = true;
  multi = true;
  items: any[];
  loadServingBtn : boolean = true;
  isCreateButtonDisabled = false;
  isUpdateButtonDisabled = false;
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    private fb: FormBuilder,
    private api: InventoryService,
    private activatedRoute: ActivatedRoute,
    private router: Router,
    public dialog: MatDialog,
    private auth: AuthService,
    @Inject(MAT_DIALOG_DATA) public dialogData: any,
    private masterDataService: MasterDataService,
    private notify : NotificationService,
    private sharedData: ShareDataService,

  ) {
    this.user = this.auth.getCurrentUser();
    this.isDuplicate = this.dialogData.key;
    this.baseData = this.sharedData.getBaseData().value;
    this.getServingSizes() ;
    this.registrationForm = this.fb.group({
      servingSize: new FormControl<string>('', Validators.required),
      ratio: new FormControl<string>('', Validators.required),
      conversionUnit: new FormControl<string>('%'),
      quantity: new FormControl<string>('', Validators.required),
      quantityUnit: new FormControl<string>('', Validators.required),
      row_uuid : new FormControl<string>('')
    }) as FormGroup;

    if (this.dialogData != null && this.dialogData.key == false) {
      this.isUpdateActive = true;
      this.setServingSizeData(this.dialogData.elements);
    }
  }

  ngOnInit(): void {
    this.displayedColumns = [
      'action',
      'servingSize',
      'ratio',
      'conversionUnit',
      'Quantity',
      'QuantityUnit',
    ];
    this.dataSource.data = this.data['servingsize conversion'];
    this.masterDataService.isChecked$.subscribe(isChecked => {
      this.isChecked = isChecked;
      this.isChecked ? (this.dataSource.data = this.data['servingsize conversion'].filter((size) => (size.changed && size.changed == true))) : undefined ;
    });
  }

  create() {
    this.isCreateButtonDisabled = true;
    if (this.registrationForm.invalid) {
      this.registrationForm.markAllAsTouched();
      this.notify.snackBarShowError('Please fill out all required fields')
      this.isCreateButtonDisabled = false;
    } else {
      if (Object.keys(this.baseData).length > 0) {
        let updatedData = this.convertServingSizeKeys();
        updatedData['modified'] = "yes";
        updatedData['changed'] = true;
        let tempObj = {}
        tempObj['servingsize conversion'] = this.baseData['servingsize conversion']
        tempObj['servingsize conversion'].unshift(updatedData);
        tempObj['servingsize conversion'] = tempObj['servingsize conversion'].filter(item => item.modified === "yes");
        let obj = {}
        obj['tenantId'] = this.user.tenantId
        obj['userEmail'] = this.user.email
        obj['data'] = tempObj
        obj['type'] = 'recipe'
        this.api.updateData(obj).pipe(first()).subscribe({
          next: (res) => {
            if (res['success']) {
              this.notify.snackBarShowSuccess('Item added successfully!');
            } else {
              this.notify.snackBarShowError('Something went wrong!');
            }
            this.close();
          },
          error: (err) => {
            console.log(err);
          }
        });
      } else {
        this.isCreateButtonDisabled = false;
        this.notify.snackBarShowError('Something went wrong!')
      }
    }
  }

  update() { 
    this.isUpdateButtonDisabled = true;
    if (this.registrationForm.invalid) {
      this.registrationForm.markAllAsTouched();
      this.notify.snackBarShowError('Please fill out all required fields')
      this.isUpdateButtonDisabled = false;
    } else {
      if (Object.keys(this.baseData).length > 0) {
          let updatedData = this.convertServingSizeKeys();
          updatedData['modified'] = "yes";
          let tempObj = {}
          tempObj['servingsize conversion'] = this.baseData['servingsize conversion']
          let requiredVendor = tempObj['servingsize conversion'].find((el) => (el['Serving Size']).toLowerCase() == (updatedData['Serving Size'].toLowerCase()))
          let index = tempObj['servingsize conversion'].indexOf(requiredVendor)
          tempObj['servingsize conversion'][index] = updatedData;
          tempObj['servingsize conversion'] = tempObj['servingsize conversion'].filter(item => item.modified === "yes");
          let obj = {}
          obj['tenantId'] = this.user.tenantId
          obj['userEmail'] = this.user.email
          obj['data'] = tempObj
          obj['type'] = 'recipe'
          this.api.updateData(obj).pipe(first()).subscribe({
            next: (res) => {
              if (res['success']) {
                this.notify.snackBarShowSuccess('Item updated successfully')
              }
            },
            error: (err) => {
              console.log(err);
            }
          });
      } else {
        this.isUpdateButtonDisabled = false;
        this.notify.snackBarShowError('Something went wrong!')
      }
    }
  }

  back() {
    this.action = 'view';
  }

  openServingSize() {
    this.action = 'create';
  }

  close() {
    this.dataSource.data = [];
    this.masterDataService.setNavigation('servingsize conversion');
    this.router.navigate(['/dashboard/home']);
    this.dialog.closeAll();
  }

  setServingSizeData(element) {
    this.action = 'update';
    this.registrationForm.setValue({
      servingSize: element['Serving Size'],
      ratio: element['Ratio'],
      conversionUnit: element['Conversion Unit'],
      quantity: element['Quantity'],
      quantityUnit: element['QuantityUnit'] ? element['QuantityUnit'] : [],
      row_uuid: element['row_uuid']
    });
    this.loadServingBtn = false;
  }

  optionSelected(type: string, option: any) {
    let invItem = this.sharedData.getDataForFillTheForm(option.value, 'servingsize conversion')
    if (invItem) {
      this.updateBtnActive = true;
    } else {
      this.updateBtnActive = false;
    }

    if (option.value.indexOf(this.question) === 0) {
      this.addOption(type);
    }
  }

  cleanSpaces(str: string): string {
    return str ? str.trim().replace(/\s+/g, ' ') : '';
  }

  addOption(type: string) {
    const cleanedItemName = this.cleanSpaces(this.itemNameControl.value);
    let requiredData = this.items.find(
      (el) => this.cleanSpaces(el.servingSize).toLowerCase() === cleanedItemName.toLowerCase()
    );

    if (requiredData && requiredData.category != "POS ONLY") {
      let requiredItem = this.baseData['servingsize conversion'].find(
        (el) => this.cleanSpaces(el['Serving Size']).toLowerCase() === this.cleanSpaces(requiredData.servingSize).toLowerCase()
      );
      this.setServingSizeData(requiredItem);
      this.isUpdateActive = true;
    }

    this.registrationForm.controls['servingSize'].patchValue(
      this.removePromptFromOption(cleanedItemName)
    );

    this.itemNameControl.reset();
    this.isDuplicate = false;
  }

  removePromptFromOption(option) {
    if (option.startsWith(this.question)) {
      option = option.substring(this.question.length, option.length - 1);
    }
    return option;
  }

  _filter(value: string, input: any[]): { category: string, items: string[] }[] {
    let filterValue = value.toLowerCase();
    let filteredCategories = [];

    input.forEach(item => {
      let category = item.category.toLowerCase();
      let servingSize = item.servingSize.toLowerCase();

      if (category.includes(filterValue) || servingSize.includes(filterValue)) {
        let existingCategory = filteredCategories.find(cat => cat.category === category);

        if (!existingCategory) {
          existingCategory = { category: category, items: [] };
          filteredCategories.push(existingCategory);
        }
        existingCategory.items.push(servingSize);
      }
    });
    return filteredCategories;
  }

  checkItem(event){
    let invItem = this.sharedData.getDataForFillTheForm(event.target.value, 'servingsize conversion')
    if(invItem){
      this.updateBtnActive = true;
    }else{
      this.updateBtnActive = false;
    }
  }


  convertServingSizeKeys() {  
    const keyData = [
      ["Serving Size","servingSize"],
      ["Ratio","ratio"],
      ["Conversion Unit","conversionUnit"],
      ["Quantity","quantity"],
      ["QuantityUnit","quantityUnit"],
      ["row_uuid" ,"row_uuid"],
      ["modified" ,"modified"],
      ["Changed","Changed"]
    ];
    this.convertServingSizeDataType(this.registrationForm.value);
    const updatedServingData = {};
    keyData.forEach((key) => {
      let value = this.registrationForm.value[key[1]];
      if (key[0] == "taxRate"){
        updatedServingData[key[0]] = value || 0;
      }else{
        updatedServingData[key[0]] = value || '';
      }
    });
    return updatedServingData
  }

  convertServingSizeDataType(jsonData){
    this.registrationForm.setValue({
      servingSize: jsonData.servingSize,
      ratio: jsonData.ratio,
      conversionUnit: jsonData.conversionUnit,
      quantity: jsonData.quantity,
      quantityUnit: jsonData.quantityUnit,
      row_uuid: jsonData.row_uuid,
    });
  }

  getServingSizes() {
    this.api.getPOSServingSizes(this.user.tenantId).pipe(first()).subscribe({
      next: (res) => {
      this.items = []
      res.forEach((size) => {
        let obj = {}
        let requiredData = this.baseData['servingsize conversion'].find((el) => el['Serving Size'] === size.name);
        obj['category'] = requiredData ? 'BOTH' : 'POS ONLY';
        obj['servingSize'] = requiredData ? requiredData['Serving Size'] : size['name'];
        this.items.push(obj);
      })
      this.baseData['servingsize conversion'].forEach(element => {
        let requiredData = this.items.find((el) => element['Serving Size'] === el.servingSize);
        if (!requiredData) {
          let obj = {}
          obj['category'] = 'INVENTORY ONLY';
          obj['servingSize'] = element['Serving Size'];
          this.items.push(obj);
        }
      });
      this.itemNameOptions = this.itemNameControl.valueChanges.pipe(startWith(''), map(value => this._filter((value || ''), this.items)));
      },
      error: (err) => {
        console.log(err);
      }
    });
  }

}
