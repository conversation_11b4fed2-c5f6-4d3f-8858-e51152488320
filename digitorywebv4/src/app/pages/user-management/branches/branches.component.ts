import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { NgxSkeletonLoaderModule } from 'ngx-skeleton-loader';
import { MatCardModule } from '@angular/material/card';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { InventoryService } from 'src/app/services/inventory.service';
import { MAT_DIALOG_DATA, MatDialog, MatDialogModule } from '@angular/material/dialog';
import { ShareDataService } from 'src/app/services/share-data.service';
import { AuthService } from 'src/app/services/auth.service';
import { NotificationService } from 'src/app/services/notification.service';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatSliderModule } from '@angular/material/slider';
import { MatSelectModule } from '@angular/material/select';
import { MatRadioModule } from '@angular/material/radio';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatDividerModule } from '@angular/material/divider';
import { AutocompleteComponent } from 'src/app/components/autocomplete/autocomplete.component';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatStepperModule } from '@angular/material/stepper';
import { Observable, ReplaySubject, Subject, first, map, startWith, takeUntil } from 'rxjs';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MasterDataService } from 'src/app/services/master-data.service';
import { Router } from '@angular/router';
@Component({
  selector: 'app-branches',
  standalone: true,
  imports: [
    FormsModule,
    MatDialogModule,
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatNativeDateModule,
    MatDatepickerModule,
    MatInputModule,
    MatSliderModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatSelectModule,
    MatRadioModule,
    MatAutocompleteModule,
    MatDividerModule,
    AutocompleteComponent,
    MatTableModule,
    NgxMatSelectSearchModule,
    MatCheckboxModule,
    MatTooltipModule,
    MatToolbarModule,
    MatSnackBarModule,
    NgxSkeletonLoaderModule,
    MatStepperModule
  ],
  templateUrl: './branches.component.html',
  styleUrls: ['./branches.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class BranchesComponent {
  branchForm!: FormGroup;
  user: any;
  searchControl = new FormControl('', [Validators.required, Validators.email]);
  isUpdateActive: boolean = false;
  searchOptions: Observable<string[]>;
  question = 'Would you like to add "';
  isDuplicate: boolean;
  isReadOnly: boolean = true;
  baseData: any;
  updateBtnActive: boolean = false;
  loadBtn: boolean = false;
  showUpdateBtn: boolean = false;
  isLoad: boolean = false;
  workAreaBank: Observable<string[]>;
  public workAreaIds = [];
  public roles =[];
  public workAreaFilterCtrl: FormControl = new FormControl();
  protected _onDestroy = new Subject<void>();
  public allLocations: any;
  loadBranchBtn : boolean = true;
  isCreateButtonDisabled = false;
  isUpdateButtonDisabled = false;
  constructor(
    private fb: FormBuilder,
    private api: InventoryService,
    public dialog: MatDialog,
    private router: Router,
    private masterDataService: MasterDataService,
    private sharedData: ShareDataService,
    private auth: AuthService,
    private notify: NotificationService,
    private cd: ChangeDetectorRef,
    @Inject(MAT_DIALOG_DATA) public dialogData: any,
  ) {
    this.user = this.auth.getCurrentUser();
    this.isDuplicate = this.dialogData.key;
    this.baseData = this.sharedData.getBaseData().value;

    this.sharedData.getUserItem.subscribe(obj =>{
      if(obj.branches){
        let names = obj.branches.map(obj => obj.branchLocation)
        this.searchOptions = this.searchControl.valueChanges.pipe(startWith(''), map(value => this._filter((value || ''), names)));
      }
    });

    this.branchForm = this.fb.group({
      tenantId: new FormControl<string>(this.user.tenantId),
      tenantName: new FormControl<string>('', Validators.required),
      branchName: new FormControl<string>('', Validators.required),
      branchLocation: new FormControl<string>('', Validators.required),
      restaurantId: new FormControl<string>('', Validators.required),
      closingTimeFormat: new FormControl<string>('24 hours'),
      abbreviatedRestaurantId: new FormControl<string>('', Validators.required),
      branchType: new FormControl<string>('', Validators.required),
      workArea: new FormControl<string>(''),
      restaurantClosingTime: new FormControl<string>('', Validators.required),
      storeId: new FormControl<string>('', Validators.required),
      defaultSalesFloorNo: new FormControl<string>('999'),   
      storeAvailable:new FormControl<string>('Yes'),   
      billTo: new FormControl<string>(''),
      shipTo: new FormControl<string>(''),
      panNo: new FormControl<string>(''),
      gstNo: new FormControl<string>(''),
      modified: new FormControl<string>(''),
      row_uuid: new FormControl<string>('')
    }) as FormGroup;
  }

  ngOnInit(){
    this.sharedData.getItemNames.pipe(first()).subscribe(obj => {
      if (this.dialogData.key == false) {
        this.isUpdateActive = true;            
        this.setValuesForForm(this.dialogData.elements);
      }
    })
    this.sharedData.getRole.pipe(first()).subscribe(obj =>{
      this.roles = obj;
    })
  }

  close() {
    this.dialog.closeAll();
    this.masterDataService.setNavigation('branches');
    this.router.navigate(['/dashboard/home']);
    this.dialog.closeAll();
  }

  cleanSpaces(str: string): string {
    return str ? str.trim().replace(/\s+/g, ' ') : '';
  }

  optionSelected(type: string, option: any) {
    let invItem = this.sharedData.getDataForFillTheForm(option.value, 'branches')
    if (invItem) {
      this.updateBtnActive = true;
    } else {
      this.updateBtnActive = false;
    }
    if (option.value.indexOf(this.question) === 0) {
      this.addOption(type);
    }
  }

  addOption(type: string) {
    this.loadBtn = true;
    if (type == "branches") {
      let cleanName = this.cleanSpaces(this.searchControl.value);
      let invItem = this.sharedData.getDataForFillTheForm(cleanName, 'branches')
      if (invItem) {
        this.isUpdateActive = true;
        this.setValuesForForm(invItem);
      }
      let search = this.removePromptFromOption(cleanName)
      this.branchForm.controls['branchLocation'].patchValue(search);
      this.branchForm.controls['restaurantId'].patchValue(`${this.user.tenantId}@${search}`);
      this.searchControl.reset();
      this.isDuplicate = false
    }
    this.loadBtn = false;
  }

  instantAdd(){
    let search = this.branchForm.get('workArea').value ;    
    if (search && search.length > 0 && !(this.workAreaIds.includes(search))) {
      this.workAreaIds.unshift(search);
      this.notify.snackBarShowSuccess('Workarea added successfully!');
      this.workAreaBank = this.branchForm.get('workArea').valueChanges.pipe(startWith(''), map(value => this._filter((value || ''), this.workAreaIds)));
      this.branchForm.controls['workArea'].patchValue('');
    } 
  }

  removePromptFromOption(option) {
    if (option.startsWith(this.question)) {
      option = option.substring(this.question.length, option.length - 1);
    }
    return option;
  }

  setValuesForForm(form){
    this.showUpdateBtn = true;
    if(form.workArea){
      this.workAreaIds = form.workArea.split(',');   
    }
    this.workAreaBank = this.branchForm.get('workArea').valueChanges.pipe(startWith(''), map(value => this._filter((value || ''), this.workAreaIds)));
    this.branchForm.setValue({
      tenantId:form.tenantId,
      tenantName: form.tenantName,
      branchName: form.branchName,
      branchLocation: form.branchLocation,
      restaurantId: form.restaurantId,
      abbreviatedRestaurantId: form.hasOwnProperty('abbreviatedRestaurantId') ? form.abbreviatedRestaurantId : (form.hasOwnProperty('abbreviated restaurantId') ? form['abbreviated restaurantId'] : ''),
      closingTimeFormat: form.closingTimeFormat,
      branchType: form.branchType,
      workArea: [],
      restaurantClosingTime: form.restaurantClosingTime,
      storeId: form.storeId,
      defaultSalesFloorNo: form.hasOwnProperty('defaultSalesFloorNo') ? form.defaultSalesFloorNo : '999', 
      storeAvailable: form.hasOwnProperty('storeAvailable') ? form.storeAvailable : 'Yes', 
      billTo: form.hasOwnProperty('billTo') ? form.billTo : '',
      shipTo: form.hasOwnProperty('shipTo') ? form.shipTo : '',
      panNo: form.hasOwnProperty('panNo') ? form.panNo : '',
      gstNo: form.hasOwnProperty('gstNo') ? form.gstNo : '',
      modified: form.modified,
      row_uuid: form.row_uuid
    });
    this.loadBranchBtn = false;
  }

  _filter(value: string, input: string[]): string[] {
    const filteredArray = input.filter(value => value !== null && value !== undefined);
    if (!Array.isArray(value)) {
      var filterValue = this.cleanSpaces(value).toLowerCase();
    }
    let filtered = filteredArray.filter(option => this.cleanSpaces(option).toLowerCase().includes(filterValue));
    if (filtered.length == 0) {
      filtered = [this.question + this.cleanSpaces(value) + '"'];
    }
    return filtered;
  }

  optionSelectedWorkArea( option: any){
    if (option.value.indexOf(this.question) === 0) {    
      let search = this.removePromptFromOption(this.branchForm.value.workArea);
      if (search && search.length > 0 && !(this.workAreaIds.includes(search))) {
        this.workAreaIds.unshift(search);
        this.notify.snackBarShowSuccess('Workarea added successfully!');
        this.workAreaBank = this.branchForm.get('workArea').valueChanges.pipe(startWith(''), map(value => this._filter((value || ''), this.workAreaIds)));
        this.branchForm.controls['workArea'].patchValue('');
      }
    }
  }

  convertUserKeys() {
    const keyData = [
      ['tenantId' , 'tenantId' ], 
      ['tenantName' , 'tenantName' ], 
      ['branchName' , 'branchName' ], 
      ['branchLocation' , 'branchLocation' ], 
      ['restaurantId' , 'restaurantId' ], 
      ['closingTimeFormat' , 'closingTimeFormat' ], 
      ['abbreviated restaurantId' , 'abbreviatedRestaurantId' ], 
      ['branchType' , 'branchType' ], 
      ['workArea' , 'workArea' ], 
      ['restaurantClosingTime' , 'restaurantClosingTime' ], 
      ['storeId' , 'storeId' ], 
      ['defaultSalesFloorNo' , 'defaultSalesFloorNo' ], 
      ['storeAvailable' , 'storeAvailable'], 
      ['billTo' , 'billTo' ], 
      ['shipTo' , 'shipTo' ], 
      ['panNo' , 'panNo' ], 
      ['gstNo' , 'gstNo' ], 
      ['modified' , 'modified' ], 
      ['row_uuid' , 'row_uuid' ]
    ];
    this.convertBranchDataType(this.branchForm.value)
    const temp = {};
    keyData.forEach((key) => {
      let value = this.branchForm.value[key[1]];
      temp[key[0]] = value || '';
    });
    return temp
  }

  convertBranchDataType(jsonData){
    this.branchForm.setValue({
      tenantId: jsonData.tenantId,
      tenantName: jsonData.tenantName,
      branchName: jsonData.branchName,
      branchLocation: jsonData.branchLocation,
      restaurantId: jsonData.restaurantId,
      closingTimeFormat: jsonData.closingTimeFormat,
      abbreviatedRestaurantId: jsonData.abbreviatedRestaurantId,
      branchType: jsonData.branchType,
      workArea: jsonData.workArea,
      restaurantClosingTime: jsonData.restaurantClosingTime,
      storeId: jsonData.storeId,
      defaultSalesFloorNo: jsonData.defaultSalesFloorNo,
      storeAvailable: jsonData.storeAvailable,
      billTo: jsonData.billTo,
      shipTo: jsonData.shipTo,
      panNo: jsonData.panNo,
      gstNo: jsonData.gstNo,
      modified: jsonData.modified,
      row_uuid: jsonData.row_uuid,
    });
  }

  submitBranch(){
    this.isCreateButtonDisabled = true;
    this.isLoad = true;
    if(this.branchForm.invalid) {
      this.branchForm.markAllAsTouched();
      this.notify.snackBarShowError('Please fill out all required fields')
      this.isLoad = false;
      this.isCreateButtonDisabled = false;
      this.cd.detectChanges();
    }else {
      let updatedData = this.convertUserKeys();
      updatedData['modified'] = "yes";
      updatedData['row_uuid'] = "";
      updatedData['workArea'] = this.workAreaIds.join(',')
      if (Object.keys(this.baseData).length > 0) {
        let temp = {}
        temp['branches'] = this.baseData['branches'];
        temp['branches'].unshift(updatedData);
        temp['branches'] = temp['branches'].filter(item => item.modified === "yes");

        this.api.updateData({
          'tenantId' :  this.user.tenantId,
          'userEmail' : this.user.email,
          'data' : temp,
          'type' : 'user'
        }).pipe(first()).subscribe({
          next: (res) => {
            if (res['success']) {
              this.isLoad = false;
              this.notify.snackBarShowSuccess('Created successfully');
              this.close();
            }
          },
          error: (err) => {
            console.log(err);
          }
        });
      } else {
        this.isCreateButtonDisabled = false;
        this.isLoad = false;
      }
    }
  }

  updateBranch(){
    this.isUpdateButtonDisabled = true;
    this.baseData = this.sharedData.getBaseData().value;
    this.isLoad = true;
    if(this.branchForm.invalid) {
      this.branchForm.markAllAsTouched();
      this.notify.snackBarShowError('Please fill out all required fields')
      this.isLoad = false;
      this.isUpdateButtonDisabled = false;
      this.cd.detectChanges();
    }else {
      let updatedData = this.convertUserKeys();
      updatedData['modified'] = "yes";
      updatedData['workArea'] = this.workAreaIds.join(',')
      if (Object.keys(this.baseData).length > 0) {
        let temp = {}
        temp['branches'] = this.baseData['branches']
        let required = temp['branches'].find((el) => el.branchLocation == updatedData['branchLocation'])
        let index = temp['branches'].indexOf(required)
        temp['branches'][index] = updatedData;
        const uniqueData = temp['branches'].filter((user, index, self) =>
        index === self.findIndex((t) => (
          t.restaurantId === user.restaurantId 
        ))
      );
      temp['branches'] = uniqueData;
      temp['branches'] = temp['branches'].filter(item => item.modified === "yes"); 
        this.api.updateData({
          'tenantId' :  this.user.tenantId,
          'userEmail' : this.user.email,
          'data' : temp,
          'type' : 'user'
        }).pipe(first()).subscribe({
          next: (res) => {
            if (res['success']) {
              this.isLoad = false;
              this.cd.detectChanges();
              this.notify.snackBarShowSuccess('Updated successfully');
            }
          },
          error: (err) => { console.log(err) }
        });
      } else {
        this.isUpdateButtonDisabled = false;
        this.isLoad = false;
      }
    }
  }


  ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  Filter(bank, form, data) {
    if (!bank) {
      return;
    }
    let search = form.value;
    if (!search) {
      data.next(bank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    const filteredBank = bank.map(item => {
      const filteredWorkAreas = item.workAreas.filter(workArea => workArea.toLowerCase().indexOf(search) > -1);
      return { ...item, workAreas: filteredWorkAreas };
    });
    data.next(filteredBank);
  }

}
