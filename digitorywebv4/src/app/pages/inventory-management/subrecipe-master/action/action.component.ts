import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Inject,
  QueryList,
  TemplateRef,
  ViewChild,
  ViewChildren,
} from '@angular/core';
import { Renderer2, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  AbstractControl,
  FormBuilder,
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { InventoryService } from 'src/app/services/inventory.service';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatNativeDateModule, MatOption } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSliderModule } from '@angular/material/slider';
import { MatSelect, MatSelectChange, MatSelectModule } from '@angular/material/select';
import { MatRadioModule } from '@angular/material/radio';
import {
  Observable,
  Subject,
  first,
  map,
  of,
  startWith,
  takeUntil,
} from 'rxjs';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatDividerModule } from '@angular/material/divider';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { AutocompleteComponent } from '../../../../components/autocomplete/autocomplete.component';
import { ShareDataService } from 'src/app/services/share-data.service';
import { ReplaySubject } from 'rxjs';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatToolbarModule } from '@angular/material/toolbar';
import { NotificationService } from 'src/app/services/notification.service';
import { AuthService } from 'src/app/services/auth.service';
import { MatSort } from '@angular/material/sort';
import { MatStepper, MatStepperModule } from '@angular/material/stepper';
import { NgxSkeletonLoaderModule } from 'ngx-skeleton-loader';
import { MasterDataService } from 'src/app/services/master-data.service';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatChipsModule } from '@angular/material/chips';
import { A11yModule } from '@angular/cdk/a11y';
import { SessionCacheService } from 'src/app/services/session-cache.service';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { EmptyStateComponent } from 'src/app/components/empty-state/empty-state.component';

@Component({
  selector: 'app-action',
  standalone: true,
  templateUrl: './action.component.html',
  styleUrls: ['./action.component.scss'],
  imports: [
    FormsModule,
    MatDialogModule,
    MatChipsModule,
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatNativeDateModule,
    MatDatepickerModule,
    MatInputModule,
    MatSliderModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatSelectModule,
    MatRadioModule,
    MatAutocompleteModule,
    MatDividerModule,
    AutocompleteComponent,
    NgxMatSelectSearchModule,
    MatTableModule,
    MatTooltipModule,
    MatToolbarModule,
    MatStepperModule,
    NgxSkeletonLoaderModule,
    MatPaginatorModule,
    A11yModule,
    MatSlideToggleModule,
    EmptyStateComponent
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ActionComponent {
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild('stepper', { static: false }) private stepper: MatStepper;
  @ViewChild('openPortionDialog') openPortionDialog: TemplateRef<any>;
  @ViewChild('openUnitCostDialog') openUnitCostDialog: TemplateRef<any>;
  @ViewChild('openDeleteDialog') openDeleteDialog: TemplateRef<any>;
  @ViewChildren(MatOption) matOptions: QueryList<MatOption>;
  @ViewChild('widgetsContent', { read: ElementRef }) public widgetsContent;
  @ViewChild(MatSort) sort!: MatSort;
  question = 'Would you like to add "';
  itemNameOptions: Observable<string[]>;
  itemNameControl = new FormControl('');
  registrationForm!: FormGroup;
  subRecipeRecipeForm!: FormGroup;
  public isUpdateActive: boolean = false;
  categories: any;
  subCategories: any;
  isDuplicate: boolean;
  costDialogkey: boolean = false;
  preparedAtLocation: any[];
  usedAtOutletLocation: any[];
  dataSource = new MatTableDataSource<any>([]);
  displayedColumns: string[];
  rest = new FormControl();
  subRecipeData = [];
  showSRR: boolean = false;
  updateSRR: boolean = false;
  isReadOnly: boolean = true;
  loadBtn: boolean = false;
  dropDownData: any;
  AccessibleUOM: any[] = [];
  ingredientUOM = ['GM', 'ML', 'NOS','MM', 'PORTION'] ;
  filteredData: any[];
  subcategories: any;
  ingredientData: any;
  public Bank: any[] = [];
  public IngredientFilterCtrl: FormControl = new FormControl();
  public ingredientNames: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public usedInWorkAreaBank: any[] = [];
  public usedInWorkAreaFilterCtrl: FormControl = new FormControl();
  public usedInWorkAreaNames: ReplaySubject<any[]> = new ReplaySubject<any[]>(
    1
  );
  public preparedBank: any[] = [];
  public preparedFilterCtrl: FormControl = new FormControl();
  public preparedLocationNames: ReplaySubject<any[]> = new ReplaySubject<any[]>(
    1
  );
  public outletBank: any[] = [];
  public outletFilterCtrl: FormControl = new FormControl();
  public outletLocationNames: ReplaySubject<any[]> = new ReplaySubject<any[]>(
    1
  );
  catBank: Observable<string[]>;
  subCatBank: Observable<string[]>;
  ingredientNamesOptions: Observable<string[]>;
  protected _onDestroy = new Subject<void>();
  dialogRef: MatDialogRef<any>;
  baseData: object = {};
  user: any;
  updateBtnActive: boolean = false;
  loadSpinnerForApi: boolean = false;
  loadSpinnerForApiSRR: boolean = false;
  newCategory: any;
  catAndsubCat: any;
  newSubCategory: any;
  closeSRRRef: MatDialogRef<unknown, any>;
  isEditable = false;
  isSRRDataReady: boolean = false;
  units: string[] = ['Portion', 'GM/ML'];
  invItems: any;
  ratio: number = 1;
  locationList: any;
  globalLocation: any;
  loadSrmBtn: boolean = true;
  loadSrrBtn: boolean = true;
  isButtonDisabled = false;
  showWeightError: boolean = false;
  showDeleteItems: boolean = false;
  discontinueDatas: any[] = [];
  tempData: any[];
  removedItem: any = [];
  discData: any=[];
  showWorkAreaError: boolean;
  logo: any;
  status: string;
  subData: any;
  filtered: string[];
  disableOption: boolean = false;
  @ViewChild('discontinuedSelectDialog') discontinuedSelectDialog: TemplateRef<any>;
  selectedDropDown: any;
  selectedData: string;
  discontinuedPreLocData: any = [];
  defaultPreLocData: any = [];
  discontinuedOutletData: any = [];
  defaultOutletData: any = [];
  discontinuedIssuedToData: any = [];
  defaultIssuedToData: any = [];
  discontinuedLocations: any;
  groupData: any;
  branchesData: any;
  restaurantFilterCtrl: FormControl = new FormControl();
  filteredRestaurantOptions: any[] = [];
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    private fb: FormBuilder,
    private api: InventoryService,
    private activatedRoute: ActivatedRoute,
    private router: Router,
    public dialog: MatDialog,
    private sharedData: ShareDataService,
    private cache: SessionCacheService,
    @Inject(MAT_DIALOG_DATA) public dialogData: any,
    public notify: NotificationService,
    private renderer: Renderer2,
    private el: ElementRef,
    private auth: AuthService,
    private cd: ChangeDetectorRef,
    private masterDataService: MasterDataService
  ) {
    this.globalLocation = this.sharedData.getGlLocation().value;
    this.user = this.auth.getCurrentUser();
    this.branchesData = this.user.restaurantAccess;
    this.rest.setValue(this.globalLocation) ;
    this.filteredRestaurantOptions = this.branchesData;

    // listen to search field value changes
    this.restaurantFilterCtrl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe(() => {
        this.filterRestaurants();
      });

    this.baseData = this.sharedData.getBaseData().value;
    this.newCategory = this.baseData['Subrecipe Master'].map(cat => cat.category.toUpperCase());
    let tenantId = this.user.tenantId
      this.api.getRolesListDiscontinuedLocations(tenantId)
      .subscribe((res) => {
        if(res['result'] == 'success' && res['discontinuedLocations']){
          this.discontinuedLocations = res['discontinuedLocations'];
        }
        this.cd.detectChanges();
      });
    this.registrationForm = this.fb.group({
      category: ['', Validators.required],
      subCategory: ['', Validators.required],
      menuItemCode: ['', Validators.required],
      menuItemName: ['', Validators.required],
      closingUOM: ['', Validators.required],
      itemType: ['SubRecipe', Validators.required],
      preparedAt: ['', Validators.required],
      usedAtOutlet: ['', Validators.required],
      usedInWorkArea: ['', Validators.required],
      uom: ['', Validators.required],
      weightInUse: [0, Validators.required],
      yield: [1, [Validators.required, this.yieldValidator()]],
      portion: [1, [Validators.required, this.portionValidator()]],
      unit: ['Portion', [Validators.required]],
      recovery: [0, Validators.required],
      rate: [0, Validators.required],
      finalRate: [0],
      discontinued: ['no', Validators.required],
      row_uuid: [''],
    });
    this.registrationForm.get('yield').markAsTouched();
    this.registrationForm.get('yield').markAsDirty();
    this.subRecipeRecipeForm = this.fb.group({
      subRecipeCode: ['', Validators.required],
      subRecipeName: ['', Validators.required],
      ingredientCode: ['', Validators.required],
      ingredientName: ['', Validators.required],
      uom: ['', Validators.required],
      initialWeight: [0],
      defaultUOM: ['', Validators.required],
      portionCount: [0],
      yield: [1, [Validators.required, this.yieldValidator()]],
      loss: [0],
      weightInUse: [0, Validators.required],
      rate: [0, Validators.required],
      finalRate: [0],
      discontinued: ['no'],
      row_uuid: [''],
    });

    this.isDuplicate = this.dialogData.key;
    this.costDialogkey = this.dialogData.costDialogkey;
    this.sharedData.getItemNames.subscribe((obj) => {
      this.itemNameOptions = this.itemNameControl.valueChanges.pipe(
        startWith(''),
        map((value) => this._filter(value || '', obj.menuItemName , ''))
      );
      this.preparedAtLocation = obj.locations;
      this.usedAtOutletLocation = obj.workAreas;
    });
    this.getMenuRecipes();
    this.getCategories();
    this.readIPConfig();
  }

  yieldValidator(): ValidatorFn {
    return (control: AbstractControl): { [key: string]: any } | null => {
      const isValid = control.value > 0;
      return isValid ? null : { yieldInvalid: { value: control.value } };
    };
  }

  portionValidator(): ValidatorFn {
    return (control: AbstractControl): { [key: string]: any } | null => {
      const isValid = control.value > 0;
      return isValid ? null : { portionInvalid: { value: control.value } };
    };
  }

  reflectSubRecipe() {
    let weightInUse = this.getTotal('Initialweight');
    this.registrationForm.get('weightInUse').setValue(weightInUse);
    let recovery = this.registrationForm.value['yield'] * weightInUse;
    this.registrationForm.get('recovery').setValue(this.notify.truncateAndFloor(recovery));
    let rate = this.getSRRTotal('finalRate');
    this.registrationForm.get('rate').setValue(rate);
    let finalRate = rate / recovery
    this.registrationForm.get('finalRate').setValue(finalRate);
    this.cd.detectChanges();
  }

  getMenuRecipes() {
    let currentInvItems = this.cache.getInvItems().value;
    if (
      currentInvItems.hasOwnProperty(this.globalLocation['restaurantIdOld'])
    ) {
      this.invItems = currentInvItems[this.globalLocation['restaurantIdOld']];
      // this.Bank = this.invItems.map((item) => item.itemName);
      this.subData = this.invItems
        .filter((item) => item.itemName && item.ItemType === "SubRecipe")
        .map((item) => item.itemName);
      let invData = this.invItems
        .filter((item) => item.itemName && item.ItemType === "Inventory")
        .map((item) => item.itemName);

      this.Bank = [...this.subData, ...invData]
      this.ingredientData = [...new Set(this.Bank)];
      this.ingredientNamesOptions = this.subRecipeRecipeForm.get('ingredientName').valueChanges.pipe(startWith(''), map(value => this._filter((value || ''), this.ingredientData , 'ingredients')));
      this.ingredientNames.next(this.Bank.slice());
      this.IngredientFilterCtrl.valueChanges
        .pipe(takeUntil(this._onDestroy))
        .subscribe(() => {
          this.Filter(
            this.Bank,
            this.IngredientFilterCtrl,
            this.ingredientNames
          );
        });
        this.getLocationCall();
    } else {
      let obj = {};
      obj['tenantId'] = this.user.tenantId;
      // obj['restaurantId'] = this.globalLocation['restaurantIdOld'];
      obj['restaurantId'] = this.rest.value['restaurantIdOld'];
      this.api.getInventoryListForSubrecipeMD(obj).subscribe({
        next: (res) => {
          if (res['success']) {
            // currentInvItems[this.globalLocation['restaurantIdOld']] =
            //   res['invList'];
            // this.cache.setInvItems(currentInvItems);
            this.invItems = res['invList'];
            this.subData = this.invItems
              .filter((item) => item.itemName && item.ItemType === "SubRecipe")
              .map((item) => item.itemName);

            let invData = this.invItems
              .filter((item) => item.itemName && item.ItemType === "Inventory")
              .map((item) => item.itemName);

            this.Bank = [...this.subData, ...invData]
            this.ingredientData = [...new Set(this.Bank)];
            this.ingredientNamesOptions = this.subRecipeRecipeForm.get('ingredientName').valueChanges.pipe(startWith(''), map(value => this._filter((value || ''), this.ingredientData , 'ingredients')));
            this.ingredientNames.next(this.Bank.slice());
            this.IngredientFilterCtrl.valueChanges
              .pipe(takeUntil(this._onDestroy))
              .subscribe(() => {
                this.Filter(
                  this.Bank,
                  this.IngredientFilterCtrl,
                  this.ingredientNames
                );
              });
           this.getLocationCall();
          }
        },
        error: (err) => {
          console.log(err);
        },
      });
    }
  }

  ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  protected Filter(bank, form, data) {
    if (!bank) {
      return;
    }
    let search = form.value;
    if (!search) {
      data.next(bank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    data.next(bank.filter((data) => data.toLowerCase().indexOf(search) > -1));
  }

  protected FilterIssued(bank, form, data) {
    if (!bank) {
      return;
    }
    let search = form.value;

    if (!search) {
      data.next(bank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    const filteredBank = bank.map((item) => {
      const filteredWorkAreas = item.workAreas.filter(
        (workArea) => workArea.toLowerCase().indexOf(search) > -1
      );
      return { ...item, workAreas: filteredWorkAreas };
    });
    data.next(filteredBank);
  }

  submit() {
    this.loadSpinnerForApi = true;
    this.showWorkAreaError = false;
    this.baseData = this.sharedData.getBaseData().value;
    if (this.registrationForm.invalid) {
      this.registrationForm.markAllAsTouched();
      this.notify.snackBarShowError('Please fill out all required fields');
      this.loadSpinnerForApi = false;
      this.cd.detectChanges();
    } else {
      // this.checkWorkArea();
      if(this.showWorkAreaError){
        this.registrationForm.markAllAsTouched();
        this.notify.snackBarShowError('Please fill out all required fields')
        this.loadSpinnerForApi = false;
        this.cd.detectChanges();
      }else{
        let updatedSRMData = this.convertSRMKeys();
        updatedSRMData['modified'] = 'yes';
        updatedSRMData['preparedAt'] = updatedSRMData['preparedAt'].join(',');
        updatedSRMData['usedAtOutlet'] = updatedSRMData['usedAtOutlet'].join(',');
        updatedSRMData['usedInWorkArea'] =
          updatedSRMData['usedInWorkArea'].join(',');
        let data = this.baseData;
        if (Object.keys(this.baseData).length > 0) {
          let tempObj = {};
          tempObj['Subrecipe Master'] = data['Subrecipe Master'];
          let requiredVendor = tempObj['Subrecipe Master'].find(
            (el) => el.menuItemCode == updatedSRMData['menuItemCode']
          );
          if (requiredVendor) {
            this.notify.snackBarShowInfo('Item code already used');
            this.loadSpinnerForApi = false;
            this.cd.detectChanges();
          } else {
            this.dataSource.data.forEach((el) => {
              el['Initialweight'] = el['weightInUse'] / el['yield'];
            });
            tempObj['Subrecipe Master'].unshift(updatedSRMData);
            tempObj['Subrecipe Master'] = tempObj['Subrecipe Master'].filter(
              (item) => item.modified === 'yes'
            );
            tempObj['Subrecipe Recipe'] = this.dataSource.data;
            let obj = {};
            obj['tenantId'] = this.user.tenantId;
            obj['userEmail'] = this.user.email;
            obj['data'] = tempObj;
            obj['type'] = 'recipe';
            obj['category'] = 'subRecipe';
            this.api
              .updateData(obj)
              .pipe(first())
              .subscribe({
                next: (res) => {
                  if (res['success']) {
                    this.updateBaseDataForSRR() ;
                    this.loadSpinnerForApi = false;
                    this.close();
                    this.cd.detectChanges();
                    this.notify.snackBarShowSuccess(
                      'Sub-recipe added successfully'
                    );
                  }
                },
                error: (err) => {
                  console.log(err);
                },
              });
          }
        } else {
          this.loadSpinnerForApi = false;
          this.cd.detectChanges();
          this.notify.snackBarShowError('Something went wrong!');
        }
        this.subRecipeRecipeForm.patchValue({
          subRecipeCode: updatedSRMData['menuItemCode'],
          subRecipeName: updatedSRMData['menuItemName'],
        });
      }

    }
  }

  selectIngredientsName(itemName) {
    const item = this.invItems.find((item) => {
      return item.itemName === itemName;
    });
    let uom;
    if (item['uom'] == 'KG' || item['uom'] == 'kg' || item['uom'] == 'Kg') {
      uom = 'GM';
    } else if (
      item['uom'] == 'LITRE' ||
      item['uom'] == 'litre' ||
      item['uom'] == 'Litre'
    ) {
      uom = 'ML';
    } else if (
      item['uom'] == 'NOS' ||
      item['uom'] == 'nos' ||
      item['uom'] == 'Nos'
    ) {
      uom = 'NOS';
    } else {
      uom = 'MM';
    }
    this.AccessibleUOM = item['ItemType'] === 'SubRecipe' && item['portionWeight'] != 0 ? [uom,"PORTION"] : [uom]
    let conversionCoefficient;
    conversionCoefficient = item['uom'] == 'NOS' ? 1 : 1000;
    let rate = item
      ? ((item.hasOwnProperty('packageQty') ? item['packageQty'] : 1) /
          conversionCoefficient) *
        item['withTaxPrice']
      : 0;
    this.subRecipeRecipeForm.patchValue({
      weightInUse: 0,
      ingredientCode: item['itemCode'],
      uom: uom,
      defaultUOM: uom,
      yield: item['yield'] ?? 1,
      rate: this.notify.truncateAndFloor(rate),
      finalRate: 0,
    });
  }

  isOptionAccessible(option: string): boolean {
    return this.AccessibleUOM.includes(option);
  }

  uomChange(){
    this.subRecipeRecipeForm.get('portionCount').setValue(1) ;
    this.getPortionWeightForSubRecipe()
  }

  getPortionWeightForSubRecipe(){
    let requiredItem = this.invItems.find(
      (item) => item.itemCode == this.subRecipeRecipeForm.value.ingredientCode
    );
    let portionWeight = this.notify.truncateAndFloor(requiredItem['portionWeight']) * this.subRecipeRecipeForm.value.portionCount
    this.subRecipeRecipeForm.get('weightInUse').setValue(portionWeight);
    this.sumForFinalRateSRR('Total');
  }

  convertPortionToUOM(){
    this.getPortionWeightForSubRecipe() ;
    this.cd.detectChanges() ;
  }

  update() {
    this.deleteDataFormDB();
    this.loadSpinnerForApi = true;
    this.showWorkAreaError = false;
    this.baseData = this.sharedData.getBaseData().value;
    if (this.registrationForm.invalid) {
      this.registrationForm.markAllAsTouched();
      this.notify.snackBarShowError('Please fill out all required fields');
      this.loadSpinnerForApi = false;
      this.cd.detectChanges();
    } else {
      // this.checkWorkArea();
      if(this.showWorkAreaError){
        this.registrationForm.markAllAsTouched();
        this.notify.snackBarShowError('Please fill out all required fields')
        this.loadSpinnerForApi = false;
        this.cd.detectChanges();
      }else{
        this.setDiscontinuedDataInRolopos();
        let updatedSRMData = this.convertSRMKeys();
        updatedSRMData['modified'] = 'yes';
        updatedSRMData['preparedAt'] = updatedSRMData['preparedAt'].join(',');
        updatedSRMData['usedAtOutlet'] = updatedSRMData['usedAtOutlet'].join(',');
        updatedSRMData['usedInWorkArea'] =
        updatedSRMData['usedInWorkArea'].join(',');
        updatedSRMData['preparedAtDiscontinued'] = this.discontinuedPreLocData.length > 0 ? this.discontinuedPreLocData.join(',') : '';
        updatedSRMData['usedAtOutletDiscontinued'] = this.discontinuedOutletData.length > 0 ? this.discontinuedOutletData.join(',') : '';
        if(this.discontinuedIssuedToData.length > 0){
          const workAreas = this.discontinuedIssuedToData.flatMap(item => item.workAreas);
          // updated['issuedToDiscontinued'] = workAreas.length > 0 ? workAreas.join(',') : '';
          updatedSRMData['issuedToDiscontinued'] = workAreas > 0 ? workAreas.join(',') : '';
        }
        if (Object.keys(this.baseData).length > 0) {
          let tempObj = {};
          tempObj['Subrecipe Master'] = this.baseData['Subrecipe Master'];
          let requiredVendor = tempObj['Subrecipe Master'].find(
            (el) => el.menuItemCode == updatedSRMData['menuItemCode']
          );
          let index = tempObj['Subrecipe Master'].indexOf(requiredVendor);
          this.dataSource.data.forEach((el) => {
            el.UOM = el.defaultUOM ;
            el['Initialweight'] = el['weightInUse'] / el['yield'];
          });
          tempObj['Subrecipe Master'][index] = updatedSRMData;
          tempObj['Subrecipe Master'] = tempObj['Subrecipe Master'].filter(
            (item) => item.modified === 'yes'
          );
          this.dataSource.data = [...this.dataSource.data, ...this.removedItem, ...this.discData];
          tempObj['Subrecipe Recipe'] = this.dataSource.data;
          this.dataSource.data = this.dataSource.data.filter(item => item.delete !== true);
          this.dataSource.data = this.dataSource.data.filter(item => item.Discontinued !== 'yes');
          this.baseData['Subrecipe Recipe'].filter((el) => el['ingredientCode'] === updatedSRMData['menuItemCode'] && (!['', null,undefined].includes(el.portionCount))).forEach((item) => {
            let portionWeight = (this.registrationForm.value.recovery * (1 / this.registrationForm.value.portion))
            item['weightInUse'] = this.notify.truncateAndFloor(item['portionCount'] * portionWeight) ;
            item['Initialweight'] = this.notify.truncateAndFloor(item['weightInUse'] /this.notify.truncateAndFloor( item['yield']));
            item['modified'] = 'yes';
            this.updateSubRecipeMaster(item['subRecipeCode']) ;
          })

          this.baseData['menu recipes'].filter((el) => el['ingredientCode'] === updatedSRMData['menuItemCode'] && (!['', null,undefined].includes(el.portionCount))).forEach((item) => {
            let portionWeight = (this.registrationForm.value.recovery * (1 / this.registrationForm.value.portion))
            item['weightInUse'] = this.notify.truncateAndFloor(item['portionCount'] * portionWeight) ;
            item['InitialWeight'] = this.notify.truncateAndFloor(item['weightInUse'] /this.notify.truncateAndFloor( item['Yield']));
            item['modified'] = 'yes';
            this.updateMenuMaster(item['menuItemCode']) ;
          })
          let updatedMenuMaster,updatedMenuRecipe,updatedSubRecipeMaster,updatedSubRecipeRecipe
          updatedMenuRecipe = this.baseData['menu recipes'].filter((el) => el['modified'] === 'yes' )
          updatedMenuMaster = this.baseData['menu master'].filter((el) => el['modified'] === 'yes' )
          updatedSubRecipeMaster = this.baseData['Subrecipe Master'].filter((el) => el['modified'] === 'yes' && el['menuItemCode'] != updatedSRMData['menuItemCode'])
          updatedSubRecipeRecipe = this.baseData['Subrecipe Recipe'].filter((el) => el['modified'] === 'yes' )
          tempObj['menu recipes'] = updatedMenuRecipe;
          tempObj['menu master'] = updatedMenuMaster;
          tempObj['Subrecipe Master'] = tempObj['Subrecipe Master'].concat(updatedSubRecipeMaster);
          tempObj['Subrecipe Recipe'] = tempObj['Subrecipe Recipe'].concat(updatedSubRecipeRecipe);
          let obj = {};
          obj['tenantId'] = this.user.tenantId;
          obj['userEmail'] = this.user.email;
          obj['data'] = tempObj;
          obj['type'] = 'recipe';
          obj['category'] = 'subRecipe';
          this.api
            .updateData(obj)
            .pipe(first())
            .subscribe({
              next: (res) => {
                if (res['success']) {
                  this.loadSpinnerForApi = false;
                  this.cd.detectChanges();
                  this.updateBaseData(tempObj) ;
                  this.updateBaseDataForSRR() ;
                  this.notify.snackBarShowSuccess(
                    'Sub-recipe updated successfully'
                  );
                  this.close();
                }
              },
              error: (err) => {
                console.log(err);
              },
            });
        } else {
          this.loadSpinnerForApi = false;
        }
      }

    }
  }

  updateBaseDataForSRR() {
    this.baseData = this.sharedData.getBaseData().value;
    let obj = {};
    obj['tenantId'] = this.user.tenantId;
    obj['userEmail'] = this.user.email;
    obj['type'] = 'recipe';
    obj['specific'] = 'Subrecipe Recipe';
    if (this.registrationForm.value) {
      obj['itemCode'] = this.registrationForm.value.menuItemCode;
    }
    this.api.getPresentData(obj).subscribe({
      next: (res) => {
        if (res['success']) {
          let latestSRR = res['data'][0] ?? res['data'];
          let currentSRR = this.baseData['Subrecipe Recipe']
          latestSRR['Subrecipe Recipe'].forEach(item => {
            const exist = currentSRR.findIndex(el => el.ingredientCode == item['ingredientCode'] && el['subRecipeCode'] === item['subRecipeCode']
            );
            if (exist !== -1) {
              currentSRR[exist] = item;
            } else {
              currentSRR.push(item);
            }
          })
          this.baseData['Subrecipe Recipe'] = currentSRR;
          this.sharedData.setBaseData(this.baseData) ;
        }
      },
      error: (err) => {
        console.log(err);
      },
    });
  }

  updateMenuMaster(itemCode){
    const index = this.baseData['menu master'].findIndex(item => item['menuItemCode'] === itemCode);
    let revisedTotalWeight,requiredItems
    requiredItems = this.baseData['menu recipes'].filter((item) => item['menuItemCode'] === itemCode) ;
    revisedTotalWeight = requiredItems.reduce((sum, item) => {
      return sum + this.notify.truncateAndFloor(item.weightInUse);
    }, 0);
    let requiredData = this.baseData['menu master'].find((el) => el['menuItemCode'] == itemCode)
    if(requiredData){
      requiredData['weight'] = revisedTotalWeight ;
      requiredData['modified'] = 'yes'
      if (index !== -1) {
        this.baseData['menu master'][index] = requiredData
      }
    }
  }

  updateSubRecipeMaster(itemCode){
    const index = this.baseData['Subrecipe Master'].findIndex(item => item['menuItemCode'] === itemCode);
    let revisedTotalWeight,requiredItems
    requiredItems = this.baseData['Subrecipe Recipe'].filter((item) => item['subRecipeCode'] === itemCode) ;
    revisedTotalWeight = requiredItems.reduce((sum, item) => {
      return sum + this.notify.truncateAndFloor(item.weightInUse);
    }, 0);
    let requiredData = this.baseData['Subrecipe Master'].find((el) => el['menuItemCode'] == itemCode)
    if(requiredData){
      requiredData['weightInUse'] = revisedTotalWeight ;
      requiredData['recovery'] = revisedTotalWeight * requiredData['yield'];
      requiredData['modified'] = 'yes'
      if (index !== -1) {
        this.baseData['Subrecipe Master'][index] = requiredData
      }
    }
  }


  // ######################## AUTO COMPLETION ########################

  _filter(value: string, input: string[], data): string[] {
    const cleanedValue = this.cleanSpaces(value);
    let filterValue = cleanedValue.toLowerCase();
    
    this.filtered = input.filter((option) =>
      option.toLowerCase().includes(filterValue)
    );
    if (this.filtered.length == 0) {
      if (data == 'ingredients') {
        // this.disableOption = true;
        this.filtered = ['No Item Found'];
      } else {
        this.filtered = [this.question + cleanedValue + '"'];
      }
    }
    return this.filtered;
  }

  checkItem(event) {
    let invItem = this.sharedData.getDataForFillTheForm(
      event.target.value,
      'Subrecipe Master'
    );
    if (invItem) {
      this.updateBtnActive = true;
    } else {
      this.updateBtnActive = false;
    }
  }

  cleanSpaces(str: string): string {
    return str ? str.trim().replace(/\s+/g, ' ') : '';
  }

  optionSelected(type: string, option: any) {
    let invItem = this.sharedData.getDataForFillTheForm(
      option.value,
      'Subrecipe Master'
    );
    if (invItem) {
      this.updateBtnActive = true;
    } else {
      this.updateBtnActive = false;
    }
    if (option.value.indexOf(this.question) === 0) {
      this.addOption(type);
    }
  }

  addOption(type: string) {
    this.loadBtn = true;
    if (type == 'package') {
      this.itemNameControl.reset();
    } else if (type == 'Subrecipe Master') {
      const cleanedValue = this.cleanSpaces(this.itemNameControl.value);
      let subRecipeMasterItem = this.sharedData.getDataForFillTheForm(
        cleanedValue,
        'Subrecipe Master'
      );
      if (subRecipeMasterItem) {
        this.isUpdateActive = true;
        this.setValuesForForm(subRecipeMasterItem);
      } else {
        this.generateCode('subrecipeMasterCode');
      }
      this.registrationForm.controls['menuItemName'].patchValue(
        this.removePromptFromOption(cleanedValue)
      );
      this.itemNameControl.reset();
      this.isDuplicate = false;
    }
    this.dataSource.data = [];
    this.loadBtn = false;
    this.getBaseData();
  }

  generateCode(code) {
    let obj = {};
    let data;
    obj['tenantId'] = this.user.tenantId;
    obj['code'] = code;
    this.api
      .getCode(obj)
      .pipe(first())
      .subscribe({
        next: async (res) => {
          if (res['success']) {
            data = res['data'];
            this.registrationForm.get('menuItemCode').setValue(data);
          }
        },
      });
  }

  setValuesForForm(subRecipeMasterItem) {
    if (
      !Array.isArray(subRecipeMasterItem['usedInWorkArea']) &&
      !Array.isArray(subRecipeMasterItem['usedAtOutlet']) &&
      !Array.isArray(subRecipeMasterItem['preparedAt'])
    ) {
      subRecipeMasterItem['usedInWorkArea'] =
        subRecipeMasterItem['usedInWorkArea'].split(',');
      subRecipeMasterItem['usedAtOutlet'] =
        subRecipeMasterItem['usedAtOutlet'].split(',');
      subRecipeMasterItem['preparedAt'] =
        subRecipeMasterItem['preparedAt'].split(',');
    }
    let recovery = this.notify.truncateAndFloor(subRecipeMasterItem['recovery'])
    let portion = subRecipeMasterItem.hasOwnProperty('portion') && subRecipeMasterItem['portion'] ? subRecipeMasterItem['portion'] : 1 ;

    this.defaultPreLocData = subRecipeMasterItem['preparedAt']
    this.defaultOutletData = subRecipeMasterItem['usedAtOutlet']
    this.defaultIssuedToData = subRecipeMasterItem['usedInWorkArea']
    if((this.discontinuedLocations && this.discontinuedLocations != undefined ) && this.discontinuedLocations.subRecipeLocations){
      this.discontinuedOutletData.push(...this.discontinuedLocations.subRecipeLocations.outLetDiscontinued)
      this.discontinuedIssuedToData.push(...this.discontinuedLocations.subRecipeLocations.issuedToDiscontinued)
      this.discontinuedPreLocData.push(...this.discontinuedLocations.subRecipeLocations.procuredAtDiscontinued)
    }
    this.registrationForm.patchValue({
      category: subRecipeMasterItem['category'],
      subCategory: subRecipeMasterItem['subCategory'],
      menuItemCode: subRecipeMasterItem['menuItemCode'],
      menuItemName: subRecipeMasterItem['menuItemName'],
      closingUOM: subRecipeMasterItem['closingUOM'],
      itemType: 'SubRecipe',
      preparedAt: subRecipeMasterItem['preparedAt'],
      usedAtOutlet: subRecipeMasterItem['usedAtOutlet'],
      uom: subRecipeMasterItem['UOM'],
      weightInUse: subRecipeMasterItem['weightInUse'],
      portion: portion,
      yield: subRecipeMasterItem['yield'],
      recovery: recovery,
      rate: this.notify.truncateAndFloor(subRecipeMasterItem['rate']),
      finalRate: this.notify.truncateAndFloor(subRecipeMasterItem['finalRate']),
      discontinued: ['no','NO' ,'No', 'N', null,''].includes(subRecipeMasterItem['Discontinued']) ? 'no' : 'yes',
      row_uuid: subRecipeMasterItem['row_uuid'],
    });

    this.loadSrmBtn = false;
    this.locationChange(subRecipeMasterItem['usedAtOutlet']);
    this.registrationForm
      .get('usedInWorkArea')
      .patchValue(subRecipeMasterItem['usedInWorkArea']);
  }

  removePromptFromOption(option) {
    if (option.startsWith(this.question)) {
      option = option.substring(this.question.length, option.length - 1);
    }
    return option;
  }

  close() {
    this.dataSource.data = [];
    this.masterDataService.setNavigation('Subrecipe Master');
    this.router.navigate(['/dashboard/home']);
    this.dialog.closeAll();
  }

  editFun(element, addSRR) {
    const item = this.invItems.find(
      (item) =>
        item.itemName.toLowerCase() === element.ingredientName.toLowerCase()
    );
    this.updateSRR = true;
    this.subRecipeRecipeForm.patchValue({
      ingredientCode: element['ingredientCode'],
      // ingredientName: item ? item.ingredientName : '',
      ingredientName: element['ingredientName'],
      subRecipeCode: element['subRecipeCode'],
      subRecipeName: element['subRecipeName'],
      uom: element['UOM'],
      defaultUOM: element['defaultUOM'],
      yield: element['yield'],
      rate: this.notify.truncateAndFloor(element['rate']) ,
      finalRate: this.notify.truncateAndFloor(element['finalRate']),
      initialWeight: element['Initialweight'],
      loss: element['loss'],
      weightInUse: element['weightInUse'],
      discontinued:['no','NO' ,'No', 'N', null,''].includes(element['Discontinued']) ? 'no' : 'yes',
      row_uuid: element['row_uuid'],
    });

    if (element.hasOwnProperty('portionCount')) {
      this.subRecipeRecipeForm.get('portionCount').setValue(element['portionCount']);
    }

     if (!element['Initialweight']) {
      this.subRecipeRecipeForm.get('initialWeight').setValue(0);
    }

    if (!element['loss']) {
      this.subRecipeRecipeForm.get('loss').setValue(0);
    }

    let dialogRef = this.dialog.open(addSRR, {
      maxHeight: '95vh',
      maxWidth: '50vw',
    });
    dialogRef.afterClosed().subscribe((result) => {});
    this.closeSRRRef = dialogRef;
    this.loadSrrBtn = false;
  }

  closeSRRDialog() {
    this.updateSRR = false;
    // this.subRecipeRecipeForm.reset();
    this.closeSRRRef.close();
  }

  addNewSubRecipeRecipe() {
    this.subRecipeRecipeForm.patchValue({
      subRecipeCode: this.registrationForm.value.menuItemCode,
      subRecipeName: this.registrationForm.value.menuItemName,
    });
    if (this.subRecipeRecipeForm.invalid) {
      this.subRecipeRecipeForm.markAllAsTouched();
      this.notify.snackBarShowError('Please fill out all required fields');
      this.cd.detectChanges();
    } else {
      if (this.subRecipeRecipeForm.value.weightInUse > 0) {
        let update = this.convertSRRKeys();
        update['modified'] = 'yes';
        let existingItem = this.dataSource.data.find(
          (el) => el.ingredientCode === update['ingredientCode']
        );
        if (!existingItem) {
          Object.entries(update).forEach(([key, value]) => {
            if (value === null || value === undefined || value === '') {
                return;
            }
            if (typeof value === 'number') {
              update[key] = this.notify.truncateAndFloor(value);
            }
          });
          this.dataSource.data.unshift(update);
          this.dataSource.paginator = this.paginator;
          this.updateSRR = false;
          this.reflectSubRecipe();
          // this.notify.snackBarShowSuccess('Created successfully');
          this.subRecipeRecipeForm.reset();
          this.subRecipeRecipeForm.patchValue({
            subRecipeCode: this.registrationForm.value.menuItemCode,
            subRecipeName: this.registrationForm.value.menuItemName,
            yield: 1
          });
          this.tempData = this.dataSource.data
          this.clearForm();
        } else {
          this.notify.snackBarShowWarning('Ingredient already exists!');
        }
      } else {
        this.subRecipeRecipeForm.markAllAsTouched();
        this.showWeightError = true;
        // this.notify.snackBarShowError('Please fill out all required fields');
        this.cd.detectChanges();
      }
      this.cd.detectChanges();
    }
    this.clearForm();
  }

  clearForm() {
    Object.keys(this.subRecipeRecipeForm.controls).forEach(key => {
      this.subRecipeRecipeForm.get(key)?.setErrors(null);
    });
  }

  editExistingSubRecipeRecipe() {
    if (this.subRecipeRecipeForm.invalid) {
      this.subRecipeRecipeForm.markAllAsTouched();
      this.notify.snackBarShowError('Please fill out all required fields');
      this.cd.detectChanges();
    } else {
      let updatedSRRData = this.convertSRRKeys();
      updatedSRRData['modified'] = 'yes';
      let requiredPackage = this.dataSource.data.find(
        (el) =>
          el.ingredientCode == updatedSRRData['ingredientCode'] &&
          el.subRecipeCode == updatedSRRData['subRecipeCode']
      );
      if (requiredPackage) {
        let data = this.tempData.filter(item => item.Discontinued !== 'yes' )
        let tempDiscData : any = []
        tempDiscData = this.discData.filter(el =>
          el.ingredientCode === updatedSRRData['ingredientCode'] &&
          el.subRecipeCode === updatedSRRData['subRecipeCode']
        );
        if (tempDiscData.length > 0) {
          tempDiscData[0].Discontinued = 'no'
          tempDiscData[0].ingredientName = tempDiscData[0].ingredientName.toUpperCase();
          this.discData.pop(tempDiscData);
        }
        this.dataSource.data = [...data, ...tempDiscData];
        let index = this.dataSource.data.indexOf(requiredPackage);
        this.dataSource.data[index] = updatedSRRData;
        let items = this.dataSource.data
        // this.dataSource.data = items.filter(item => item.Discontinued !== 'yes')
        this.dataSource.data = items.filter(item =>
          !['yes', 'y'].includes((item.Discontinued || 'no').toLowerCase())
        );
        this.tempData = this.dataSource.data;
        let disData = items.filter(item =>
          ['yes', 'y'].includes((item.Discontinued || 'no').toLowerCase())
        );
        // let disData = items.filter(item => item.Discontinued === 'yes')
        this.discData.push(...disData)
        this.dataSource.paginator = this.paginator;
        this.reflectSubRecipe();
        this.notify.snackBarShowSuccess('Updated successfully');
        this.subRecipeRecipeForm.reset();
        this.clearForm();
        this.subRecipeRecipeForm.patchValue({
          subRecipeCode: this.registrationForm.value.menuItemCode,
          subRecipeName: this.registrationForm.value.menuItemName,
          yield: 1
        });
        this.closeSRRDialog();
        this.showDeleteItems = false
        this.cd.detectChanges();
      }
    }
  }

  goBack() {
    this.isDuplicate = false;
    this.showSRR = false;
    this.subRecipeRecipeForm.reset();
    setTimeout(() => {
      const dataSourceTable = this.el.nativeElement.querySelector('.section');
      if (dataSourceTable) {
        dataSourceTable.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }, 100);
  }

  convertSRMKeys() {
    const keyData = [
      ['category', 'category'],
      ['subCategory', 'subCategory'],
      ['menuItemCode', 'menuItemCode'],
      ['menuItemName', 'menuItemName'],
      ['closingUOM', 'closingUOM'],
      ['itemType', 'itemType'],
      ['preparedAt', 'preparedAt'],
      ['usedAtOutlet', 'usedAtOutlet'],
      ['usedInWorkArea', 'usedInWorkArea'],
      ['UOM', 'uom'],
      ['weightInUse', 'weightInUse'],
      ['yield', 'yield'],
      ['recovery', 'recovery'],
      ['rate', 'rate'],
      ['finalRate', 'finalRate'],
      ['Discontinued', 'discontinued'],
      ['closingConversion', 'discontinued'],
      ['modified', 'modified'],
      ['portion', 'portion'],
      ['row_uuid', 'row_uuid'],
    ];

    const updatedRecipeData = {};
    keyData.forEach((key) => {
      let value = this.registrationForm.value[key[1]];
      if (key[0] == 'taxRate') {
        updatedRecipeData[key[0]] = value || 0;
      } else if (key[0] == 'portion'){
        if (this.registrationForm.value.unit === 'GM/ML') {
          value = this.registrationForm.value.recovery/value
        }
        updatedRecipeData[key[0]] =  this.notify.truncateAndFloor(value) || 1;
      } else {
        updatedRecipeData[key[0]] = value || '';
      }
    });
    return updatedRecipeData;
  }

  convertSRRKeys() {
    const keyData = [
      ['subRecipeCode', 'subRecipeCode'],
      ['subRecipeName', 'subRecipeName'],
      ['ingredientCode', 'ingredientCode'],
      ['ingredientName', 'ingredientName'],
      ['UOM', 'uom'],
      ['Initialweight', 'initialWeight'],
      ['defaultUOM', 'defaultUOM'],
      ['yield', 'yield'],
      ['weightInUse', 'weightInUse'],
      ['rate', 'rate'],
      ['finalRate', 'finalRate'],
      ['Discontinued', 'discontinued'],
      ['row_uuid', 'row_uuid'],
      ['modified', 'modified'],
      ['portionCount', 'portionCount']
    ];
    const updatedSubRecipeData = {};
    keyData.forEach((key) => {
      let value = this.subRecipeRecipeForm.value[key[1]];
      if (key[0] === 'Initialweight') {
        let formattedRec = this.notify.truncateAndFloor(this.subRecipeRecipeForm.value['weightInUse'] / this.subRecipeRecipeForm.value['yield'] )
        updatedSubRecipeData[key[0]] = formattedRec;
      } else {
        updatedSubRecipeData[key[0]] = value || '';
      }
    });
    return updatedSubRecipeData;
  }

  applyFilter(filterValue: any) {
    filterValue = filterValue.target.value;
    filterValue = filterValue.trim();
    filterValue = filterValue.toLowerCase();
    this.dataSource.filter = filterValue;
  }

  toggleSelectAll() {
    const control = this.registrationForm.controls['usedInWorkArea'];
    let data = [
      ...this.usedInWorkAreaBank.map((location) => location.workAreas),
    ];
    const flattenedArray = [].concat(...data);
    if (control.value.length - 1 === flattenedArray.length) {
      control.setValue([]);
    } else {
      control.setValue(flattenedArray);
    }
  }

  usedOutLetToggleSelectAll() {
    const control = this.registrationForm.controls['usedAtOutlet'];
    if (control.value.length - 1 === this.outletBank.length) {
      control.setValue(this.defaultOutletData);
    } else {
      control.setValue(this.outletBank);
    }
    this.locationChange(this.registrationForm.value.usedAtOutlet);
  }

  preparedToggleSelectAll() {
    const control = this.registrationForm.controls['preparedAt'];
    if (control.value.length - 1 === this.outletBank.length) {
      control.setValue(this.defaultPreLocData);
    } else {
      control.setValue(this.outletBank);
    }
  }

  filterDialog(filterValue) {
    filterValue = filterValue.target.value;
    filterValue = filterValue.trim().toLowerCase();
    this.filteredData = this.dropDownData.filter((item) =>
      item.toLowerCase().includes(filterValue)
    );
  }

  setZeroSRM(val, formData) {
    if (this.registrationForm.value.yield < 0) {
      this.registrationForm.get(formData).setValue(1);
      this.notify.snackBarShowInfo('Please enter a value greater than 0');
    } else if (this.registrationForm.value.yield > 0) {
      let formattedRec = this.notify.truncateAndFloor(this.registrationForm.value.yield * this.registrationForm.value.weightInUse)
      this.registrationForm.get('recovery').setValue(formattedRec);
    }
  }

  setZeroSRR(val, formData) {
    if (this.subRecipeRecipeForm.value.yield < 0) {
      this.subRecipeRecipeForm.get(formData).setValue(1);
      this.notify.snackBarShowInfo('Please enter a value greater than 0');
    } else if (this.subRecipeRecipeForm.value.yield > 0) {
      let initialWeight =  this.subRecipeRecipeForm.value.weightInUse / this.subRecipeRecipeForm.value.yield
      this.subRecipeRecipeForm.get('initialWeight').setValue(this.notify.truncateAndFloor((initialWeight)));
      let sum = (
        this.subRecipeRecipeForm.value.initialWeight *
        this.subRecipeRecipeForm.value.rate
      );
      this.subRecipeRecipeForm.get('finalRate').setValue(this.notify.truncateAndFloor(sum));
    }
  }

  sumForFinalRateSRR(val) {
    this.showWeightError = false;
      this.subRecipeRecipeForm.get('initialWeight').setValue(this.subRecipeRecipeForm.value.weightInUse / this.subRecipeRecipeForm.value.yield);
      let sum = (
        this.subRecipeRecipeForm.value.initialWeight *
        this.subRecipeRecipeForm.value.rate
      );
      this.subRecipeRecipeForm.get('finalRate').setValue(this.notify.truncateAndFloor(sum));
  }

  closeInfoDialog() {
    if(this.costDialogkey == true){
      this.close()
      if (this.dialogRef) {
        this.dialogRef.close();
      }
    }else{
      if (this.dialogRef) {
        this.dialogRef.close();
      }
    }
  }

  getCategories() {
    // this.api.getCategories({ tenantId: this.user.tenantId, type: 'subRecipe' }).pipe(first()).subscribe({
    //   next: (res) => {
    //     if (res['success']) {
          this.catAndsubCat = this.sharedData.getCategories().value;
          let categoryData = Object.keys(this.sharedData.getCategories().value).map(category => category.toUpperCase());
          let newCat = [...this.newCategory, ...categoryData];
          this.categories = [...new Set(newCat)];
          this.catBank = this.registrationForm.get('category').valueChanges.pipe(startWith(''), map(value => this._filter((value || ''), this.categories, '')));
        // }
    //   },
    //   error: (err) => { console.log(err); }
    // });
  }

  getSubCategories(val) {
    this.registrationForm.get('subCategory').setValue('');
    let data = this.baseData['Subrecipe Recipe'].filter(item => item.category === val);
    this.newSubCategory = data.map(subCat => subCat.subCategory)
    if (!(val in this.catAndsubCat)) {
      this.catAndsubCat[val] = []
    }
    let newSubCat = [...this.newSubCategory, ...this.catAndsubCat[val]]
    this.subCategories = [...new Set(newSubCat)];
    this.subCatBank = this.registrationForm.get('subCategory').valueChanges.pipe(startWith(''), map(value => this._filter((value || ''), this.subCategories , '')));
  }

  optionSelectedCat(option: any) {
    if (option.value.indexOf(this.question) === 0) {
      this.addOptionCat();
    } else {
      this.getSubCategories(this.registrationForm.value.category);
    }
    this.catBank = this.registrationForm.get('category').valueChanges.pipe(startWith(''), map(value => this._filter((value || ''), this.categories , '')));
  }

  addOptionCat() {
    this.registrationForm.controls['category'].patchValue(this.removePromptFromOption(this.registrationForm.value.category));
    this.getSubCategories(this.registrationForm.value.category);
  }

  optionSelectedSubCat(option: any) {
    if (option.value.indexOf(this.question) === 0) {
      this.addOptionSubCat();
    }
    this.subCatBank = this.registrationForm.get('subCategory').valueChanges.pipe(startWith(''), map(value => this._filter((value || ''), this.subCategories, '')));
  }

  addOptionSubCat() {
    this.registrationForm.controls['subCategory'].patchValue(this.removePromptFromOption(this.registrationForm.value.subCategory));
  }

  focusOutFunction(formKey) {
    if (this.registrationForm.get(formKey)) {
      if (this.registrationForm.get(formKey).value === null) {
        this.registrationForm.get(formKey).setValue(0);
      }
    }
  }

  focusOutFunctionSRR(formKey) {
    if (this.subRecipeRecipeForm.get(formKey).value === null) {
      this.subRecipeRecipeForm.get(formKey).setValue(0);
    }
  }

  focusFunction(formKey) {
    if (this.notify.truncateAndFloor(this.registrationForm.get(formKey).value) === 0) {
      this.registrationForm.get(formKey).setValue(null);
    }
  }

  focusFunctionSRR(formKey) {
    if (this.notify.truncateAndFloor(this.subRecipeRecipeForm.get(formKey).value) === 0) {
      this.subRecipeRecipeForm.get(formKey).setValue(null);
    }
  }

  public scrollRight(): void {
    this.widgetsContent.nativeElement.scrollTo({
      left: this.widgetsContent.nativeElement.scrollLeft + 150,
      behavior: 'smooth',
    });
  }

  public scrollLeft(): void {
    this.widgetsContent.nativeElement.scrollTo({
      left: this.widgetsContent.nativeElement.scrollLeft - 150,
      behavior: 'smooth',
    });
  }

  getBaseData() {
    this.baseData = this.sharedData.getBaseData().value;
    let obj = {};
    obj['tenantId'] = this.user.tenantId;
    obj['userEmail'] = this.user.email;
    obj['type'] = 'recipe';
    obj['specific'] = 'Subrecipe Recipe';
    if (this.registrationForm.value) {
      obj['itemCode'] = this.registrationForm.value.menuItemCode;
    }
    this.api.getPresentData(obj).subscribe({
      next: (res) => {
        if (res['success']) {
          this.baseData = res['data'][0] ?? res['data'];
          this.displayedColumns = [
            'sNo',
            // 'discontinued',
            'ingredientName',
            // 'ingredientCode',
            // 'subRecipeName',
            // 'subRecipeCode',
            'uom',
            'weightInUse',
            'yield',
            'initialWeight',
            'rate',
            'finalRate',
          ];
          this.isSRRDataReady = true;
          this.baseData['Subrecipe Recipe'].sort((a, b) => {
            if (a.modified === 'yes' && b.modified !== 'yes') {
              return -1;
            }
            if (b.modified === 'yes' && a.modified !== 'yes') {
              return 1;
            }
            return 0;
          });
          this.dataSource.data = this.baseData['Subrecipe Recipe']
            ? this.baseData['Subrecipe Recipe']
            : [];
          this.dataSource.data.forEach((el) => {
            el['Initialweight'] = this.notify.truncateAndFloor(el['Initialweight']);
            let portion = 1
            let requiredItem = this.invItems.find(
              (item) => item.itemCode == el['ingredientCode']
            );
            let conversionCoefficient;
            conversionCoefficient = requiredItem
              ? requiredItem['uom'] == 'NOS'
                ? 1
                : 1000
              : 0;
            let rate = requiredItem
            ? ((requiredItem.hasOwnProperty('packageQty') ? requiredItem['packageQty'] : 1) / conversionCoefficient )* requiredItem['withTaxPrice']
            : 0;

            el['defaultUOM'] = el['UOM'] ;
            if (requiredItem && el.hasOwnProperty('portionCount') && (!['', null,undefined].includes(el.portionCount)) ){
              el['UOM'] = 'PORTION'
              el['portionCount'] = this.notify.truncateAndFloor(el['portionCount'])
            }
            el['rate'] = rate ;
            el['Initialweight'] = this.notify.truncateAndFloor(el['Initialweight'] * portion);
            el['weightInUse'] = this.notify.truncateAndFloor(el['weightInUse'] * portion) ;
            // el['finalRate'] = this.notify.truncateAndFloor(rate * el['Initialweight']);
            el['finalRate'] = rate * el['Initialweight'];
          });
          this.tempData = this.dataSource.data
          // this.dataSource.data = this.dataSource.data.filter(item => item.Discontinued !== 'yes')
          this.dataSource.data = this.dataSource.data.filter(item =>
            !['yes', 'y'].includes((item.Discontinued || 'no').toLowerCase())
          );
          // this.discData = this.tempData.filter(item => item.Discontinued === 'yes')
          this.discData = this.tempData.filter(item =>
            ['yes', 'y'].includes((item.Discontinued || 'no').toLowerCase())
          );
          this.cd.detectChanges();
          this.dataSource.paginator = this.paginator;
          this.dataSource.sort = this.sort;
          this.reflectSubRecipe();
          this.cd.detectChanges();
        } else {
          this.baseData = [];
        }

        if(this.costDialogkey == true){
          this.openPortionData();
        }

      },
      error: (err) => {
        console.log(err);
      },
    });
  }

  nextTab() {
    if (this.registrationForm.invalid) {
      this.registrationForm.markAllAsTouched();
      this.notify.snackBarShowError('Please fill out all required fields');
      this.loadSpinnerForApi = false;
      this.cd.detectChanges();
    } else {
      this.stepper.next();
    }
  }

  getTotal(key: string) {
    let total = this.dataSource.data.reduce((total, item) => {
      const value = this.notify.truncateAndFloor(item[key]) || 0;
      return total + value;
    }, 0);
    return this.notify.truncateAndFloor((total * this.ratio));
  }

  getSRRTotal(key: string) {
    let total = this.dataSource.data.reduce((total, item) => {
      const value = (item[key]) || 0;
      return total + value;
    }, 0);
    return ((total * this.ratio));
  }

  getLocationCall() {
    this.locationList = this.sharedData.getLocation().value;
    this.preparedBank = this.locationList.map(
      (branch) => branch.abbreviatedRestaurantId
    );
    if (this.preparedBank.length === 1) {
      this.registrationForm.get('preparedAt').setValue(this.preparedBank);
    }
    this.preparedLocationNames.next(this.preparedBank.slice());
    this.preparedFilterCtrl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe(() => {
        this.Filter(
          this.preparedBank,
          this.preparedFilterCtrl,
          this.preparedLocationNames
        );
      });

    this.outletBank = this.locationList.map(
      (branch) => branch.abbreviatedRestaurantId
    );
    if (this.outletBank.length === 1) {
      this.registrationForm.get('usedAtOutlet').setValue(this.outletBank);
      this.locationChange(this.registrationForm.value.usedAtOutlet)
    }
    this.outletLocationNames.next(this.outletBank.slice());
    this.outletFilterCtrl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe(() => {
        this.Filter(
          this.outletBank,
          this.outletFilterCtrl,
          this.outletLocationNames
        );
      });

    if (this.dialogData.key == false || this.dialogData.costDialogkey == true ) {
      this.isUpdateActive = true;
      this.setValuesForForm(this.dialogData.elements);
    } else if (this.dialogData.key == null && this.costDialogkey == false) {
      this.dropDownData = this.dialogData.dropDownData;
      this.filteredData = [...this.dropDownData];
    }
    this.getBaseData();
  }

  readIPConfig() {
    this.api.readIPConfig(this.user.tenantId).subscribe({
      next: (res) => {
        if (res?.['success']) {
          const data = res['data'];
          this.logo = data.tenantDetails.logo
        }
      },
      error: (err) => { console.log(err) }
    });
  }

  printOption() {
    const discontinuedValue = this.registrationForm.get('discontinued').value;
    this.status = discontinuedValue === 'yes' ? 'active' : 'Discontinued';
    const tableData = this.dataSource.data.map((element: any) => [
      element.ingredientName,
      element.ingredientCode,
      element.UOM,
      element.Initialweight,
      element.yield,
      element.weightInUse,
      this.notify.truncateAndFloor(element.rate),
      this.notify.truncateAndFloor(element.finalRate),
    ]);

    let obj = {
      factory_name: this.user.name,
      recipe_details: {
        'Sub Recipe Name': this.registrationForm.get('menuItemName').value,
        'Sub Recipe Code': this.registrationForm.get('menuItemCode').value,
        'Category': this.registrationForm.get('category').value,
        'Sub Category': this.registrationForm.get('subCategory').value,
        'Status': this.status,
        'UOM': this.registrationForm.get('uom').value,
        'Closing UOM': this.registrationForm.get('closingUOM').value,
        'Prepared At': this.registrationForm.get('preparedAt').value.join(', '),
        'Sales Outlet': this.registrationForm.get('usedAtOutlet').value.join(', '),
        'Issued To': this.registrationForm.get('usedInWorkArea').value.join(', ')
      },
      logo: this.logo,
      table_headers : ['Ingredient Name','Ingredient Code','UOM','Initial Weight','Yield','Weight In Use','Unit Cost','Final Rate'],
      table_data: tableData,
      summary: {
        'Weight In Use': `${this.registrationForm.get('weightInUse').value} (${this.registrationForm.get('uom').value})`,
        'Yield': this.registrationForm.get('yield').value,
        'Recovery': `${this.registrationForm.get('recovery').value} (${this.registrationForm.get('uom').value})`,
        'Unit Cost': `${this.registrationForm.get('rate').value} (Rs)`,
        'Gross Weight': `${this.registrationForm.get('weightInUse').value} (${this.registrationForm.get('uom').value})`,
        'Preparation Cost': `${this.registrationForm.get('rate').value} (Rs)`,
        'Weight / Portion': `${this.getWeightPerPortion()} (${this.registrationForm.get('uom').value})`,
        'Cost / Portion': `${this.notify.truncateAndFloor(this.getCostPerPortion())} (Rs)`,
      },
    };
    this.api.printInvoice(obj).subscribe({
      next: (data) => {
        this.api.globalPrintPdf(data.pdf_base64);
      },
      error: (err) => {
        console.error(err);
      }
    });
  }

  locationChange(event) {
    if (event) {
      const selectedWorkAreasArray = this.locationList.filter((branch) =>
        event.includes(branch.abbreviatedRestaurantId)
      );
      this.usedInWorkAreaBank = selectedWorkAreasArray;
      if (this.usedInWorkAreaBank.length === 1) {
        this.registrationForm
          .get('usedInWorkArea')
          .setValue(this.usedInWorkAreaBank);
      }
      if(this.discontinuedOutletData.length > 0){
        this.discontinuedOutletData.forEach(val => {
          this.usedInWorkAreaBank = this.usedInWorkAreaBank.map(item => {
            if (item.branchName === val) {
              item.disabled = true;
            }
            return item;
          });
        })
      }
      this.usedInWorkAreaNames.next(this.usedInWorkAreaBank.slice());
      this.usedInWorkAreaFilterCtrl.valueChanges
        .pipe(takeUntil(this._onDestroy))
        .subscribe(() => {
          this.FilterIssued(
            this.usedInWorkAreaBank,
            this.usedInWorkAreaFilterCtrl,
            this.usedInWorkAreaNames
          );
        });
    } else {
      this.usedInWorkAreaBank = [];
    }
  }

  openPortionData() {
    this.dialogRef = this.dialog.open(this.openPortionDialog, {
      minWidth: '25vw',
      panelClass: 'smallCustomDialog',
      autoFocus: false,
      disableClose: true,
    });
  }

  openUnitDialog() {
    this.dialogRef = this.dialog.open(this.openUnitCostDialog, {
      minWidth: '25vw',
      panelClass: 'smallCustomDialog',
    });
  }

  deleteSRR(element) {
    const item = this.invItems.find(
      (item) =>
        item.itemName.toLowerCase() === element.ingredientName.toLowerCase()
    );
    this.updateSRR = true;
    this.subRecipeRecipeForm.patchValue({
      ingredientCode: element['ingredientCode'],
      ingredientName: item ? item.itemName : '',
      subRecipeCode: element['subRecipeCode'],
      subRecipeName: element['subRecipeName'],
      uom: element['UOM'],
      yield: element['yield'],
      rate: this.notify.truncateAndFloor(element['rate']) ,
      finalRate: this.notify.truncateAndFloor(element['finalRate']),
      initialWeight: element['Initialweight'],
      loss: element['loss'],
      weightInUse: element['weightInUse'],
      discontinued: ['no','NO' ,'No', 'N', null,''].includes(element['Discontinued']) ? 'no' : 'yes',
      row_uuid: element['row_uuid'],
    });

    if (!element['Initialweight']) {
      this.subRecipeRecipeForm.get('initialWeight').setValue(0);
    }

    if (!element['loss']) {
      this.subRecipeRecipeForm.get('loss').setValue(0);
    }
    this.dialogRef = this.dialog.open(this.openDeleteDialog, {
      minWidth: '25vw',
      panelClass: 'smallCustomDialog',
    });
  }

  deleteData(){
    const indexToRemove = this.dataSource.data.findIndex(
        (recipe) => recipe.ingredientCode === this.subRecipeRecipeForm.value.ingredientCode
      );
      if (indexToRemove !== -1) {
        let item = this.dataSource.data[indexToRemove];  // Store the removed item
        this.removedItem.push(item)
        this.dataSource.data = this.dataSource.data
          .slice(0, indexToRemove)
          .concat(this.dataSource.data.slice(indexToRemove + 1));
          // this.deleteDataFormDB()
      }
    this.tempData = this.dataSource.data
    this.subRecipeRecipeForm.reset();
        this.subRecipeRecipeForm.patchValue({
          subRecipeCode: this.registrationForm.value.menuItemCode,
          subRecipeName: this.registrationForm.value.menuItemName,
    });
    this.clearForm();
    this.reflectSubRecipe();
    this.closeInfoDialog();
  }

  deleteDataFormDB(){
    let obj = {};
    if(this.removedItem.length > 0){
      this.removedItem.forEach(item => {
        item['delete'] = true;
        // item['Discontinued'] = 'yes';
        item['InvUom'] = item['Inv. UOM'];
        delete item['Inv. UOM']
      });
      obj['tenantId'] = this.user.tenantId;
      obj['userEmail'] = this.user.email;
      obj['data'] = this.removedItem;
      obj['type'] = 'recipe';
      obj['category'] = 'subRecipe';
      this.api
        .removeData(obj)
        .pipe(first())
        .subscribe({
          next: (res) => {
            if (res['success']) {
              this.cd.detectChanges();
              this.notify.snackBarShowSuccess(
                'Data Deleted successfully'
              );
            }
          },
          error: (err) => {
            console.log(err);
          },
        });
    }
  }

  discontinueData(){
      let updatedSRRData = this.convertSRRKeys();
      updatedSRRData['modified'] = 'yes';
      updatedSRRData['Discontinued'] = 'yes';
      let requiredPackage = this.dataSource.data.find(
        (el) =>
          el.ingredientCode == updatedSRRData['ingredientCode'] &&
          el.subRecipeCode == updatedSRRData['subRecipeCode']
          // el.ingredientName.toLowerCase() == updatedSRRData['ingredientName'].toLowerCase()
      );
      if (requiredPackage) {
        requiredPackage['modified'] = 'yes';
        requiredPackage['Discontinued'] = 'yes';
        let index = this.dataSource.data.indexOf(requiredPackage);
        this.discData.push(updatedSRRData)
        this.dataSource.data[index] = updatedSRRData;
        // let data = this.dataSource.data.filter(item => item.Discontinued !== 'yes' )
        let data = this.dataSource.data.filter(item =>
          !['yes', 'y'].includes((item.Discontinued || 'no').toLowerCase())
        );
        this.dataSource.data = data
        // this.dataSource.paginator = this.paginator;
        this.cd.detectChanges();
        this.reflectSubRecipe();
        this.notify.snackBarShowSuccess('Updated successfully');
        this.subRecipeRecipeForm.reset();
        this.subRecipeRecipeForm.patchValue({
          subRecipeCode: this.registrationForm.value.menuItemCode,
          subRecipeName: this.registrationForm.value.menuItemName,
        });
        this.closeInfoDialog();
      }
  }

  showItems(){
    if(this.showDeleteItems == true){
      this.dataSource.data = this.discData
    }else if(this.showDeleteItems == false){
      // this.dataSource.data = this.tempData.filter(item => item.Discontinued !== 'yes' )
      this.dataSource.data = this.tempData.filter(item =>
        !['yes', 'y'].includes((item.Discontinued || 'no').toLowerCase())
      );
    }
  }

  openSelect(select: MatSelect) {
    select.open();
  }

  onUnitChange(el) {
    if (this.registrationForm.value.unit === 'GM/ML') {
      let reqWeight = this.registrationForm.value.recovery / this.registrationForm.value.portion
      this.registrationForm.get('portion').setValue(this.notify.truncateAndFloor(reqWeight));
    } else {
      let reqPortion = this.registrationForm.value.recovery / this.registrationForm.value.portion
      this.registrationForm.get('portion').setValue(this.notify.truncateAndFloor(reqPortion));
    }
  }

  getWeightPerPortion() {
    let portion = this.registrationForm.value.portion
    if (this.registrationForm.value.unit === 'GM/ML') {
       portion = this.registrationForm.value.recovery / this.registrationForm.value.portion
    }
    return (this.registrationForm.value.recovery * (1 / portion))
  }

  getCostPerPortion() {
    let portion = this.registrationForm.value.portion
    if (this.registrationForm.value.unit === 'GM/ML') {
       portion = this.registrationForm.value.recovery / this.registrationForm.value.portion
    }
    return (this.registrationForm.value.rate * (1 / portion))
  }

  setPortionData() {
    if (this.registrationForm.value.unit === 'GM/ML') {
      this.registrationForm.value.portion < 0 ? this.registrationForm.get('portion').setValue(this.registrationForm.value.recovery) : undefined ;
    } else {
      this.registrationForm.value.portion < 0 ? this.registrationForm.get('portion').setValue(1) : undefined ;
    }
  }

  updateBaseData(data){
    data['menu master'].forEach((el)=> {
      const index = this.baseData['menu master'].findIndex(item => item['menuItemCode'] === el['menuItemCode']);
      el['modified'] = 'no'
      if (index !== -1) {
        this.baseData['menu master'][index] = el
      }
    })
    data['menu recipes'].forEach((el)=> {
      const recipeIndex = this.baseData['menu recipes'].findIndex(item => item['menuItemCode'] === el['menuItemCode'] && item['ingredientCode'] === el['ingredientCode']);
      el['modified'] = 'no'
      if (recipeIndex !== -1) {
        this.baseData['menu recipes'][recipeIndex] = el
      }
    })


    data['Subrecipe Master'].forEach((el)=> {
      const index = this.baseData['Subrecipe Master'].findIndex(item => item['menuItemCode'] === el['menuItemCode']);
      el['modified'] = 'no'
      if (index !== -1) {
        this.baseData['Subrecipe Master'][index] = el
      }
    })
    data['Subrecipe Recipe'].forEach((el)=> {
      const recipeIndex = this.baseData['Subrecipe Recipe'].findIndex(item => item['subRecipeCode'] === el['subRecipeCode'] && item['ingredientCode'] === el['ingredientCode']);
      el['modified'] = 'no'
      if (recipeIndex !== -1) {
        this.baseData['Subrecipe Recipe'][recipeIndex] = el
      }
    })
    this.sharedData.setBaseData(this.baseData)
  }

  onDelete(location: string, event: Event, select ,group) {
    event.stopPropagation();
    this.selectedDropDown = select
    this.selectedData = location
    this.groupData = group
    this.dialogRef = this.dialog.open(this.discontinuedSelectDialog, {
      width: '500px',
    });
    this.dialogRef.afterClosed().subscribe(result => {
    });
  }

  onRestore(location: string, event: Event, select ,group){
    event.stopPropagation();
    if(select === 'usedOutlet'){
      this.discontinuedOutletData = this.discontinuedOutletData.filter(item => item !== location);
      this.discontinuedIssuedToData = this.discontinuedIssuedToData.filter(item =>
        item.abbreviatedRestaurantId !== location
      );
      this.usedInWorkAreaBank =  this.usedInWorkAreaBank.map(item => {
        if (item.abbreviatedRestaurantId === location && item.hasOwnProperty('disabled')) {
          delete item.disabled;
        }
        return item;
      });
      this.usedInWorkAreaNames.next(this.usedInWorkAreaBank.slice());
      this.usedInWorkAreaFilterCtrl.valueChanges
        .pipe(takeUntil(this._onDestroy))
        .subscribe(() => {
          this.FilterIssued(
            this.usedInWorkAreaBank,
            this.usedInWorkAreaFilterCtrl,
            this.usedInWorkAreaNames
          );
        });
    }else if(select === 'issuedTo'){
      this.discontinuedIssuedToData.forEach(item => {
        if (item.abbreviatedRestaurantId === group.abbreviatedRestaurantId) {
          item.workAreas = item.workAreas.filter(workArea => workArea !== location);
        }
      });
      this.discontinuedIssuedToData = this.discontinuedIssuedToData.filter(item => item.workAreas.length > 0);
    }else if(select === 'preparatoryLocation'){
      this.discontinuedPreLocData = this.discontinuedPreLocData.filter(item => item !== location);
    }
    this.cd.detectChanges();
  }

  discontinuedSelectData(){
    if(this.selectedDropDown === 'usedOutlet'){
      this.discontinuedOutletData.push(this.selectedData)
      const selectedWorkAreasArray = this.locationList.filter(branch => this.selectedData.includes(branch.abbreviatedRestaurantId))
      this.discontinuedIssuedToData.push(selectedWorkAreasArray[0])
      this.usedInWorkAreaBank = this.usedInWorkAreaBank.map(item => {
        if (item.abbreviatedRestaurantId === this.selectedData) {
          item.disabled = true;
        }
        return item;
      });
      this.usedInWorkAreaNames.next(this.usedInWorkAreaBank.slice());
      this.usedInWorkAreaFilterCtrl.valueChanges
        .pipe(takeUntil(this._onDestroy))
        .subscribe(() => {
          this.FilterIssued(
            this.usedInWorkAreaBank,
            this.usedInWorkAreaFilterCtrl,
            this.usedInWorkAreaNames
          );
        });

    }else if(this.selectedDropDown === 'issuedTo'){
      [this.groupData].forEach(item => {
        const matchingIssued = this.discontinuedIssuedToData.find(issuedItem => issuedItem.abbreviatedRestaurantId === item.abbreviatedRestaurantId);
        if (matchingIssued) {
            matchingIssued.workAreas.push(this.selectedData);
        } else {
            const newObject = {
                abbreviatedRestaurantId: item.abbreviatedRestaurantId,
                workAreas: [this.selectedData]
            };
            this.discontinuedIssuedToData.push(newObject);
        }
      });
      const newArray = [this.groupData].map(item => {
          return {
              abbreviatedRestaurantId: item.abbreviatedRestaurantId,
              workAreas: item.workAreas.filter(workArea => workArea === this.selectedData)
          };
      });
      this.discontinuedIssuedToData.push(...newArray);
    }else if(this.selectedDropDown === 'preparatoryLocation'){
      this.discontinuedPreLocData.push(this.selectedData)
    }
    this.closeDiscontinuedDialog();
    this.cd.detectChanges();
  }

  closeDiscontinuedDialog(){
    this.dialogRef.close();
  }

  getPerKGCost() {
    return ((this.registrationForm.value.rate ) / this.registrationForm.value.recovery) * 1000
  }

  isOptionDisabled(data: string , group): boolean {
    return this.discontinuedIssuedToData.some(item =>
      item.abbreviatedRestaurantId === group.abbreviatedRestaurantId &&
      item.workAreas.includes(data)
    );
  }

  isCheckOptionDisabled(data: string , group): boolean {
    return this.discontinuedIssuedToData.some(item =>
      item.abbreviatedRestaurantId === group.abbreviatedRestaurantId &&
      item.workAreas.includes(data)
    );
  }

  setDiscontinuedDataInRolopos(){
    this.api.dicontinuedData({
      'tenantId' : this.user.tenantId,
      'userEmail' : this.user.email,
      'type' : 'subRecipeLocations',
      'discontinuedLocations' : {
        'subRecipeLocations' : {
          'procuredAtDiscontinued' : this.discontinuedPreLocData.length > 0 ? this.discontinuedPreLocData : [],
          'outLetDiscontinued' : this.discontinuedOutletData.length > 0 ? this.discontinuedOutletData : [],
          'issuedToDiscontinued' : this.discontinuedIssuedToData.length > 0 ? this.discontinuedIssuedToData : []
        }
      }
    }).pipe(first()).subscribe({
      next: (res) => {
        if (res['success']) {
          this.cd.detectChanges();
        }
      },
      error: (err) => { console.log(err) }
    });
  }

  setRestaurant(event){
    this.getMenuRecipes();
    this.cd.detectChanges();
  }

  filterRestaurants() {
    const search = this.restaurantFilterCtrl.value?.toLowerCase() || '';
    this.filteredRestaurantOptions = this.branchesData.filter(branch =>
      branch.branchName.toLowerCase().includes(search)
    );
  }

}
