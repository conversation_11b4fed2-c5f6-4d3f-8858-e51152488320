import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';

// ===== INTERFACES =====
export interface DashboardFilters {
  locations: string[];
  startDate: string;
  endDate: string;
  baseDate: string;
  categories: string[];
  subcategories: string[];
  workAreas: string[];
  departments?: string[];
}

export interface SmartDashboardRequest {
  tenant_id: string;
  filters: DashboardFilters;
  user_query: string;
  use_default_charts: boolean;
  dashboard_type: string;
}

export interface SummaryItem {
  icon: string;
  value: string;
  label: string;
  data_type: string;
}

export interface ChartDataset {
  label: string;
  data: number[];
  backgroundColor: string[];
  borderColor: string[];
}

export interface ChartData {
  labels: string[];
  datasets: ChartDataset[];
  headers?: string[];  // For table charts
  rows?: any[];        // For table charts
}

export interface Chart {
  id: string;
  title: string;
  type: string;
  data: ChartData;
}

export interface DashboardResponse {
  charts: Chart[];
  summary_items: SummaryItem[];
}

export interface SmartDashboardApiResponse {
  status: string;
  data: DashboardResponse;
  message?: string;
  error_code?: string;
}


// ===== SERVICE =====
@Injectable({
  providedIn: 'root'
})
export class SmartDashboardService {
  private readonly baseUrl = environment.baseUrl;
  private readonly engineUrl = environment.engineUrl;

  constructor(private readonly http: HttpClient) {}

  // ===== API METHODS =====
  /**
   * Get smart dashboard data with filters and optional query
   */
  getSmartDashboardData(request: SmartDashboardRequest): Observable<SmartDashboardApiResponse> {
    return this.http.post<SmartDashboardApiResponse>(`${this.engineUrl}api/smart-dashboard/smart_ask`, request);
  }

  /**
   * Get categories from the API
   */
  getCategories(tenantId: string): Observable<any> {
    const payload = {
      tenantId: tenantId,
      categoryType: ["inventory", "menu", "subRecipe"]
    };
    return this.http.post<any>(`${this.baseUrl}getCategories/`, payload);
  }

  /**
   * Get subcategories from the API
   */
  getSubCategories(tenantId: string, selectedCategories: string[] = []): Observable<any> {
    const payload = {
      tenantId: tenantId,
      categories: selectedCategories.length > 0 ? selectedCategories : [],
      categoryType: ["inventory", "menu", "subRecipe"]
    };
    return this.http.post<any>(`${this.baseUrl}getSubCategories/`, payload);
  }

  // ===== UTILITY METHODS =====
  /**
   * Format number with Indian numbering system and optional currency
   */
  formatValue(value: number, currency?: string): string {
    const formatted = this.getFormattedNumber(value);
    return currency ? `${currency}${formatted}` : formatted;
  }

  /**
   * Format currency value using Indian numbering system
   */
  formatCurrency(value: number, currency = '₹'): string {
    return this.formatValue(value, currency);
  }

  /**
   * Format number with Indian numbering system
   */
  formatNumber(value: number): string {
    return this.formatValue(value);
  }

  private getFormattedNumber(value: number): string {
    // Convert to number if it's a string
    const numValue = typeof value === 'string' ? parseFloat(value) : value;

    // Handle invalid numbers
    if (isNaN(numValue)) {
      return '0';
    }

    // Format with Indian numbering system showing exact values
    return this.formatExactIndianNumber(numValue);
  }

  /**
   * Format number with Indian comma formatting - showing exact numbers
   */
  private formatExactIndianNumber(value: number): string {
    // Handle negative numbers
    const isNegative = value < 0;
    const absValue = Math.abs(value);

    // Use Indian locale formatting for exact numbers with commas
    const formatted = absValue.toLocaleString('en-IN', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    });

    return isNegative ? `-${formatted}` : formatted;
  }

  /**
   * Format exact number with Indian comma separation (no abbreviations)
   */
  formatExactNumber(value: number | string): string {
    const numValue = typeof value === 'string' ? parseFloat(value.replace(/[^\d.-]/g, '')) : value;

    if (isNaN(numValue)) {
      return '0';
    }

    return numValue.toLocaleString('en-IN', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    });
  }


  // ===== ICON MAPPING =====
  /**
   * Get icon for summary card based on data type and label
   */
  getSummaryCardIcon(dataType: string, label: string): string {
    const lowerLabel = label.toLowerCase();

    // Label-based icon mapping (more specific)
    const labelIconMap: Array<[string, string]> = [
      ['order', 'shopping_cart'],
      ['amount', 'attach_money'],
      ['value', 'attach_money'],
      ['vendor', 'store'],
      ['supplier', 'store'],
      ['average', 'trending_up'],
      ['inventory', 'inventory'],
      ['stockout', 'warning'],
      ['turnover', 'autorenew'],
      ['variance', 'trending_down'],
      ['issued', 'sync_alt']
    ];

    // Check label-based mapping first
    for (const [keyword, icon] of labelIconMap) {
      if (lowerLabel.includes(keyword)) {
        return icon;
      }
    }

    // Fallback to data type mapping
    const dataTypeIconMap: Record<string, string> = {
      currency: 'attach_money',
      number: 'numbers',
      percentage: 'percent',
      vendor: 'store',
      ratio: 'autorenew'
    };

    return dataTypeIconMap[dataType] || 'analytics';
  }

  // ===== DATE UTILITIES =====
  /**
   * Validate date range constraints
   */
  validateDateRange(startDate: Date, endDate: Date): boolean {
    if (!startDate || !endDate) {
      return false;
    }

    if (endDate <= startDate) {
      return false;
    }

    // Maximum 1 year range
    const oneYear = 365 * 24 * 60 * 60 * 1000;
    return (endDate.getTime() - startDate.getTime()) <= oneYear;
  }

  /**
   * Get default date range (last 30 days)
   */
  getDefaultDateRange(): { startDate: Date; endDate: Date } {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30);

    return { startDate, endDate };
  }
}
