import { Injectable } from '@angular/core';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { Observable } from 'rxjs';
import { ConfirmationDialogComponent, ConfirmationDialogData } from '../components/confirmation-dialog/confirmation-dialog.component';

@Injectable({
  providedIn: 'root'
})
export class ConfirmationDialogService {
  constructor(private dialog: MatDialog) {}

  /**
   * Open a confirmation dialog
   */
  confirm(data: ConfirmationDialogData): Observable<boolean> {
    const dialogRef: MatDialogRef<ConfirmationDialogComponent> = this.dialog.open(
      ConfirmationDialogComponent,
      {
        width: '450px',
        disableClose: true,
        data: data
      }
    );

    return dialogRef.afterClosed();
  }

  /**
   * Open a delete confirmation dialog
   */
  confirmDelete(itemName: string, additionalMessage?: string): Observable<boolean> {
    const message = `Are you sure you want to delete "${itemName}"?${additionalMessage ? '\n\n' + additionalMessage : ''}`;
    
    return this.confirm({
      title: 'Confirm Deletion',
      message: message,
      confirmText: 'Delete',
      cancelText: 'Cancel',
      type: 'danger'
    });
  }

  /**
   * Open a warning confirmation dialog
   */
  confirmWarning(title: string, message: string, confirmText: string = 'Continue'): Observable<boolean> {
    return this.confirm({
      title: title,
      message: message,
      confirmText: confirmText,
      cancelText: 'Cancel',
      type: 'warning'
    });
  }

  /**
   * Open an info confirmation dialog
   */
  confirmInfo(title: string, message: string, confirmText: string = 'OK'): Observable<boolean> {
    return this.confirm({
      title: title,
      message: message,
      confirmText: confirmText,
      cancelText: 'Cancel',
      type: 'info'
    });
  }
}
