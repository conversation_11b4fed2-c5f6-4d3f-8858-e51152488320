import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { map, catchError, switchMap } from 'rxjs/operators';
import { environment } from '../../environments/environment';

// ===== INTERFACES =====
export interface Department {
  id: string;
  name: string;
  code?: string;
  description?: string;
  isActive?: boolean;
}

export interface DepartmentGroup {
  id: string;
  name: string;
  description?: string;
  departmentIds: string[];
  isActive?: boolean;
}

export interface DepartmentCategoryMapping {
  departmentId: string;
  departmentName: string;
  categories: string[];
}

export interface DepartmentGroupCategoryMapping {
  groupId: string;
  groupName: string;
  categories: string[];
}

export interface DepartmentGroupWorkareaMapping {
  groupId: string;
  groupName: string;
  workAreas: string[];
}

export interface CategoryWorkareaMapping {
  categoryName: string;
  workAreas: string[];
}

export interface MappingConfig {
  departmentGroups?: DepartmentGroup[];
  departmentCategoryMappings: DepartmentCategoryMapping[];
  departmentGroupCategoryMappings?: DepartmentGroupCategoryMapping[];
  departmentGroupWorkareaMappings?: DepartmentGroupWorkareaMapping[];
  categoryWorkareaMappings: CategoryWorkareaMapping[];
  lastUpdated?: string;
}

@Injectable({
  providedIn: 'root'
})
export class DepartmentService {
  private readonly engineUrl = environment.engineUrl;

  constructor(private readonly http: HttpClient) {}

  // ===== DEPARTMENT METHODS =====
  /**
   * Get all departments for a tenant (via secure backend endpoint)
   */
  getDepartments(tenantId: string): Observable<Department[]> {
    return this.http.get<any>(`${this.engineUrl}api/smart-dashboard/departments/${tenantId}`)
      .pipe(
        map(response => {
          if (response.status === 'success' && response.data) {
            const departments = response.data.map((dept: any) => ({
              id: String(dept.id),
              name: dept.name,
              code: dept.code,
              description: dept.description,
              isActive: dept.isActive !== false
            }));
            return departments;
          }
          throw new Error(response.message || 'Failed to fetch departments');
        }),
        catchError(error => {
          console.error('DepartmentService: Error fetching departments:', error);
          return throwError(() => new Error('Failed to fetch departments'));
        })
      );
  }

  // ===== DEPARTMENT GROUP METHODS =====
  /**
   * Get all department groups for a tenant
   */
  getDepartmentGroups(tenantId: string): Observable<DepartmentGroup[]> {
    return this.http.get<any>(`${this.engineUrl}master_data/get-department-groups/${tenantId}`)
      .pipe(
        map(response => {
          if (response.success && response.data) {
            return response.data.departmentGroups || [];
          }
          return [];
        }),
        catchError(error => {
          console.error('DepartmentService: Error fetching department groups:', error);
          return throwError(() => new Error('Failed to fetch department groups'));
        })
      );
  }

  /**
   * Save department groups for a tenant
   */
  saveDepartmentGroups(tenantId: string, groups: DepartmentGroup[]): Observable<boolean> {
    console.log('DepartmentService: saveDepartmentGroups called with:', {
      tenantId,
      groups,
      groupsLength: groups.length
    });

    const requestBody = {
      tenantId: tenantId,
      departmentGroups: groups
    };

    return this.http.post<any>(`${this.engineUrl}master_data/save-department-groups`, requestBody)
      .pipe(
        map(response => response.success || false),
        catchError(error => {
          console.error('DepartmentService: Error saving department groups:', error);
          return throwError(() => new Error('Failed to save department groups'));
        })
      );
  }

  /**
   * Create a new department group
   */
  createDepartmentGroup(tenantId: string, groupName: string, groupDescription: string = '', departmentIds: string[] = []): Observable<any> {
    console.log('DepartmentService: createDepartmentGroup called with:', {
      tenantId,
      groupName,
      groupDescription,
      departmentIds
    });

    const requestBody = {
      tenantId: tenantId,
      groupName: groupName,
      groupDescription: groupDescription,
      departmentIds: departmentIds
    };

    return this.http.post<any>(`${this.engineUrl}master_data/create-department-group`, requestBody)
      .pipe(
        map(response => response.data || {}),
        catchError(error => {
          console.error('DepartmentService: Error creating department group:', error);
          return throwError(() => new Error('Failed to create department group'));
        })
      );
  }

  /**
   * Update an existing department group
   */
  updateDepartmentGroup(tenantId: string, group: DepartmentGroup): Observable<any> {
    console.log('DepartmentService: updateDepartmentGroup called with:', {
      tenantId,
      group
    });

    const requestBody = {
      tenantId: tenantId,
      groupId: group.id,
      groupName: group.name,
      groupDescription: group.description || '',
      departmentIds: group.departmentIds || []
    };

    return this.http.put<any>(`${this.engineUrl}master_data/update-department-group`, requestBody)
      .pipe(
        map(response => response.data || {}),
        catchError(error => {
          console.error('DepartmentService: Error updating department group:', error);
          return throwError(() => new Error('Failed to update department group'));
        })
      );
  }

  deleteDepartmentGroup(tenantId: string, groupId: string): Observable<boolean> {
    console.log('DepartmentService: deleteDepartmentGroup called with:', {
      tenantId,
      groupId
    });

    const requestBody = {
      tenantId: tenantId,
      groupId: groupId
    };

    return this.http.delete<any>(`${this.engineUrl}master_data/delete-department-group`, { body: requestBody })
      .pipe(
        map(response => response.success || false),
        catchError(error => {
          console.error('DepartmentService: Error deleting department group:', error);
          return throwError(() => new Error('Failed to delete department group'));
        })
      );
  }

  // ===== DEPARTMENT-CATEGORY MAPPING METHODS =====
  /**
   * Get department-category mappings for a tenant from database
   */
  getDepartmentCategoryMappings(tenantId: string): Observable<DepartmentCategoryMapping[]> {
    return this.http.get<any>(`${this.engineUrl}master_data/get-mapping-config/${tenantId}`)
      .pipe(
        map(response => {
          if (response.success && response.data) {
            return response.data.departmentCategoryMappings || [];
          }
          return [];
        }),
        catchError(error => {
          console.error('DepartmentService: Error fetching department-category mappings:', error);
          return throwError(() => new Error('Failed to fetch department-category mappings'));
        })
      );
  }

  /**
   * Save department-category mappings to database
   */
  saveDepartmentCategoryMappings(tenantId: string, mappings: DepartmentCategoryMapping[]): Observable<boolean> {
    console.log('DepartmentService: saveDepartmentCategoryMappings called with:', {
      tenantId,
      mappings,
      mappingsLength: mappings.length
    });

    const requestBody = {
      tenantId: tenantId,
      departmentCategoryMappings: mappings,
      categoryWorkareaMappings: [] // Will be updated separately
    };

    console.log('DepartmentService: Request body being sent:', requestBody);

    return this.http.post<any>(`${this.engineUrl}master_data/save-mapping-config`, requestBody)
      .pipe(
        map(response => response.success || false),
        catchError(error => {
          console.error('DepartmentService: Error saving department-category mappings:', error);
          return throwError(() => new Error('Failed to save department-category mappings'));
        })
      );
  }

  /**
   * Get categories mapped to a specific department
   */
  getCategoriesForDepartment(tenantId: string, departmentId: string): Observable<string[]> {
    return this.getDepartmentCategoryMappings(tenantId).pipe(
      map(mappings => {
        const mapping = mappings.find(m => m.departmentId === departmentId);
        return mapping ? mapping.categories : [];
      })
    );
  }

  /**
   * Get department for a specific category
   */
  getDepartmentForCategory(tenantId: string, category: string): Observable<string | null> {
    return this.getDepartmentCategoryMappings(tenantId).pipe(
      map(mappings => {
        const mapping = mappings.find(m => m.categories.includes(category));
        return mapping ? mapping.departmentId : null;
      })
    );
  }

  // ===== DEPARTMENT GROUP-CATEGORY MAPPING METHODS =====
  /**
   * Get department group-category mappings for a tenant
   */
  getDepartmentGroupCategoryMappings(tenantId: string): Observable<DepartmentGroupCategoryMapping[]> {
    return this.http.get<any>(`${this.engineUrl}master_data/get-mapping-config/${tenantId}`)
      .pipe(
        map(response => {
          if (response.success && response.data) {
            return response.data.departmentGroupCategoryMappings || [];
          }
          return [];
        }),
        catchError(error => {
          console.error('DepartmentService: Error fetching department group-category mappings:', error);
          return throwError(() => new Error('Failed to fetch department group-category mappings'));
        })
      );
  }

  getDepartmentGroupWorkareaMappings(tenantId: string): Observable<DepartmentGroupWorkareaMapping[]> {
    return this.http.get<any>(`${this.engineUrl}master_data/get-mapping-config/${tenantId}`)
      .pipe(
        map(response => {
          if (response.success && response.data) {
            return response.data.departmentGroupWorkareaMappings || [];
          }
          return [];
        }),
        catchError(error => {
          console.error('DepartmentService: Error fetching department group-workarea mappings:', error);
          return throwError(() => new Error('Failed to fetch department group-workarea mappings'));
        })
      );
  }

  /**
   * Save department group-category mappings to database
   */
  saveDepartmentGroupCategoryMappings(tenantId: string, mappings: DepartmentGroupCategoryMapping[]): Observable<boolean> {
    console.log('DepartmentService: saveDepartmentGroupCategoryMappings called with:', {
      tenantId,
      mappings,
      mappingsLength: mappings.length
    });

    // Get existing mapping config first
    return this.getMappingConfig(tenantId).pipe(
      switchMap(existingConfig => {
        const requestBody = {
          tenantId: tenantId,
          departmentGroups: existingConfig.departmentGroups || [],
          departmentCategoryMappings: existingConfig.departmentCategoryMappings || [],
          departmentGroupCategoryMappings: mappings,
          categoryWorkareaMappings: existingConfig.categoryWorkareaMappings || []
        };

        return this.http.post<any>(`${this.engineUrl}master_data/save-mapping-config`, requestBody)
          .pipe(
            map(response => response.success || false),
            catchError(error => {
              console.error('DepartmentService: Error saving department group-category mappings:', error);
              return throwError(() => new Error('Failed to save department group-category mappings'));
            })
          );
      })
    );
  }

  /**
   * Get categories mapped to a specific department group
   */
  getCategoriesForDepartmentGroup(tenantId: string, groupId: string): Observable<string[]> {
    return this.getDepartmentGroupCategoryMappings(tenantId).pipe(
      map(mappings => {
        const mapping = mappings.find(m => m.groupId === groupId);
        return mapping ? mapping.categories : [];
      })
    );
  }

  /**
   * Get department group for a specific category
   */
  getDepartmentGroupForCategory(tenantId: string, category: string): Observable<string | null> {
    return this.getDepartmentGroupCategoryMappings(tenantId).pipe(
      map(mappings => {
        const mapping = mappings.find(m => m.categories.includes(category));
        return mapping ? mapping.groupId : null;
      })
    );
  }

  // ===== COMPLETE MAPPING CONFIGURATION METHODS =====
  /**
   * Get complete mapping configuration (department-category + category-workarea)
   */
  getMappingConfig(tenantId: string): Observable<MappingConfig> {
    return this.http.get<any>(`${this.engineUrl}master_data/get-mapping-config/${tenantId}`)
      .pipe(
        map(response => {
          if (response.success && response.data) {
            return {
              departmentGroups: response.data.departmentGroups || [],
              departmentCategoryMappings: response.data.departmentCategoryMappings || [],
              departmentGroupCategoryMappings: response.data.departmentGroupCategoryMappings || [],
              departmentGroupWorkareaMappings: response.data.departmentGroupWorkareaMappings || [],
              categoryWorkareaMappings: response.data.categoryWorkareaMappings || [],
              lastUpdated: response.data.lastUpdated
            };
          }
          return {
            departmentGroups: [],
            departmentCategoryMappings: [],
            departmentGroupCategoryMappings: [],
            departmentGroupWorkareaMappings: [],
            categoryWorkareaMappings: [],
            lastUpdated: undefined
          };
        }),
        catchError(error => {
          console.error('DepartmentService: Error fetching mapping config:', error);
          return throwError(() => new Error('Failed to fetch mapping configuration'));
        })
      );
  }

  /**
   * Save group mappings in group-centric format (groups have both categories and work areas)
   */
  saveGroupCentricMappings(
    tenantId: string,
    groupMappings: DepartmentGroupCategoryMapping[]
  ): Observable<boolean> {
    // Group mappings already contain both categories and work areas at group level
    const requestBody = {
      tenantId: tenantId,
      groupMappings: groupMappings.map(mapping => ({
        groupId: mapping.groupId,
        groupName: mapping.groupName,
        categories: mapping.categories || [],
        workAreas: (mapping as any).workAreas || [] // Work areas are now at group level
      }))
    };

    console.log('DepartmentService: Sending group-centric mappings:', requestBody);

    return this.http.post<any>(`${this.engineUrl}master_data/save-group-mappings`, requestBody)
      .pipe(
        map(response => {
          console.log('DepartmentService: Group-centric API response:', response);
          return response.success || false;
        }),
        catchError(error => {
          console.error('DepartmentService: Error saving group-centric mappings:', error);
          return throwError(() => new Error('Failed to save group-centric mappings'));
        })
      );
  }

  /**
   * @deprecated Use saveGroupCentricMappings instead
   * Save group mappings with work areas in simplified format
   */
  saveGroupMappingsWithWorkAreas(
    tenantId: string,
    groupMappings: DepartmentGroupCategoryMapping[],
    categoryWorkareaMappings: CategoryWorkareaMapping[]
  ): Observable<boolean> {
    // For backward compatibility, convert to group-centric format
    const categoryToWorkAreas = new Map<string, string[]>();
    categoryWorkareaMappings.forEach(mapping => {
      categoryToWorkAreas.set(mapping.categoryName, mapping.workAreas);
    });

    // Convert to group-centric format
    const groupCentricMappings = groupMappings.map(mapping => {
      const categories = mapping.categories || [];
      const workAreasSet = new Set<string>();

      categories.forEach(category => {
        const workAreas = categoryToWorkAreas.get(category) || [];
        workAreas.forEach(wa => workAreasSet.add(wa));
      });

      return {
        ...mapping,
        workAreas: Array.from(workAreasSet)
      };
    });

    return this.saveGroupCentricMappings(tenantId, groupCentricMappings);
  }

  /**
   * Save complete mapping configuration (department-category + category-workarea)
   */
  saveMappingConfig(tenantId: string, config: MappingConfig): Observable<boolean> {
    const requestBody = {
      tenantId: tenantId,
      departmentGroups: config.departmentGroups || [],
      departmentCategoryMappings: config.departmentCategoryMappings,
      departmentGroupCategoryMappings: config.departmentGroupCategoryMappings || [],
      categoryWorkareaMappings: config.categoryWorkareaMappings
    };

    console.log('DepartmentService: Sending request to API:', requestBody);

    return this.http.post<any>(`${this.engineUrl}master_data/save-mapping-config`, requestBody)
      .pipe(
        map(response => {
          console.log('DepartmentService: API response:', response);
          return response.success || false;
        }),
        catchError(error => {
          console.error('DepartmentService: Error saving mapping config:', error);
          return throwError(() => new Error('Failed to save mapping configuration'));
        })
      );
  }

  /**
   * Clear mapping configuration for a tenant
   */
  clearMappingConfig(tenantId: string): Observable<boolean> {
    return this.http.delete<any>(`${this.engineUrl}master_data/clear-mapping-config/${tenantId}`)
      .pipe(
        map(response => response.success || false),
        catchError(error => {
          console.error('DepartmentService: Error clearing mapping config:', error);
          return throwError(() => new Error('Failed to clear mapping configuration'));
        })
      );
  }

  // ===== CATEGORY-WORKAREA MAPPING METHODS =====
  /**
   * Get category-workarea mappings for a tenant
   */
  getCategoryWorkareaMappings(tenantId: string): Observable<CategoryWorkareaMapping[]> {
    return this.getMappingConfig(tenantId).pipe(
      map(config => config.categoryWorkareaMappings)
    );
  }

  /**
   * Save category-workarea mappings (preserves department-category mappings)
   */
  saveCategoryWorkareaMappings(tenantId: string, mappings: CategoryWorkareaMapping[]): Observable<boolean> {
    console.log('DepartmentService: Saving category-workarea mappings for tenant:', tenantId);
    console.log('DepartmentService: Mappings to save:', mappings);

    return this.getMappingConfig(tenantId).pipe(
      switchMap((currentConfig: MappingConfig) => {
        console.log('DepartmentService: Current config:', currentConfig);

        const updatedConfig: MappingConfig = {
          departmentCategoryMappings: currentConfig.departmentCategoryMappings,
          categoryWorkareaMappings: mappings
        };

        console.log('DepartmentService: Updated config to save:', updatedConfig);
        return this.saveMappingConfig(tenantId, updatedConfig);
      })
    );
  }

  /**
   * Get workareas mapped to a specific category
   */
  getWorkareasForCategory(tenantId: string, categoryName: string): Observable<string[]> {
    return this.getCategoryWorkareaMappings(tenantId).pipe(
      map(mappings => {
        const mapping = mappings.find(m => m.categoryName === categoryName);
        return mapping ? mapping.workAreas : [];
      })
    );
  }

  /**
   * Get category for a specific workarea
   */
  getCategoryForWorkarea(tenantId: string, workarea: string): Observable<string | null> {
    return this.getCategoryWorkareaMappings(tenantId).pipe(
      map(mappings => {
        const mapping = mappings.find(m => m.workAreas.includes(workarea));
        return mapping ? mapping.categoryName : null;
      })
    );
  }

  // ===== UTILITY METHODS =====

  /**
   * Validate department-category mapping rules
   * - Single department can have multiple categories
   * - Single category can only be mapped to one department
   */
  validateMappings(mappings: DepartmentCategoryMapping[]): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    const categoryToDepartment = new Map<string, string>();

    for (const mapping of mappings) {
      for (const category of mapping.categories) {
        if (categoryToDepartment.has(category)) {
          const existingDept = categoryToDepartment.get(category);
          if (existingDept !== mapping.departmentId) {
            errors.push(`Category "${category}" is mapped to multiple departments: "${existingDept}" and "${mapping.departmentName}"`);
          }
        } else {
          categoryToDepartment.set(category, mapping.departmentId);
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
